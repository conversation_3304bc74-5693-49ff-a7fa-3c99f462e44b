<?xml version="1.0" encoding="UTF-8"?>
<ActionPlanTemplate xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>CP Collection</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>Low</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 3</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 3 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>CP Collection</name>
        <uniqueName>CP_Collection_2275b5b8_21da_11ed_94da_3109ff94397c</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Pre-NBAC</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>Low</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 3</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 3 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Pre-NBAC</name>
        <uniqueName>Pre_NBAC_e93556fe_21d9_11ed_94da_e54387adde75</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Credit</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>Low</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 3</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 3 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Credit</name>
        <uniqueName>Credit_01b74b6a_21da_11ed_94da_c3aed70af0c9</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Legal</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>Low</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 3</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 3 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Legal</name>
        <uniqueName>Legal_0ff0a741_21da_11ed_94da_5d11ad617dfd</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>NBAC</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>Low</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 3</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 3 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>NBAC</name>
        <uniqueName>NBAC_f50097f5_21d9_11ed_94da_77bed08e04fb</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Client Pitch &amp; Scenario Testing</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>Low</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 3</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 3 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Client Pitch &amp; Scenario Testing</name>
        <uniqueName>Client_Pitch_Scenario_Testing_db05bf1d_21d9_11ed_94da_812e48506c4a</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItemDependencies>
        <creationType>OnPreviousItemCompleted</creationType>
        <name>name</name>
        <previousTemplateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Credit</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>Low</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 3</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>false</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 3 - 0 +</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Credit</name>
            <uniqueName>Credit_01b74b6a_21da_11ed_94da_c3aed70af0c9</uniqueName>
        </previousTemplateItem>
        <templateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Legal</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>Low</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 3</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>false</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 3 - 0 +</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Legal</name>
            <uniqueName>Legal_0ff0a741_21da_11ed_94da_5d11ad617dfd</uniqueName>
        </templateItem>
    </actionPlanTemplateItemDependencies>
    <actionPlanTemplateItemDependencies>
        <creationType>OnPreviousItemCompleted</creationType>
        <name>name</name>
        <previousTemplateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Client Pitch &amp; Scenario Testing</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>Low</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 3</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>false</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 3 - 0 +</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Client Pitch &amp; Scenario Testing</name>
            <uniqueName>Client_Pitch_Scenario_Testing_db05bf1d_21d9_11ed_94da_812e48506c4a</uniqueName>
        </previousTemplateItem>
        <templateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Pre-NBAC</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>Low</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 3</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>false</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 3 - 0 +</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Pre-NBAC</name>
            <uniqueName>Pre_NBAC_e93556fe_21d9_11ed_94da_e54387adde75</uniqueName>
        </templateItem>
    </actionPlanTemplateItemDependencies>
    <actionPlanTemplateItemDependencies>
        <creationType>OnPreviousItemCompleted</creationType>
        <name>name</name>
        <previousTemplateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Pre-NBAC</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>Low</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 3</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>false</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 3 - 0 +</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Pre-NBAC</name>
            <uniqueName>Pre_NBAC_e93556fe_21d9_11ed_94da_e54387adde75</uniqueName>
        </previousTemplateItem>
        <templateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>NBAC</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>Low</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 3</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>false</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 3 - 0 +</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>NBAC</name>
            <uniqueName>NBAC_f50097f5_21d9_11ed_94da_77bed08e04fb</uniqueName>
        </templateItem>
    </actionPlanTemplateItemDependencies>
    <actionPlanTemplateItemDependencies>
        <creationType>OnPreviousItemCompleted</creationType>
        <name>name</name>
        <previousTemplateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>NBAC</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>Low</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 3</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>false</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 3 - 0 +</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>NBAC</name>
            <uniqueName>NBAC_f50097f5_21d9_11ed_94da_77bed08e04fb</uniqueName>
        </previousTemplateItem>
        <templateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Credit</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>Low</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 3</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>false</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 3 - 0 +</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Credit</name>
            <uniqueName>Credit_01b74b6a_21da_11ed_94da_c3aed70af0c9</uniqueName>
        </templateItem>
    </actionPlanTemplateItemDependencies>
    <isAdHocItemCreationEnabled>false</isAdHocItemCreationEnabled>
    <name>CIB IB Lending</name>
    <targetEntityType>Opportunity</targetEntityType>
    <uniqueName>CIB_IB_Lending_c4f20a9a_21d9_11ed_94da_473d3c0623c9</uniqueName>
</ActionPlanTemplate>
