<?xml version="1.0" encoding="UTF-8"?>
<ActionPlanTemplate xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>1.6 Revenue from interest / dividend income</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 6</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Does the company earn any interest and/or dividend income?</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 6 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>1.6 Revenue from interest / dividend income</name>
        <uniqueName>X1_6_Revenue_from_interest_dividend_income_cd3c59e2_ac57_11ed_8b9f_87c7cae79ed8</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>1.19 Directors / Key management</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 19</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>List of directors and key management staff, a brief description of their background, positions, qualifications, remuneration, benefits, pensions and service agreements, etc.</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 19 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>1.19 Directors / Key management</name>
        <uniqueName>X1_19_Directors_Key_management_45845289_ac59_11ed_8b9f_159c678682ca</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>1.18 Significant events</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 18</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Description of any significant events of the company since its setup (e.g. capital enlargement, change of business scope and change of shareholders).</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 18 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>1.18 Significant events</name>
        <uniqueName>X1_18_Significant_events_2ad370b2_ac59_11ed_8b9f_7bc3dfdcecf0</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>1.21 Data strategy</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 21</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Does the business have a data strategy, and if yes, what is it?</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 21 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>1.21 Data strategy</name>
        <uniqueName>X1_21_Data_strategy_74ddc0f8_ac59_11ed_8b9f_d94feb82ef70</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>1.11 Period trading</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 11</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Please confirm the period that the company has been trading</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 11 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>1.11 Period trading</name>
        <uniqueName>X1_11_Period_trading_69f09f70_ac58_11ed_8b9f_e11a3ba2c3be</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>1.23 Social Media</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 23</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Does the business have any social media accounts?</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 23 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>1.23 Social Media</name>
        <uniqueName>X1_23_Social_Media_9b13187e_ac59_11ed_8b9f_cf4c4da9f422</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>1.12 Accounting system</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 12</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Please confirm the accounting system used (e.g. SAGE, Pastel etc), if applicable</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 12 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>1.12 Accounting system</name>
        <uniqueName>X1_12_Accounting_system_7f0c8687_ac58_11ed_8b9f_3764d77b370e</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>1.22 Trade marks / Patents etc</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 22</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Does the business have Trade Marks, Patents, Designs, Domain Names, Know-How?</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 22 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>1.22 Trade marks / Patents etc</name>
        <uniqueName>X1_22_Trade_marks_Patents_etc_8bfffe9e_ac59_11ed_8b9f_4d8dc1fef1dc</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>1.10 Yearend Date</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 10</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Please confirm the company&apos;s financial year end</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 10 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>1.10 Yearend Date</name>
        <uniqueName>X1_10_Yearend_Date_476b7ec7_ac58_11ed_8b9f_8534ee2d23cd</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 1 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>1.1 Nature of the business</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 1</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Please provide a description of the nature of the business.</valueLiteral>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>1.1 Nature of the business</name>
        <uniqueName>X1_Nature_of_the_business_6863f76f_ac56_11ed_8b9f_5fe84e8d2a83</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>1.9 Start up funding</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 9</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Please elaborate how start up funding is obtained (issue of shares/grant funding/loans/donations)</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 9 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>1.9 Start up funding</name>
        <uniqueName>X1_9_Start_up_funding_28dfd2ab_ac58_11ed_8b9f_117db1295e11</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>1.20 Meeting minutes</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 20</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Minutes of meetings for  the past 3 years (board, audit etc).</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 20 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>1.20 Meeting minutes</name>
        <uniqueName>X1_20_Meeting_minutes_594c422d_ac59_11ed_8b9f_bb5ddc297a4d</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>1.13 Target segments</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 13</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Define the key target market(s) and market segmentation.</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 13 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>1.13 Target segments</name>
        <uniqueName>X1_13_Target_segments_9662a568_ac58_11ed_8b9f_d197c6c4b610</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>1.8 Legal entity organization chart</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 8</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Legal entity organisation chart, with an explanation of the locations and activities of major entities.</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 8 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>1.8 Legal entity organization chart</name>
        <uniqueName>X1_8_Legal_entity_organization_chart_10795535_ac58_11ed_8b9f_89548ecce9d3</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>1.4 Offices outside SA</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 4</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Please confirm whether the company has any offices outside South Africa or has staff who work outside South Africa.</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 4 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>1.4 Offices outside SA</name>
        <uniqueName>X1_4_Offices_outside_SA_73aca581_ac57_11ed_8b9f_c9e70f915768</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>1.5 Revenue from goods and services</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 5</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Do you earn revenue from the sale of goods and/or the rendering of services?</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 5 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>1.5 Revenue from goods and services</name>
        <uniqueName>X1_5_Revenue_from_goods_and_services_a4a56ba4_ac57_11ed_8b9f_1b268250acf6</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>1.14 Target market size</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 14</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Define the target market size and the business market share.</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 14 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>1.14 Target market size</name>
        <uniqueName>X1_14_Target_market_size_befb6865_ac58_11ed_8b9f_771307dc72df</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>1.17 Headcount / Salaries</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 17</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Headcount list with salaries for the past 3 financial year ends</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 17 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>1.17 Headcount / Salaries</name>
        <uniqueName>X1_17_Headcount_Salaries_0d65dec8_ac59_11ed_8b9f_cb598477ebbb</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>1.3 Cross border supplies</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 3</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Does the company make any cross border supplies?</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 3 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>1.3 Cross border supplies</name>
        <uniqueName>X1_3_Cross_border_supplies_462c64c8_ac57_11ed_8b9f_c18266ac3ec7</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>1.16 Emerging threats</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 16</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>What are the emerging threats and who are emerging competitors?</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 16 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>1.16 Emerging threats</name>
        <uniqueName>X1_16_Emerging_threats_ef17d7bb_ac58_11ed_8b9f_330037abd4ee</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>1.15 Competitors</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 15</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Define the current competitors and threats. Is there an updated  competitor analysis available?</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 15 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>1.15 Competitors</name>
        <uniqueName>X1_15_Competitors_da5b76de_ac58_11ed_8b9f_2d768698a8de</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>1.2 Countries operating in</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 2</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Please list the countries where you operate in</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 2 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>1.2 Countries operating in</name>
        <uniqueName>X1_2_Countries_operating_in_26df3d26_ac57_11ed_8b9f_51300891e46d</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>1.7 Company organogram</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 7</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Please provide a company organogram/company structure, indicating the shareholders and any subsidiaries/affiliates.</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 7 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>1.7 Company organogram</name>
        <uniqueName>X1_7_Company_organogram_e8c5e95b_ac57_11ed_8b9f_3f76e7bad77c</uniqueName>
    </actionPlanTemplateItem>
    <isAdHocItemCreationEnabled>true</isAdHocItemCreationEnabled>
    <name>SIA Partners - DD - General</name>
    <targetEntityType>Opportunity</targetEntityType>
    <uniqueName>SIA_Partners_DD_General_684e739c_ac56_11ed_8b9f_eba031ff7ba8</uniqueName>
</ActionPlanTemplate>
