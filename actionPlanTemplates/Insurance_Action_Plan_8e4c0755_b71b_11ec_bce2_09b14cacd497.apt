<?xml version="1.0" encoding="UTF-8"?>
<ActionPlanTemplate xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Underwriting information being collected</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 15</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Obtain Client and Risk information. Conduct Needs Analysis. Complete proposal forms, where required.</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 15 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Underwriting information being collected</name>
        <uniqueName>Underwriting_information_being_collected_b41e13fa_b71c_11ec_bce2_f15144db0ed3</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Awaiting Quotation from Insurance Product Suppliers</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 20</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Compile a Request for Quotation (Broking Notes). Send Request for Quotation/s to Insurance Product Suppliers.</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 20 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Awaiting Quotation from Insurance Product Suppliers</name>
        <uniqueName>Proposal_or_Quotation_sent_to_Client_and_aw_ff657889_b71c_11ec_bce2_c11c594880be</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Business Won / Loss</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 45</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Confirm Decision from Client.</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 45 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Business Won / Loss</name>
        <uniqueName>Business_Won_Loss_be6dbc47_b71d_11ec_bce2_69273f126562</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Proposal (or Quotation) sent to Client and awaiting Decision</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 40</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Review Quotation/s received. Compile Proposal for presentation to Client. Submit Proposal to Client.</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 40 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Proposal (or Quotation) sent to Client and awaiting Decision</name>
        <uniqueName>Proposal_or_Quotation_sent_to_Client_and_a56b7491_b71d_11ec_bce2_01ec34c3451f</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Opportunity identified with Client</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 5</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Engage with Client to confirm Opportunity. Send Disclosure notice for signature.</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 5 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Opportunity identified with Client</name>
        <uniqueName>Opportunity_identified_with_Client_7b1e8e8b_b71c_11ec_bce2_21c7bf01b0a7</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItemDependencies>
        <creationType>OnPreviousItemCompleted</creationType>
        <name>name</name>
        <previousTemplateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Opportunity identified with Client</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 5</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Description</name>
                <valueLiteral>Engage with Client to confirm Opportunity. Send Disclosure notice for signature.</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 5 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Opportunity identified with Client</name>
            <uniqueName>Opportunity_identified_with_Client_7b1e8e8b_b71c_11ec_bce2_21c7bf01b0a7</uniqueName>
        </previousTemplateItem>
        <templateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Underwriting information being collected</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 15</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Description</name>
                <valueLiteral>Obtain Client and Risk information. Conduct Needs Analysis. Complete proposal forms, where required.</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 15 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Underwriting information being collected</name>
            <uniqueName>Underwriting_information_being_collected_b41e13fa_b71c_11ec_bce2_f15144db0ed3</uniqueName>
        </templateItem>
    </actionPlanTemplateItemDependencies>
    <actionPlanTemplateItemDependencies>
        <creationType>OnPreviousItemCompleted</creationType>
        <name>name</name>
        <previousTemplateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Underwriting information being collected</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 15</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Description</name>
                <valueLiteral>Obtain Client and Risk information. Conduct Needs Analysis. Complete proposal forms, where required.</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 15 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Underwriting information being collected</name>
            <uniqueName>Underwriting_information_being_collected_b41e13fa_b71c_11ec_bce2_f15144db0ed3</uniqueName>
        </previousTemplateItem>
        <templateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Awaiting Quotation from Insurance Product Suppliers</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 20</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Description</name>
                <valueLiteral>Compile a Request for Quotation (Broking Notes). Send Request for Quotation/s to Insurance Product Suppliers.</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 20 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Awaiting Quotation from Insurance Product Suppliers</name>
            <uniqueName>Proposal_or_Quotation_sent_to_Client_and_aw_ff657889_b71c_11ec_bce2_c11c594880be</uniqueName>
        </templateItem>
    </actionPlanTemplateItemDependencies>
    <actionPlanTemplateItemDependencies>
        <creationType>OnPreviousItemCompleted</creationType>
        <name>name</name>
        <previousTemplateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Awaiting Quotation from Insurance Product Suppliers</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 20</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Description</name>
                <valueLiteral>Compile a Request for Quotation (Broking Notes). Send Request for Quotation/s to Insurance Product Suppliers.</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 20 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Awaiting Quotation from Insurance Product Suppliers</name>
            <uniqueName>Proposal_or_Quotation_sent_to_Client_and_aw_ff657889_b71c_11ec_bce2_c11c594880be</uniqueName>
        </previousTemplateItem>
        <templateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Proposal (or Quotation) sent to Client and awaiting Decision</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 40</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Description</name>
                <valueLiteral>Review Quotation/s received. Compile Proposal for presentation to Client. Submit Proposal to Client.</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 40 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Proposal (or Quotation) sent to Client and awaiting Decision</name>
            <uniqueName>Proposal_or_Quotation_sent_to_Client_and_a56b7491_b71d_11ec_bce2_01ec34c3451f</uniqueName>
        </templateItem>
    </actionPlanTemplateItemDependencies>
    <actionPlanTemplateItemDependencies>
        <creationType>OnPreviousItemCompleted</creationType>
        <name>name</name>
        <previousTemplateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Proposal (or Quotation) sent to Client and awaiting Decision</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 40</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Description</name>
                <valueLiteral>Review Quotation/s received. Compile Proposal for presentation to Client. Submit Proposal to Client.</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 40 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Proposal (or Quotation) sent to Client and awaiting Decision</name>
            <uniqueName>Proposal_or_Quotation_sent_to_Client_and_a56b7491_b71d_11ec_bce2_01ec34c3451f</uniqueName>
        </previousTemplateItem>
        <templateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Business Won / Loss</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 45</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Description</name>
                <valueLiteral>Confirm Decision from Client.</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 45 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Business Won / Loss</name>
            <uniqueName>Business_Won_Loss_be6dbc47_b71d_11ec_bce2_69273f126562</uniqueName>
        </templateItem>
    </actionPlanTemplateItemDependencies>
    <isAdHocItemCreationEnabled>false</isAdHocItemCreationEnabled>
    <name>Insurance Action Plan</name>
    <targetEntityType>Opportunity</targetEntityType>
    <uniqueName>Insurance_Action_Plan_8e4c0755_b71b_11ec_bce2_09b14cacd497</uniqueName>
</ActionPlanTemplate>
