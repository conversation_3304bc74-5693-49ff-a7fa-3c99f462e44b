<?xml version="1.0" encoding="UTF-8"?>
<ActionPlanTemplate xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Evaluate and Discuss of merger target</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>Low</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 3</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 3 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Evaluate and Discuss of merger target</name>
        <uniqueName>Evaluate_and_Discuss_of_merger_target_b4e49360_1ec6_11ed_984d_bd70510bb5c0</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Engage and advise company on BEE transaction</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>Low</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 3</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 3 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Engage and advise company on BEE transaction</name>
        <uniqueName>Engage_and_advise_company_on_BEE_transactio_37e0d4c8_1ec7_11ed_984d_b9590f2b611e</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Regulatory engagement</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>Low</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 3</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 3 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Regulatory engagement</name>
        <uniqueName>Regulatory_engagement_5ddfda24_1ec7_11ed_984d_cbb7ecd715c5</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Identify and engage merger target</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>Low</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 3</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 3 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Identify and engage merger target</name>
        <uniqueName>Identify_and_engage_merger_target_86151643_1ec6_11ed_984d_c38b53c5111c</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Send IM to interested buyers</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>Low</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 3</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 3 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Send IM to interested buyers</name>
        <uniqueName>Send_IM_to_interested_buyers_0bea8fc0_1ec7_11ed_984d_adb4f4b8bc77</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Formal offers received and accepted</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>Low</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 3</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 3 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Formal offers received and accepted</name>
        <uniqueName>Formal_offers_received_and_accepted_28c0ea0a_1ec7_11ed_984d_9f7300ab6a60</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Awaiting external approvals</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>Low</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 3</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 3 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Awaiting external approvals</name>
        <uniqueName>Awaiting_external_approvals_e006e738_1ec6_11ed_984d_7f9cbc28931a</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Funding confirmation</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>Low</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 3</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 3 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Funding confirmation</name>
        <uniqueName>Funding_confirmation_523148f8_1ec7_11ed_984d_6db75013666f</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Prepare company for sale</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>Low</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 3</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 3 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Prepare company for sale</name>
        <uniqueName>Prepare_company_for_sale_ff0db67d_1ec6_11ed_984d_8b248ed9b018</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Negotiate structure and pricing</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>Low</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 3</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 3 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Negotiate structure and pricing</name>
        <uniqueName>Negotiate_structure_and_pricing_456a8fec_1ec7_11ed_984d_81510677a69e</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Audit &amp; Execute transaction</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>Low</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 3</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 3 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Audit &amp; Execute transaction</name>
        <uniqueName>Audit_Execute_transaction_cf92b6d0_1ec6_11ed_984d_33e992795a97</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Shareholder engagement</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>Low</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 3</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 3 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Shareholder engagement</name>
        <uniqueName>Shareholder_engagement_efab6b96_1ec6_11ed_984d_c34ab7aa4041</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Expressions of interest received</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>Low</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 3</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 3 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Expressions of interest received</name>
        <uniqueName>Expressions_of_interest_received_1c666134_1ec7_11ed_984d_c98abc0cbc37</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Deal closed</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>Low</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 3</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 3 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Deal closed</name>
        <uniqueName>Deal_closed_7a84021f_1ec7_11ed_984d_0d26bda512c5</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Awaiting external approvals</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>Low</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 3</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 3 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Awaiting external approvals</name>
        <uniqueName>Awaiting_external_approvals_6863809b_1ec7_11ed_984d_d7159fc790a9</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItemDependencies>
        <creationType>OnPreviousItemCompleted</creationType>
        <name>name</name>
        <previousTemplateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Identify and engage merger target</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>Low</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 3</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>false</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 3 - 0 +</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Identify and engage merger target</name>
            <uniqueName>Identify_and_engage_merger_target_86151643_1ec6_11ed_984d_c38b53c5111c</uniqueName>
        </previousTemplateItem>
        <templateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Evaluate and Discuss of merger target</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>Low</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 3</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>false</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 3 - 0 +</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Evaluate and Discuss of merger target</name>
            <uniqueName>Evaluate_and_Discuss_of_merger_target_b4e49360_1ec6_11ed_984d_bd70510bb5c0</uniqueName>
        </templateItem>
    </actionPlanTemplateItemDependencies>
    <actionPlanTemplateItemDependencies>
        <creationType>OnPreviousItemCompleted</creationType>
        <name>name</name>
        <previousTemplateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Evaluate and Discuss of merger target</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>Low</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 3</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>false</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 3 - 0 +</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Evaluate and Discuss of merger target</name>
            <uniqueName>Evaluate_and_Discuss_of_merger_target_b4e49360_1ec6_11ed_984d_bd70510bb5c0</uniqueName>
        </previousTemplateItem>
        <templateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Audit &amp; Execute transaction</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>Low</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 3</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>false</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 3 - 0 +</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Audit &amp; Execute transaction</name>
            <uniqueName>Audit_Execute_transaction_cf92b6d0_1ec6_11ed_984d_33e992795a97</uniqueName>
        </templateItem>
    </actionPlanTemplateItemDependencies>
    <actionPlanTemplateItemDependencies>
        <creationType>OnPreviousItemCompleted</creationType>
        <name>name</name>
        <previousTemplateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Audit &amp; Execute transaction</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>Low</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 3</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>false</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 3 - 0 +</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Audit &amp; Execute transaction</name>
            <uniqueName>Audit_Execute_transaction_cf92b6d0_1ec6_11ed_984d_33e992795a97</uniqueName>
        </previousTemplateItem>
        <templateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Awaiting external approvals</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>Low</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 3</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>false</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 3 - 0 +</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Awaiting external approvals</name>
            <uniqueName>Awaiting_external_approvals_e006e738_1ec6_11ed_984d_7f9cbc28931a</uniqueName>
        </templateItem>
    </actionPlanTemplateItemDependencies>
    <actionPlanTemplateItemDependencies>
        <creationType>OnPreviousItemCompleted</creationType>
        <name>name</name>
        <previousTemplateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Awaiting external approvals</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>Low</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 3</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>false</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 3 - 0 +</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Awaiting external approvals</name>
            <uniqueName>Awaiting_external_approvals_e006e738_1ec6_11ed_984d_7f9cbc28931a</uniqueName>
        </previousTemplateItem>
        <templateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Shareholder engagement</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>Low</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 3</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>false</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 3 - 0 +</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Shareholder engagement</name>
            <uniqueName>Shareholder_engagement_efab6b96_1ec6_11ed_984d_c34ab7aa4041</uniqueName>
        </templateItem>
    </actionPlanTemplateItemDependencies>
    <isAdHocItemCreationEnabled>false</isAdHocItemCreationEnabled>
    <name>CIB IB Advisory - Buy Side,  Sell Side &amp; Merger</name>
    <targetEntityType>Opportunity</targetEntityType>
    <uniqueName>CIB_IB_Advisory_Buy_Side_Sell_Side_Me_3cf1ca56_1ec6_11ed_984d_cb528c4cea22</uniqueName>
</ActionPlanTemplate>
