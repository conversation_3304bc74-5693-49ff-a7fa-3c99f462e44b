<?xml version="1.0" encoding="UTF-8"?>
<ActionPlanTemplate xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Step 3: Compliance Control Room</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 7</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 7 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Submit to Compliance Control Room and receive notification of all member registered on the deal team.</valueLiteral>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Step 3: Compliance Control Room</name>
        <uniqueName>Step_3_Compliance_Control_Room_f27c30e4_dabe_11ed_91e1_b5d7532e0206</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Step 12: Conditions Precedent</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 240</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Close conditions precedent</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 240 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Step 12: Conditions Precedent</name>
        <uniqueName>Step_12_8fdee9ed_dac0_11ed_91e1_d3869b5452c6</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Step 8: Due Diligence</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 110</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Perform the due diligence</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 110 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Step 8: Due Diligence</name>
        <uniqueName>Due_Diligence_ff235ec7_dabf_11ed_91e1_4d37be6b9d2b</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Step 4: Non-binding term sheet/EOI/LOI</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 10</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Complete non-binding term sheet/EOI/LOI</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 10 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Step 4: Non-binding term sheet/EOI/LOI</name>
        <uniqueName>Step_4_Non_binding_term_sheet_EOI_LOI_40885b3b_dabf_11ed_91e1_259062a7fdce</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Step 13: Payment</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 245</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Execute payment</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 245 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Step 13: Payment</name>
        <uniqueName>Step_13_Payment_ad409430_dac0_11ed_91e1_7f578f417a4f</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Step 14: Post-closing Obligations</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 300</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Track post-closing obligations</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 300 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Step 14: Post-closing Obligations</name>
        <uniqueName>Step_14_Post_closing_Obligations_d4378edb_dac0_11ed_91e1_915c3a38e3a6</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Step 1: Enter into NDA with counterparty</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 5</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Complete NDA and submit to counter party for signature</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 5 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Step 1: Enter into NDA with counterparty</name>
        <uniqueName>Step_1_Enter_into_NDA_with_counterparty_327cbb4e_dabe_11ed_91e1_1dbc1a9326e1</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Step 9: Final Approval after Due Diligence</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 140</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Obtain final approval from the investment committee post the DD outcome</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 140 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Step 9: Final Approval after Due Diligence</name>
        <uniqueName>Step_9_Final_Approval_after_Due_Diligence_3a27b526_dac0_11ed_91e1_e5ae466c87cb</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Step 10: Legal Agreements</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 200</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Draft the relevant legal agreements for signature [done in parallel to DD]</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 200 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Step 10: Legal Agreements</name>
        <uniqueName>Step_10_Legal_Agreements_54d4ee85_dac0_11ed_91e1_630851f2a36d</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Step 15: Directors Appointment</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 360</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Appoint the relevant directors</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 360 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Step 15: Directors Appointment</name>
        <uniqueName>Step_15_Directors_Appointment_f5a96a8c_dac0_11ed_91e1_0187886618f4</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Step 5: Investment paper</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 30</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Draft an investment paper with the business sponsor</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 30 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Step 5: Investment paper</name>
        <uniqueName>Step_5_Investment_paper_791c74be_dabf_11ed_91e1_dd9b65ed915f</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Step 17: Ongoing Monitoring</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 500</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Ongoing Monitoring</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 500 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Step 17: Ongoing Monitoring</name>
        <uniqueName>Step_17_Ongoing_Monitoring_31320b92_dac1_11ed_91e1_ffd3269a177c</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Step 6: Investment Approval</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 40</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Obtain approval from the appropriate investment committee</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 40 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Step 6: Investment Approval</name>
        <uniqueName>Step_6_Investment_Approval_a64bafc0_dabf_11ed_91e1_a3fc90d92627</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Step 7: Certificate of Authorities</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 50</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Obtain the necessary CoAs</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 50 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Step 7: Certificate of Authorities</name>
        <uniqueName>Step_7_Certificate_of_Authorities_df8642a2_dabf_11ed_91e1_7ba96533a7e6</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Step 2: Conflict Clearance</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 6</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>All Partner Team Members to complete conflict clearance declarations.</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 6 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Step 2: Conflict Clearance</name>
        <uniqueName>Step_2_Compliance_Control_Room_Conflict_bd7cafad_dabe_11ed_91e1_7d726ee1a3fc</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Step 11: Agreement Signature</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 205</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Signing of agreements</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 205 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Step 11: Agreement Signature</name>
        <uniqueName>Step_11_Agreement_Signature_7160d21e_dac0_11ed_91e1_a3af3374ca32</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Step 16: Share Certificates</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 400</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Obtain share certificates</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 400 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Step 16: Share Certificates</name>
        <uniqueName>Step_16_Share_Certificates_11a08925_dac1_11ed_91e1_ed021d98b73e</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItemDependencies>
        <creationType>OnPreviousItemCompleted</creationType>
        <name>name</name>
        <previousTemplateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Step 4: Non-binding term sheet/EOI/LOI</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 10</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Description</name>
                <valueLiteral>Complete non-binding term sheet/EOI/LOI</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 10 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Step 4: Non-binding term sheet/EOI/LOI</name>
            <uniqueName>Step_4_Non_binding_term_sheet_EOI_LOI_40885b3b_dabf_11ed_91e1_259062a7fdce</uniqueName>
        </previousTemplateItem>
        <templateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Step 5: Investment paper</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 30</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Description</name>
                <valueLiteral>Draft an investment paper with the business sponsor</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 30 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Step 5: Investment paper</name>
            <uniqueName>Step_5_Investment_paper_791c74be_dabf_11ed_91e1_dd9b65ed915f</uniqueName>
        </templateItem>
    </actionPlanTemplateItemDependencies>
    <actionPlanTemplateItemDependencies>
        <creationType>OnPreviousItemCompleted</creationType>
        <name>name</name>
        <previousTemplateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Step 11: Agreement Signature</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 205</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Description</name>
                <valueLiteral>Signing of agreements</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 205 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Step 11: Agreement Signature</name>
            <uniqueName>Step_11_Agreement_Signature_7160d21e_dac0_11ed_91e1_a3af3374ca32</uniqueName>
        </previousTemplateItem>
        <templateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Step 12: Conditions Precedent</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 240</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Description</name>
                <valueLiteral>Close conditions precedent</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 240 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Step 12: Conditions Precedent</name>
            <uniqueName>Step_12_8fdee9ed_dac0_11ed_91e1_d3869b5452c6</uniqueName>
        </templateItem>
    </actionPlanTemplateItemDependencies>
    <actionPlanTemplateItemDependencies>
        <creationType>OnPreviousItemCompleted</creationType>
        <name>name</name>
        <previousTemplateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Step 7: Certificate of Authorities</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 50</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Description</name>
                <valueLiteral>Obtain the necessary CoAs</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 50 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Step 7: Certificate of Authorities</name>
            <uniqueName>Step_7_Certificate_of_Authorities_df8642a2_dabf_11ed_91e1_7ba96533a7e6</uniqueName>
        </previousTemplateItem>
        <templateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Step 8: Due Diligence</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 110</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Description</name>
                <valueLiteral>Perform the due diligence</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 110 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Step 8: Due Diligence</name>
            <uniqueName>Due_Diligence_ff235ec7_dabf_11ed_91e1_4d37be6b9d2b</uniqueName>
        </templateItem>
    </actionPlanTemplateItemDependencies>
    <actionPlanTemplateItemDependencies>
        <creationType>OnPreviousItemCompleted</creationType>
        <name>name</name>
        <previousTemplateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Step 14: Post-closing Obligations</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 300</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Description</name>
                <valueLiteral>Track post-closing obligations</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 300 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Step 14: Post-closing Obligations</name>
            <uniqueName>Step_14_Post_closing_Obligations_d4378edb_dac0_11ed_91e1_915c3a38e3a6</uniqueName>
        </previousTemplateItem>
        <templateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Step 15: Directors Appointment</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 360</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Description</name>
                <valueLiteral>Appoint the relevant directors</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 360 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Step 15: Directors Appointment</name>
            <uniqueName>Step_15_Directors_Appointment_f5a96a8c_dac0_11ed_91e1_0187886618f4</uniqueName>
        </templateItem>
    </actionPlanTemplateItemDependencies>
    <description>Due Diligence Checklist for new SIA investments</description>
    <isAdHocItemCreationEnabled>true</isAdHocItemCreationEnabled>
    <name>SIA Partners - Overall Deal Checklist</name>
    <targetEntityType>Opportunity</targetEntityType>
    <uniqueName>SIA_Partners_DD_Checklist_d4beee89_dabd_11ed_91e1_6d87f93cd9ce</uniqueName>
</ActionPlanTemplate>
