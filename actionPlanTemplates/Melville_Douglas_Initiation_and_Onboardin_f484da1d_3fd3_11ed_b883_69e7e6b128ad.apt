<?xml version="1.0" encoding="UTF-8"?>
<ActionPlanTemplate xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Ops Onboarding - Open Script Account</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 6</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 6 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Ops Onboarding - Open Script Account</name>
        <uniqueName>Ops_Onboarding_Open_Script_Account_3c9b1c69_3fd7_11ed_b883_b76384dc3b76</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>PM - Engage Client to position MD</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 1</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>On receipt of the client lead the Portfolio Manager will engage the prospective client to position the Melville Douglas proposition to determine whether the client</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 1 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>PM - Engage Client to position MD</name>
        <uniqueName>PM_Engage_Client_to_position_MD_b45cf220_3fd4_11ed_b883_678d7748e61e</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Ops Settlement - Adjust Book Cost on FPM</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 8</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 8 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Ops Settlement - Adjust Book Cost on FPM</name>
        <uniqueName>Ops_Settlement_Adjust_Book_Cost_on_FPM_4d966b78_3fd9_11ed_b883_91cfc199a7dc</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Ops Settlement - Perform Cash / Share Transfer</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 8</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 8 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Ops Settlement - Perform Cash / Share Transfer</name>
        <uniqueName>Ops_Settlement_Perform_Cash_Share_Trans_fd0e1de4_3fd8_11ed_b883_2b618cb1fb89</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>PM &amp; CST - Submission to MD BAF</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 4</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 4 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>PM &amp; CST - Submission to MD BAF</name>
        <uniqueName>PM_CST_Submission_to_MD_BAF_c3b11632_3fd5_11ed_b883_4dba15f0cc4b</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Ops Onboarding - Open Account on Baobab</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 6</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 6 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Ops Onboarding - Open Account on Baobab</name>
        <uniqueName>Ops_Onboarding_Open_Account_on_Baobab_6a3e7545_3fd7_11ed_b883_a7c73742132e</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>CST - Client Onboarding in Customer 1st</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 4</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 4 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>CST - Client Onboarding in Customer 1st</name>
        <uniqueName>CST_Client_Onboarding_in_Customer_1st_1a318f12_3fd6_11ed_b883_43accdf528ce</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Ops Onboarding - Onboarding Pack Received</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 5</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Ops Onboarding team to review the onboarding pack received for completeness.
Request additional documentation if incomplete.</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 5 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Ops Onboarding - Onboarding Pack Received</name>
        <uniqueName>Ops_Onboarding_Onboarding_Pack_Received_ff8ac8fc_3fd6_11ed_b883_4b2de0278ddd</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>PM &amp; CST - Client Onboarding Documentation</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 3</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 3 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>PM &amp; CST - Client Onboarding Documentation</name>
        <uniqueName>PM_CST_Client_Onboarding_Documentation_9f9af58d_3fd5_11ed_b883_2d37889957ff</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Ops Onboarding - Open Cash Account</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 6</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 6 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Ops Onboarding - Open Cash Account</name>
        <uniqueName>Ops_Onboarding_Open_Cash_Account_14e872ff_3fd7_11ed_b883_519aba32e60f</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Ops Onboarding - Open Account on FPM</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 6</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 6 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Ops Onboarding - Open Account on FPM</name>
        <uniqueName>Ops_Onboarding_Open_Account_on_FPM_50cca233_3fd7_11ed_b883_f34459e9f6a4</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>CST - Communication final onboarding details to Client</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 10</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 10 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>CST - Communication final onboarding details to Client</name>
        <uniqueName>CST_Communication_final_onboarding_detail_618a25e5_3fd9_11ed_b883_9f5a903396bb</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>MD BAF - Review submission</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 4</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 4 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>MD BAF - Review submission</name>
        <uniqueName>MD_BAF_Review_submission_ee315df4_3fd5_11ed_b883_8bb47e785803</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Ops Onboarding to submit to Ops Settlement</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 7</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Description</name>
            <valueLiteral>Ops Onboarding Team to notify Ops Settlement to complete settlement steps</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 7 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Ops Onboarding to submit to Ops Settlement</name>
        <uniqueName>Ops_Onboarding_to_submit_to_Ops_Settlement_e60ada42_3fd8_11ed_b883_9f4b76332d0b</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>Ops Settlement - Upload Shares on FPM</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 8</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 8 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>Ops Settlement - Upload Shares on FPM</name>
        <uniqueName>Ops_Settlement_Upload_Shares_on_FPM_0dc6f84a_3fd9_11ed_b883_a532f4925b47</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>PM &amp; CST - Onboarding Decision</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>High</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 2</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>true</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 2 - 0 + 08:00:00.000</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>PM &amp; CST - Onboarding Decision</name>
        <uniqueName>PM_CST_Onboarding_Decision_82751ad7_3fd5_11ed_b883_25dcb595c5f4</uniqueName>
    </actionPlanTemplateItem>
    <actionPlanTemplateItemDependencies>
        <creationType>OnPreviousItemCompleted</creationType>
        <name>name</name>
        <previousTemplateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>PM &amp; CST - Onboarding Decision</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 2</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 2 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>PM &amp; CST - Onboarding Decision</name>
            <uniqueName>PM_CST_Onboarding_Decision_82751ad7_3fd5_11ed_b883_25dcb595c5f4</uniqueName>
        </previousTemplateItem>
        <templateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>PM &amp; CST - Client Onboarding Documentation</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 3</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 3 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>PM &amp; CST - Client Onboarding Documentation</name>
            <uniqueName>PM_CST_Client_Onboarding_Documentation_9f9af58d_3fd5_11ed_b883_2d37889957ff</uniqueName>
        </templateItem>
    </actionPlanTemplateItemDependencies>
    <actionPlanTemplateItemDependencies>
        <creationType>OnPreviousItemCompleted</creationType>
        <name>name</name>
        <previousTemplateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>PM &amp; CST - Submission to MD BAF</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 4</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 4 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>PM &amp; CST - Submission to MD BAF</name>
            <uniqueName>PM_CST_Submission_to_MD_BAF_c3b11632_3fd5_11ed_b883_4dba15f0cc4b</uniqueName>
        </previousTemplateItem>
        <templateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Ops Onboarding - Onboarding Pack Received</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 5</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Description</name>
                <valueLiteral>Ops Onboarding team to review the onboarding pack received for completeness.
Request additional documentation if incomplete.</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 5 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Ops Onboarding - Onboarding Pack Received</name>
            <uniqueName>Ops_Onboarding_Onboarding_Pack_Received_ff8ac8fc_3fd6_11ed_b883_4b2de0278ddd</uniqueName>
        </templateItem>
    </actionPlanTemplateItemDependencies>
    <actionPlanTemplateItemDependencies>
        <creationType>OnPreviousItemCompleted</creationType>
        <name>name</name>
        <previousTemplateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>PM &amp; CST - Onboarding Decision</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 2</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 2 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>PM &amp; CST - Onboarding Decision</name>
            <uniqueName>PM_CST_Onboarding_Decision_82751ad7_3fd5_11ed_b883_25dcb595c5f4</uniqueName>
        </previousTemplateItem>
        <templateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>PM &amp; CST - Submission to MD BAF</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 4</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 4 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>PM &amp; CST - Submission to MD BAF</name>
            <uniqueName>PM_CST_Submission_to_MD_BAF_c3b11632_3fd5_11ed_b883_4dba15f0cc4b</uniqueName>
        </templateItem>
    </actionPlanTemplateItemDependencies>
    <actionPlanTemplateItemDependencies>
        <creationType>OnPreviousItemCompleted</creationType>
        <name>name</name>
        <previousTemplateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>PM &amp; CST - Onboarding Decision</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 2</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 2 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>PM &amp; CST - Onboarding Decision</name>
            <uniqueName>PM_CST_Onboarding_Decision_82751ad7_3fd5_11ed_b883_25dcb595c5f4</uniqueName>
        </previousTemplateItem>
        <templateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>MD BAF - Review submission</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 4</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 4 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>MD BAF - Review submission</name>
            <uniqueName>MD_BAF_Review_submission_ee315df4_3fd5_11ed_b883_8bb47e785803</uniqueName>
        </templateItem>
    </actionPlanTemplateItemDependencies>
    <actionPlanTemplateItemDependencies>
        <creationType>OnPreviousItemCompleted</creationType>
        <name>name</name>
        <previousTemplateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>PM &amp; CST - Submission to MD BAF</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 4</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 4 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>PM &amp; CST - Submission to MD BAF</name>
            <uniqueName>PM_CST_Submission_to_MD_BAF_c3b11632_3fd5_11ed_b883_4dba15f0cc4b</uniqueName>
        </previousTemplateItem>
        <templateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>CST - Client Onboarding in Customer 1st</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 4</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 4 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>CST - Client Onboarding in Customer 1st</name>
            <uniqueName>CST_Client_Onboarding_in_Customer_1st_1a318f12_3fd6_11ed_b883_43accdf528ce</uniqueName>
        </templateItem>
    </actionPlanTemplateItemDependencies>
    <actionPlanTemplateItemDependencies>
        <creationType>OnPreviousItemCompleted</creationType>
        <name>name</name>
        <previousTemplateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Ops Onboarding - Onboarding Pack Received</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 5</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Description</name>
                <valueLiteral>Ops Onboarding team to review the onboarding pack received for completeness.
Request additional documentation if incomplete.</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 5 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Ops Onboarding - Onboarding Pack Received</name>
            <uniqueName>Ops_Onboarding_Onboarding_Pack_Received_ff8ac8fc_3fd6_11ed_b883_4b2de0278ddd</uniqueName>
        </previousTemplateItem>
        <templateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Ops Onboarding - Open Cash Account</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 6</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 6 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Ops Onboarding - Open Cash Account</name>
            <uniqueName>Ops_Onboarding_Open_Cash_Account_14e872ff_3fd7_11ed_b883_519aba32e60f</uniqueName>
        </templateItem>
    </actionPlanTemplateItemDependencies>
    <actionPlanTemplateItemDependencies>
        <creationType>OnPreviousItemCompleted</creationType>
        <name>name</name>
        <previousTemplateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Ops Onboarding - Onboarding Pack Received</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 5</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Description</name>
                <valueLiteral>Ops Onboarding team to review the onboarding pack received for completeness.
Request additional documentation if incomplete.</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 5 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Ops Onboarding - Onboarding Pack Received</name>
            <uniqueName>Ops_Onboarding_Onboarding_Pack_Received_ff8ac8fc_3fd6_11ed_b883_4b2de0278ddd</uniqueName>
        </previousTemplateItem>
        <templateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Ops Onboarding - Open Script Account</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 6</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 6 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Ops Onboarding - Open Script Account</name>
            <uniqueName>Ops_Onboarding_Open_Script_Account_3c9b1c69_3fd7_11ed_b883_b76384dc3b76</uniqueName>
        </templateItem>
    </actionPlanTemplateItemDependencies>
    <actionPlanTemplateItemDependencies>
        <creationType>OnPreviousItemCompleted</creationType>
        <name>name</name>
        <previousTemplateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Ops Onboarding to submit to Ops Settlement</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 7</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Description</name>
                <valueLiteral>Ops Onboarding Team to notify Ops Settlement to complete settlement steps</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 7 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Ops Onboarding to submit to Ops Settlement</name>
            <uniqueName>Ops_Onboarding_to_submit_to_Ops_Settlement_e60ada42_3fd8_11ed_b883_9f4b76332d0b</uniqueName>
        </previousTemplateItem>
        <templateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Ops Settlement - Adjust Book Cost on FPM</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 8</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 8 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Ops Settlement - Adjust Book Cost on FPM</name>
            <uniqueName>Ops_Settlement_Adjust_Book_Cost_on_FPM_4d966b78_3fd9_11ed_b883_91cfc199a7dc</uniqueName>
        </templateItem>
    </actionPlanTemplateItemDependencies>
    <actionPlanTemplateItemDependencies>
        <creationType>OnPreviousItemCompleted</creationType>
        <name>name</name>
        <previousTemplateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Ops Onboarding - Onboarding Pack Received</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 5</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Description</name>
                <valueLiteral>Ops Onboarding team to review the onboarding pack received for completeness.
Request additional documentation if incomplete.</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 5 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Ops Onboarding - Onboarding Pack Received</name>
            <uniqueName>Ops_Onboarding_Onboarding_Pack_Received_ff8ac8fc_3fd6_11ed_b883_4b2de0278ddd</uniqueName>
        </previousTemplateItem>
        <templateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Ops Onboarding - Open Account on FPM</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 6</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 6 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Ops Onboarding - Open Account on FPM</name>
            <uniqueName>Ops_Onboarding_Open_Account_on_FPM_50cca233_3fd7_11ed_b883_f34459e9f6a4</uniqueName>
        </templateItem>
    </actionPlanTemplateItemDependencies>
    <actionPlanTemplateItemDependencies>
        <creationType>OnPreviousItemCompleted</creationType>
        <name>name</name>
        <previousTemplateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Ops Onboarding - Onboarding Pack Received</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 5</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Description</name>
                <valueLiteral>Ops Onboarding team to review the onboarding pack received for completeness.
Request additional documentation if incomplete.</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 5 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Ops Onboarding - Onboarding Pack Received</name>
            <uniqueName>Ops_Onboarding_Onboarding_Pack_Received_ff8ac8fc_3fd6_11ed_b883_4b2de0278ddd</uniqueName>
        </previousTemplateItem>
        <templateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Ops Onboarding - Open Account on Baobab</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 6</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 6 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Ops Onboarding - Open Account on Baobab</name>
            <uniqueName>Ops_Onboarding_Open_Account_on_Baobab_6a3e7545_3fd7_11ed_b883_a7c73742132e</uniqueName>
        </templateItem>
    </actionPlanTemplateItemDependencies>
    <actionPlanTemplateItemDependencies>
        <creationType>OnPreviousItemCompleted</creationType>
        <name>name</name>
        <previousTemplateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Ops Onboarding - Onboarding Pack Received</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 5</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Description</name>
                <valueLiteral>Ops Onboarding team to review the onboarding pack received for completeness.
Request additional documentation if incomplete.</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 5 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Ops Onboarding - Onboarding Pack Received</name>
            <uniqueName>Ops_Onboarding_Onboarding_Pack_Received_ff8ac8fc_3fd6_11ed_b883_4b2de0278ddd</uniqueName>
        </previousTemplateItem>
        <templateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Ops Onboarding to submit to Ops Settlement</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 7</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Description</name>
                <valueLiteral>Ops Onboarding Team to notify Ops Settlement to complete settlement steps</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 7 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Ops Onboarding to submit to Ops Settlement</name>
            <uniqueName>Ops_Onboarding_to_submit_to_Ops_Settlement_e60ada42_3fd8_11ed_b883_9f4b76332d0b</uniqueName>
        </templateItem>
    </actionPlanTemplateItemDependencies>
    <actionPlanTemplateItemDependencies>
        <creationType>OnPreviousItemCompleted</creationType>
        <name>name</name>
        <previousTemplateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Ops Onboarding to submit to Ops Settlement</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 7</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Description</name>
                <valueLiteral>Ops Onboarding Team to notify Ops Settlement to complete settlement steps</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 7 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Ops Onboarding to submit to Ops Settlement</name>
            <uniqueName>Ops_Onboarding_to_submit_to_Ops_Settlement_e60ada42_3fd8_11ed_b883_9f4b76332d0b</uniqueName>
        </previousTemplateItem>
        <templateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>CST - Communication final onboarding details to Client</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 10</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 10 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>CST - Communication final onboarding details to Client</name>
            <uniqueName>CST_Communication_final_onboarding_detail_618a25e5_3fd9_11ed_b883_9f5a903396bb</uniqueName>
        </templateItem>
    </actionPlanTemplateItemDependencies>
    <actionPlanTemplateItemDependencies>
        <creationType>OnPreviousItemCompleted</creationType>
        <name>name</name>
        <previousTemplateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Ops Onboarding to submit to Ops Settlement</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 7</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Description</name>
                <valueLiteral>Ops Onboarding Team to notify Ops Settlement to complete settlement steps</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 7 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Ops Onboarding to submit to Ops Settlement</name>
            <uniqueName>Ops_Onboarding_to_submit_to_Ops_Settlement_e60ada42_3fd8_11ed_b883_9f4b76332d0b</uniqueName>
        </previousTemplateItem>
        <templateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Ops Settlement - Perform Cash / Share Transfer</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 8</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 8 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Ops Settlement - Perform Cash / Share Transfer</name>
            <uniqueName>Ops_Settlement_Perform_Cash_Share_Trans_fd0e1de4_3fd8_11ed_b883_2b618cb1fb89</uniqueName>
        </templateItem>
    </actionPlanTemplateItemDependencies>
    <actionPlanTemplateItemDependencies>
        <creationType>OnPreviousItemCompleted</creationType>
        <name>name</name>
        <previousTemplateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Ops Onboarding to submit to Ops Settlement</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 7</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Description</name>
                <valueLiteral>Ops Onboarding Team to notify Ops Settlement to complete settlement steps</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 7 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Ops Onboarding to submit to Ops Settlement</name>
            <uniqueName>Ops_Onboarding_to_submit_to_Ops_Settlement_e60ada42_3fd8_11ed_b883_9f4b76332d0b</uniqueName>
        </previousTemplateItem>
        <templateItem>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Subject</name>
                <valueLiteral>Ops Settlement - Upload Shares on FPM</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>Priority</name>
                <valueLiteral>High</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ActivityDate</name>
                <valueFormula>StartDate + 8</valueFormula>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>IsReminderSet</name>
                <valueLiteral>true</valueLiteral>
            </actionPlanTemplateItemValue>
            <actionPlanTemplateItemValue>
                <itemEntityType>Task</itemEntityType>
                <name>ReminderDateTime</name>
                <valueFormula>StartDate + 8 - 0 + 08:00:00.000</valueFormula>
            </actionPlanTemplateItemValue>
            <isRequired>true</isRequired>
            <itemEntityType>Task</itemEntityType>
            <name>Ops Settlement - Upload Shares on FPM</name>
            <uniqueName>Ops_Settlement_Upload_Shares_on_FPM_0dc6f84a_3fd9_11ed_b883_a532f4925b47</uniqueName>
        </templateItem>
    </actionPlanTemplateItemDependencies>
    <description>Client Initiation and Onboarding action steps for Melville Douglas investment portfolio management clients</description>
    <isAdHocItemCreationEnabled>true</isAdHocItemCreationEnabled>
    <name>Melville Douglas - Initiation and Onboarding</name>
    <targetEntityType>Opportunity</targetEntityType>
    <uniqueName>Melville_Douglas_Initiation_and_Onboardin_f484da1d_3fd3_11ed_b883_69e7e6b128ad</uniqueName>
</ActionPlanTemplate>
