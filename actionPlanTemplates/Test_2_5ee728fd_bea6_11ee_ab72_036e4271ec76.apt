<?xml version="1.0" encoding="UTF-8"?>
<ActionPlanTemplate xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionPlanTemplateItem>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Subject</name>
            <valueLiteral>action 1</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>Priority</name>
            <valueLiteral>Low</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ActivityDate</name>
            <valueFormula>StartDate + 78</valueFormula>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>OwnerId</name>
            <valueLiteral>0056M00000AqKxEQAV</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>IsReminderSet</name>
            <valueLiteral>false</valueLiteral>
        </actionPlanTemplateItemValue>
        <actionPlanTemplateItemValue>
            <itemEntityType>Task</itemEntityType>
            <name>ReminderDateTime</name>
            <valueFormula>StartDate + 78 - 0 +</valueFormula>
        </actionPlanTemplateItemValue>
        <isRequired>true</isRequired>
        <itemEntityType>Task</itemEntityType>
        <name>action 1</name>
        <uniqueName>action_1_80af3afc_bea6_11ee_b4f0_6d3938d9d657</uniqueName>
    </actionPlanTemplateItem>
    <isAdHocItemCreationEnabled>true</isAdHocItemCreationEnabled>
    <name>Test 2</name>
    <targetEntityType>PP_PartnerApplication__c</targetEntityType>
    <uniqueName>Test_2_5ee728fd_bea6_11ee_ab72_036e4271ec76</uniqueName>
</ActionPlanTemplate>
