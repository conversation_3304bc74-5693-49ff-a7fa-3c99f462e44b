({
     handleNavigate : function(component, event, helper) {
        event.preventDefault();
        helper.navigateToHomePage(component);
    },
 	onInit: function(component, event, helper) {
        helper.initializeUserData(component);
        helper.setupPathValidation(component);
        helper.initializeHomepage(component);
        helper.setupEventListeners(component);
        helper.setupResponsiveLayout(component);
    },
    
  navigateToMenu: function(component, event, helper)
  {
    let savedDataLayer = Object.assign({}, window.savedDataLayer);
    $A.util.removeClass(component.find("submenuid"), "show-sub-menu");
    $A.util.removeClass(component.find("rotateicon"), "down");
    $A.util.removeClass(component.find("mobilenavid"), "show-overlay");
    $A.util.removeClass(component.find("naviconid"), "show-navicon");

    let id = event.target.dataset.menuItemId;
    if(id)
    {
      component.getSuper().navigate(id);     
      component.find('analyticsintegration').fireDirectAnalyticsInteraction(event, savedDataLayer);
    }
    
  },    
  handleMobileMenuClick : function(component, event, helper)
  {
    
    $A.util.addClass(component.find("hamburgerid"), "show");
    $A.util.toggleClass(component.find("mobilenavid"), "show-overlay");
    $A.util.toggleClass(component.find("naviconid"), "show-navicon");
    
  },
  showSearch: function(component, event, helper)
  {
    component.set('v.isSearchBoxVisible', true);
    $A.util.toggleClass(component.find("showsearchbar"), "slds-hide");

  },  
  searchKeyCheck : function(component, event, helper)
  {
    if (event.which == 13){
      component.set('v.isSearchBoxVisible', false);
      $A.util.toggleClass(component.find("showsearchbar"), "slds-hide");
    }    
  },
  closeSearch: function(component, event, helper)
  {
    component.set('v.isSearchBoxVisible', false);
    $A.util.toggleClass(component.find("showsearchbar"), "slds-hide");
  },
  showSubMenu: function(component)
  {
      $A.util.toggleClass(component.find("submenuid"), "show-sub-menu");
      $A.util.toggleClass(component.find("rotateicon"), "down");
  },
  navigateToCountries: function(component)
  {     
	  let urlEvent = $A.get("e.force:navigateToURL");
	  urlEvent.setParams({
		  "url": "/countries"
	  });
	  urlEvent.fire();
  },

  handlefireDirectAnalyticsInteraction: function(component, event, helper)
  {
    component.find('analyticsintegration').fireDirectAnalyticsInteraction(event,window.savedDataLayer);
  }
})