<!--
  @description       : TO show custom header items 
  <AUTHOR> <PERSON><PERSON><PERSON>
  @last modified on  : 27-05-2022
-->
<aura:component extends="forceCommunity:navigationMenuBase" implements="forceCommunity:availableForAllPageTypes"
    access="global" description="Navigation Menu Component" controller = "ACM_GetLoggedInUserInfo">

    <aura:attribute name="navigationLinkSetId" type="String"  />
    <aura:attribute name="showProfileMenu" type="Boolean" default="true" access="private" />
    <aura:attribute name="hideCountrySelection" type="Boolean" default="false" access="public" />
    <aura:attribute name="homepage" type="String" default="" access="private" />
    <aura:attribute name="isSearchBoxVisible" type="Boolean" default="false" access="private" />
    <aura:attribute name="profileMenu" type="Aura.Component[]" required="false"/>
    <aura:attribute name="search" type="Aura.Component[]" required="false" access="public" />
    <aura:attribute name="apiCountryUrl" type="String" default="/countries" access="public" />
    <aura:attribute name="adobeAnalyticsUrl" type="String" default="https://assets.adobedtm.com/45b28ee1a4af/49c2f428e3e9/launch-cb5dac3d7cfe-development.min.js" access="public" />
    <aura:attribute name="userAlias" type="String"/>
    <aura:attribute name="ispathExternal" type="Boolean" default = "true"/>
    
    <aura:handler name="init" value="{!this}" action="{!c.onInit}" />
    <c:acm_AdobeAnalytics aura:id="analyticsintegration" adobeAnalyticsUrl="{!v.adobeAnalyticsUrl}" class="adobeClass"></c:acm_AdobeAnalytics>	

    <div class="acm-header-container">
        <!-- * Start - Region Section -->
        <div class="acm-region-container">
            <aura:if isTrue="{!v.ispathExternal}">
                <div class="apicountryRegion">
                    <a class="api-countries" 
                    onclick="{!c.navigateToCountries}" 
                    data-intent="informational" 
                    data-scope="ACM Navigation Menu" 
                    data-id="link_content" 
                    data-text="API Countries">
                        <img width="auto" height="30px" alt="img" src="/sfsites/c/file-asset/globeiconpng?v=1" />
                        <span>API countries</span> 
                    </a>
                </div>
            </aura:if>
        </div>
        <!-- * End - Region Section -->
        <div class="acm-header slds-p-horizontal_medium slds-p-vertical_none">
            <div class="acm-mobile-top__content">
                <div class="acm-header-logo-nav">
                    <a href="javascript:void(0);" aura:id="naviconid" class="navicon" onclick="{!c.handleMobileMenuClick}">
                        <span></span>
                        <span></span>
                        <span></span>
                    </a>
                    <!-- * Start - Logo -->
                      <a class="sbglogo" onclick="{!c.handleNavigate}" data-id="header_nav_link" data-text="Header | Logo">
                         
                         <img 
                                    src="/sfsites/c/resource/sbdsAssets/Icons/LOGO'S/SBLogoWhiteTrading.svg"
                                    alt="SBG Logo"
                                    width="100%"
                                    height="100%"
                                />             
                     </a>
                    <!-- * End - Logo -->

                    <!-- * Start - Navigation -->
                    <div aura:id="mobilenavid" class="mobileNavOverlay">
                        <nav class="acm-navbar" aura:id="hamburgerid">
                            <ul class="nav-links">
                                <aura:if isTrue="{!!v.isSearchBoxVisible}">
                                    <aura:iteration items="{!v.menuItems}" var="item" >
                                        <li class="{! equals(item.active, true) ? 'menu-items-active' : ''}" >
                                            <!-- if the label has a submenu, this will render -->
                                            <aura:if isTrue="{!item.subMenu}">
                                                <a 
                                                    href="javascript:void(0);" 
                                                    class="slds-button showSubMenu" 
                                                    onclick="{! c.showSubMenu }">
                                                        {!item.label}
                                                        <lightning:icon 
                                                            aura:id="rotateicon" 
                                                            iconName="utility:chevrondown" 
                                                            iconPosition="right" 
                                                            size="xx-small" 
                                                            class="rotate"/>
                                                </a>
                                                <ul aura:id="submenuid" class="acm-submenu" >
                                                    <aura:iteration items="{!item.subMenu}" var="subItem">
                                                        <li class="{!equals(item.active, true) ? 'menu-items-active' : ''}">
                                                            <a 
                                                                class="slds-truncate slds-p-around_large" 
                                                                data-menu-item-id="{!subItem.id}" 
                                                                href="javascript:void(0);" 
                                                                data-intent="navigational" 
                                                                data-scope="ACM Navigation Menu" 
                                                                data-id="link_content" 
                                                                data-text="{!item.label}" 
                                                                onclick="{!c.navigateToMenu}">
                                                                    {!subItem.label}
                                                            </a>
                                                        </li>
                                                    </aura:iteration>
                                                </ul>
                                                <!-- This will render if it does not have a submenu -->
                                                <aura:set attribute="else">
                                                    <a class="slds-truncate  slds-p-horizontal_medium slds-p-vertical_none" 
                                                        href="javascript:void(0);" 
                                                        data-menu-item-id="{!item.id}" 
                                                        data-intent="navigational" 
                                                        data-scope="ACM Navigation Menu" 
                                                        data-id="link_content" 
                                                        data-text="{!item.label}" 
                                                        onclick="{!c.navigateToMenu}">
                                                        <span class="nav-menu-label">{!item.label}</span>
                                                    </a>
                                                </aura:set>
                                            </aura:if>
                                            
                                        </li>
                                    </aura:iteration>
                                </aura:if>
                            </ul>
                        </nav>
                    </div>
                    <!-- * End - Navigation -->
                    
                </div>

                <div class="acm-header-right__content">

                    <!-- * Start - Notification Button -->
                    <div class="padding-horizontal notifications" >
                        <forceCommunity:notifications/>
                    </div>
                    <!-- * End - Notification Button -->

                    <!-- * Start - Search Button -->
                    <div class="searchRegion">
                        <span aura:id="showsearchbar" class="search-icon">
                            <lightning:buttonIcon 
                            iconName="utility:search" 
                            variant="bare" 
                            iconClass="utilityicon" 
                            onclick="{! c.showSearch }" 
                            alternativeText="Expand search" 
                            tooltip="Expand search" />
                        </span>
                        <aura:if isTrue="{!v.isSearchBoxVisible}">
                            <span class="searchRegion" onkeypress="{!c.searchKeyCheck}">
                                {!v.search}
                            </span>
                            <span >
                                <lightning:buttonIcon iconName="utility:close" variant="bare" iconClass="utilityicon cross" onclick="{! c.closeSearch }" alternativeText="Close Search" tooltip="Close Search" />
                            </span>
                        </aura:if>
                    </div>
                    <!-- * End - Search Button-->
                    
                    <!-- * Start - Profile Section  -->
                   <div class="profileMenuRegion">
                        <span>{!v.userAlias} {!v.profileMenu}</span>                        
                    </div> 
                    <!-- * End - Profile Section -->
                    
                    
                </div>
            </div>

        </div>
    </div>
</aura:component>