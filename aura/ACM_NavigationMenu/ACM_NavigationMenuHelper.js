({
     navigateToHomePage : function(component) {
        let urlEvent = $A.get("e.force:navigateToURL");
        let homepage = component.get("v.homepage") ;
        
        urlEvent.setParams({
            "url": homepage
        });
        urlEvent.fire();
    },
    
    // Helper methods
    initializeUserData: function(component) {
        const action = component.get("c.getCurrentUserAlias");
        action.setCallback(this, function(response) {
            const state = response.getState();
            if (state === "SUCCESS") {
                component.set("v.userAlias", response.getReturnValue());
            } else {
                console.error('Failed to fetch user alias');
            }
        });
        $A.enqueueAction(action);
    },
    
    setupPathValidation: function(component) {
        try {
            const pathName = this.getSecurePath();
            if (pathName && pathName.includes('internalapimarketplace')) {
                component.set("v.ispathExternal", false);
            }
        } catch (error) {
            console.error('Error in path validation:', error);
            // Set default secure state
            component.set("v.ispathExternal", true);
        }
    },
    
    getSecurePath: function() {
        // Sanitize and validate path
        const path = window.location.pathname;
        return path;
    },
    
   
    
    initializeHomepage: function(component) {
        try {
            const url = new URL(window.location.href);
            const baseUrl = url.href.split('/s/')[0] + '/s/';
            component.set("v.homepage", baseUrl);
        } catch (error) {
            console.error('Error initializing homepage:', error);
            // Set fallback URL if needed
            component.set("v.homepage", '/');
        }
    },
    
    setupEventListeners: function(component) {
        // Debounced click handler
        const clickHandler = this.debounce((event) => {
            if (event.target && !event.target.matches('.showSubMenu')) {
                this.handleSubmenuClose(component);
            }
        }, 100);
        
        // Add event listener with proper binding
        if (typeof window !== 'undefined') {
            window.removeEventListener('click', clickHandler); // Clean up existing
            window.addEventListener('click', clickHandler);
            
            // Store reference for cleanup
            component.set("v.clickHandler", clickHandler);
        }
    },
    
    handleSubmenuClose: function(component) {
        $A.getCallback(() => {
            try {
                const dropdownCmp = component.find("submenuid");
                const dropdownIconCmp = component.find("rotateicon");
                
                if (dropdownCmp && $A.util.hasClass(dropdownCmp, "show-sub-menu")) {
                    $A.util.removeClass(dropdownCmp, "show-sub-menu");
                }
                
                if (dropdownIconCmp && $A.util.hasClass(dropdownIconCmp, "down")) {
                    $A.util.removeClass(dropdownIconCmp, "down");
                }
            } catch (error) {
                console.error('Error closing submenu:', error);
            }
        })();
    },
    
    setupResponsiveLayout: function(component) {
        try {
            const media = window.matchMedia('(min-width: 1025px)');
            component.set("v.showProfileMenu", media.matches);
            
            // Debounced resize handler
            const resizeHandler = this.debounce(() => {
                component.set("v.showProfileMenu", media.matches);
            }, 250);
            
            // Add event listener with proper binding
            if (typeof window !== 'undefined') {
                window.removeEventListener('resize', resizeHandler); // Clean up existing
                window.addEventListener('resize', resizeHandler);
                
                // Store reference for cleanup
                component.set("v.resizeHandler", resizeHandler);
            }
        } catch (error) {
            console.error('Error setting up responsive layout:', error);
            // Set default state
            component.set("v.showProfileMenu", true);
        }
    },
    
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    destroy: function(component) {
        // Cleanup event listeners
        if (typeof window !== 'undefined') {
            const clickHandler = component.get("v.clickHandler");
            const resizeHandler = component.get("v.resizeHandler");
            
            if (clickHandler) {
                window.removeEventListener('click', clickHandler);
            }
            if (resizeHandler) {
                window.removeEventListener('resize', resizeHandler);
            }
        }
    }

    
})