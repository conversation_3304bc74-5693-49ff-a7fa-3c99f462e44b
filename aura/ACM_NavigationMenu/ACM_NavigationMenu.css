/*
  Last Revised by <PERSON> of Smartenup, March 2024

  This file uses the extension 'Better Comments' by <PERSON>
  to better label the separate sections of code.
*/

.THIS {
  background-color: var(--sbds-secondary-color-action) !important;
}
        .THIS .profile .uiMenuItem{
            display:None;      
        }
        .THIS .trigger-link img.profileIcon {
            display:block !important;
        }
  
.THIS .src-components-commons-searchBox-searchBox__cross--xYASw{
  position: absolute;
  right: 13px;
  top: 0px;
  cursor: pointer;
  border: none;
  background-color: transparent;
  padding: 0;
  font-size: 13px;
  height: 100%;
}

.THIS .src-components-acmSearch-acmSearch__searchBoxInput--35u-s {
  border-radius: 5px;
}

.THIS .unsNotificationsCounter .headerButtonBody {
  display: block;
}

.THIS.acm-header-container {
  box-shadow: 0 5px 35px 0 rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 3;
  background: linear-gradient(90deg, #0140BC, #0241BD, var(--sbds-extended-11-color, #0A2240));
}

.THIS .acm-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 auto;
  height: 120px;
  max-width: 1368px;
  background-color: var(--sbds-gradient-primary-01-diagonal)!important;
}


.THIS .acm-header .acm-mobile-top__content {
  display: flex;
  justify-content: space-between;
  flex-wrap: nowrap;
  flex-direction: row;
  align-items: stretch;
  width: 100%;
  height: 100%;
}

.THIS .acm-header-right__content {
  display: flex;
  flex-wrap: nowrap;
  flex-direction: row;
  height: 100%;
  align-content: center;
  align-items: center;
}

.THIS .acm-header-right__content .notifications {
  margin-right: 20px;
}

.THIS .acm-header-right__content .tooltipTrigger, .THIS .acm-header-right__content .headerButtonBody{
  display: block;
}

.THIS .acm-header-right__content .slds-icon__container{
  display: block;
}

.THIS .acm-header-right__content .notifications svg.slds-icon.slds-icon_xx-small {
    color: #fff;
    top: unset;
  }

/* ? START - Search Icon */

.THIS .acm-header .padding-horizontal .slds-button:hover,
.THIS .acm-header .padding-horizontal .slds-button:focus {
  color: #FFF;
  text-decoration: none;
}

.THIS .acm-header .utilityicon {
  width: 1.5rem;
  height: 1.5rem;
  color: var(--sbds-white, #fff);
  margin-right: 20px;
}

.THIS .acm-header .rotate {
  -moz-transition: all .2s linear;
  -webkit-transition: all .2s linear;
  transition: all .2s linear;
  margin-left: 10px;
  fill: #fff;
}

.THIS .acm-header .rotate.down {
  -moz-transform:rotate(180deg);
  -webkit-transform:rotate(180deg);
  transform:rotate(180deg);
}

/* ! END - Search Icon */

/* ? START - Nav Logo */

.THIS .acm-header-logo-nav {
  display: flex;
  align-items: center;
  gap: var(--sbds-spacing-small);
  align-content: center;
  flex-wrap: nowrap;
  flex-direction: row;
}

.THIS .acm-header .sbglogo {
  cursor: pointer;
}

.THIS .acm-header .sbglogo img {
  height: 3.3125rem;
  width: 12.4375rem;
}

/* ! End - Nav Logo */

/* ? Start - Desktop Navigation menu */

.THIS .acm-header .acm-navbar ul.nav-links {
  list-style: none;
  display: flex;
  align-content: center;
  align-items: center;
  height: 100%;
}

.THIS .acm-header .acm-navbar ul.nav-links > li > a {
  color: #FFF;
  text-decoration: none;
  font-size: 16px;
  font-weight: 300;
  font-stretch: normal;
  font-style: normal;
  box-shadow: none;
  display: flex;
  align-items: center;
  align-content: center;
  height: 100%;
  line-height : 58px;
                                        
}

.THIS .acm-header .acm-navbar ul.nav-links > li > a > span {
  pointer-events: none
}

.THIS .acm-header .acm-navbar ul.nav-links > li {
  position: relative;

  border-bottom: 3px solid transparent;
  text-decoration: none;
  height: 100%;
}

.THIS .acm-header .acm-navbar > ul.nav-links > li:hover, .THIS .acm-header .acm-navbar ul.nav-links > li.menu-items-active {
  border-bottom: 3px solid #ffffff;
  line-height : 60px;                                     
} 

.THIS .acm-header .acm-navbar ul.nav-links li ul.acm-submenu {
  position: absolute;
  left: 0;
  width: 100px;
  background-color: #FFF;
  min-width: -webkit-fill-available;
  z-index: 99999;
  top: 75px;
  border-top: none;
  border-radius: 4px;
  display: none;
}

.THIS .acm-header .acm-navbar ul.nav-links li ul.acm-submenu.show-sub-menu{
  display: block;
}

.THIS a.slds-button.showSubMenu svg {
  fill: #fff;
}


.THIS .acm-header .acm-navbar ul.nav-links li ul.acm-submenu:before {
  border-bottom-color: #ffffff;
  width: 1rem;
  height: 1rem;
  position: absolute;
  transform: rotate(45deg);
  content: '';
  background-color: inherit;
  left: 1.5rem;
  top: -0.5rem;
  margin-left: -0.5rem;
}

.THIS .acm-header .acm-navbar ul.nav-links li ul.acm-submenu li {
  width: 100%;
  padding: 10px;
  font-size: 16px;
  flex: 1;
  height: 37px;
  display: flex;
  align-items: center;
  color: var(--stature-blue);
  text-align: left;
  max-width: 200px;
  border-color: #f0f0f0!important;
  border-bottom: 1px solid #ececec;
  margin: 0;
}

.THIS .acm-header .acm-navbar ul.nav-links li ul.acm-submenu li:last-child {
  border-bottom: 0px;
}

.THIS .acm-header .acm-navbar ul.nav-links li ul.acm-submenu li a {
  color: var(--lwc-colorTextDefault,rgb(10, 34, 64));
  font-size: .75rem;
  white-space: nowrap;
  overflow-x: hidden;
  text-overflow: ellipsis;
  position: relative;
  padding-left: 10px;
  font-weight: bold;
}

/* ! End of Desktop Navigation menu */

/* ? START - API Region controller */

.THIS .acm-region-container {
  height: 1.875rem;
  background-color: #fff;
  padding: 0 30px;
}

.THIS .acm-region-container .apicountryRegion {
  display: flex;
  justify-content: flex-end;
  max-width: 1368px;
  margin: auto;
}

.THIS .acm-region-container .apicountryRegion .api-countries {
  height: 30px;
  margin-right: 10px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.THIS .acm-region-container .apicountryRegion .api-countries img {
  height: 30px;
  width: auto;
  margin-right: .625rem;
}
.THIS .acm-header .profileMenuRegion .selfServiceUserProfileMenu .selfServiceProfileMenuTrigger .trigger-link:before{
    content: 'Username';
    color: #fff;
    font-family: var(--FONT-FAMILY);
    font-size: 1em;
    font-weight: normal;
    position: absolute
                                                    }
.THIS .acm-region-container .apicountryRegion .api-countries span {
  color: var(--neutrals-medium-grey-text-body, #1A314D);
}

/* ! END - API Region controller */

/* ? START - Profile Menu */

.THIS .acm-header .profileMenuRegion {
  position: relative;
  height: 100%;
  background: var(--sbds-secondary-color, #0062e1);
  margin-right: 1rem;
  display: flex;
  align-content: center;
  flex-direction: column;
  justify-content: center;
  width: auto;
  padding: 0 35px;
}


/* I see no trace of this in the design. I'm removing it for now */
.THIS .acm-header .profileMenuRegion span.profileName {
  display: none;
}

.THIS .acm-header .profileMenuRegion .linkLabel {
  display: flex;
  justify-content: center;
  align-items: center;
  border: none;
  border-radius: 0;
  height: 100%;
  align-content: center;
  flex-wrap: nowrap;
  flex-direction: row;
  font-size: 16px;
  font-weight: 500;
  text-transform: uppercase;
  gap: 15px;
}

.THIS .acm-header .profileMenuRegion .linkLabel:before {
  content: '';
  height: 24px;
  display: block;
  width: 24px;
  background-image: url("/internalapimarketplace/s/sfsites/c/resource/sbdsAssets/Icons/SECURITY/icn_lock_closed.svg");
  background-size: contain;
  background-repeat: no-repeat;
  position: relative;
  filter: brightness(0) saturate(100%) invert(99%) sepia(6%) saturate(585%) hue-rotate(237deg) brightness(115%) contrast(100%);
}

.THIS .slds-global-actions__item-action:hover:not(:disabled), .THIS .slds-global-actions__item-action:focus {
  color: #FFF;
}

.THIS .acm-header .profileMenuRegion .selfServiceUserProfileMenu .uiMenuList--default.uiMenuList {
  border-radius: 4px;
  position: absolute;
  top: 85px;
  padding: 0;
  width: fit-content;
  border: none;
  box-shadow: 0 0 4px 0 rgb(0 0 0 / 30%);
}
.THIS .acm-header .profileMenuRegion .selfServiceUserProfileMenu .uiMenuList.visible {
  min-width: auto;
}

.THIS .acm-header .profileMenuRegion .selfServiceUserProfileMenu .uiMenuList--default.uiMenuList::before {
    width: 1rem;
    height: 1rem;
    position: absolute;
    transform: rotate(45deg);
    content: '';
    background-color: #FFF;
    left: 75%;
    top: -0.5rem;
    margin-left: -0.5rem;
}

/* ! END - Profile Menu */

.THIS .searchRegion .button:active{
  background: unset;
}

.THIS .searchRegion span:last-child button{
  display: flex;
  height: 100%;
  flex-wrap: nowrap;
  margin: 0 20px 0 10px;
}

.THIS .searchRegion span:last-child button .utilityicon.cross{
  margin: 0;
}

.THIS .acm-header .profileMenuRegion .menuList ul li.uiMenuItem {
  margin: 0;
}

.THIS .acm-header .profileMenuRegion .selfServiceProfileMenuTrigger .trigger-link {
  padding: 0;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  flex-direction: row;
  align-content: center;
  justify-content: center;
}

.THIS .acm-header .profileMenuRegion .selfServiceUserProfileMenu .selfServiceProfileMenuTrigger .trigger-link::before {
  content: '';
  color: #fff;
  font-family: var(--FONT-FAMILY);
  font-size: 1em;
  font-weight: normal;
  position: absolute;

}
.THIS .profileMenuRegion span{
  color: #fff;
  font-family: var(--FONT-FAMILY);
  font-size: 1em;
  font-weight: normal;
}                                                                                 
.THIS .profileIcon{
	margin-left: 20%;                                                                                                
}

.THIS .acm-header .profileMenuRegion .selfServiceProfileMenuTrigger .profileIcon {
  width: 20px;
  height: 20px;
  margin-top: 5px;
}

.THIS .acm-header .profileMenuRegion .selfServiceProfileMenuTrigger .triggerDownArrow {
  display: none;
}

.THIS .selfServiceUserProfileMenu li.uiMenuItem a {
  display: block;
  color: var(--lwc-colorTextDefault,rgb(10, 34, 64));
  position: relative;
  white-space: nowrap;
  font-size: 0.75rem;
  overflow-x: hidden;
  text-overflow: ellipsis;
}

/* ? Start - Mobile Navigation menu */

.THIS .acm-header .navicon {
  display: none;
  width: 2rem;
  position: relative;
  cursor: pointer;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  height: 2rem;
}

/* ? End - Mobile Navigation menu */

.THIS .selfServiceUserProfileMenu .selfServiceProfileMenuTrigger .trigger-link img.profileIcon {
  display: none;
}

.THIS .selfServiceUserProfileMenu .menuList.popupTargetContainer .home.uiMenuItem {
  display: none;
}

.THIS .selfServiceUserProfileMenu .menuList.popupTargetContainer li.uiMenuItem:last-child {
  border-bottom: none;
}

@media only screen and (max-width: 1366px) {
  .THIS .acm-header {
    padding: 0 30px;
  }
}

@media only screen and (max-width: 1120px) {   
  .THIS .acm-header .sbglogo-image:after {
    display: none;
  }
}

@media only screen and (max-width: 1024px) {
 
  .THIS .acm-header {
    padding: 0 8px;
  }
  .THIS .acm-header .mobileNavOverlay {
    position: fixed;
    display: none;
    width: 100%;
    height: 100%;
    top: 9.375rem;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0,0,0,0.5);
    z-index: 2;
    cursor: pointer;
  }

  .THIS .acm-header .show-overlay {
    display: block !important;
    -moz-transition: all .2s linear;
    -webkit-transition: all .2s linear;
    transition: all .2s linear;
  }
  

  .THIS .acm-header .acm-navbar.show {
    display: block;
  }

 

  .THIS .acm-header .acm-navbar {
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    background: #fff;
    z-index: 999999;
    display: none;
  }
  .THIS .acm-header .acm-navbar ul.nav-links{
    flex-direction: column;
  }

  .THIS .acm-header .acm-navbar ul.nav-links li {
    display: flex;
    padding: 16px 28px 16px 32px;
    align-items: center;
    flex-shrink: 0;
    align-self: stretch;
    align-content: flex-start;
    justify-content: flex-start;
    flex-wrap: nowrap;
    flex-direction: column;
  }
  
  .THIS .acm-header .acm-navbar ul.nav-links li ul.acm-submenu:before {
    display: none;
  }

  .THIS .acm-header .acm-navbar > ul.nav-links > li, .THIS .acm-header .acm-navbar > ul.nav-links > li:hover {
    width: 100%;
    border-color: #f0f0f0!important;
    border-bottom: 1px solid #ececec;
  }

  .THIS .acm-header .acm-navbar > ul.nav-links > li:last-child {
    border-bottom: 0px;
  }

  .THIS .acm-header .acm-navbar ul.nav-links > li > a, .THIS .acm-header .acm-navbar ul.nav-links li ul.acm-submenu > li > a {
    line-height: unset;
    color: black;
    font-size: 16px!important;
    padding: 4px 10px;
    font-weight: normal;
    box-shadow: none;
    width: 100%;
    display: flex;
    text-decoration: none;
      
  }

  /* ? Begin - Submenu Chevron */
 
  .THIS .acm-header .rotate {
    right: 2rem;
    position: absolute;
  }

  

  .cACM_NavigationMenu a.slds-button.showSubMenu svg {
    height: 24px;
    width: 24px;
    fill: var(--sbds-secondary-color, #0062e1);
  }

  .THIS .acm-header .rotate.down {
    -moz-transform:rotate(180deg);
    -webkit-transform:rotate(180deg);
    transform:rotate(180deg);
  }

  /* ! End - Submenu Chevron */
  
  .THIS .acm-header .acm-navbar ul.nav-links li ul.acm-submenu {
    width: 100%;
    position: relative;
  }

  .THIS .acm-header .acm-navbar ul.nav-links li ul.acm-submenu li {
    padding: 16px 28px 16px 32px;
    background: #FFF;
    margin: 0 20px 0 0;
    max-width: 100%;
  }

  .THIS .acm-header .navicon {
    display: flex;
    gap: 0.25rem;
  }

  .THIS .acm-header .navicon span {
    width: 1.4rem;
    background-color: white;
    height: 1.5px;
    border-radius: 10px;
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }

}

@media only screen and (max-width: 769px) {

  .cACM_NavigationMenu .acm-header .profileMenuRegion .selfServiceProfileMenuTrigger > div > div {
    text-align: right;
  }

  .THIS .selfServiceUserProfileMenu .login .linkLabel {
    justify-content: flex-end;
  }

  .THIS .acm-header {
    margin-bottom: 40px;
  }

  .THIS .acm-header .profileMenuRegion {
    margin-right: 0;
  }
}

@media only screen and (max-width: 600px) {
  .THIS .searchRegion span:last-child button{
    margin: unset;
  }
  .THIS .searchRegion .acm_pkgAcmSearch{
    position: absolute;
    left: 10px;
    width: calc(100% - 60px);
    top: 40px;
  }

  .THIS .acm-header {
    display: flex;
    flex-direction: column;
    padding: 0 var(--sbds-spacing-small, 1rem);
    justify-content: center;
    height: 60px;
  }

  .THIS .acm-header .mobileNavOverlay { 
    top: 5.625rem;
  }
  
  .THIS .acm-mobile-bottom__content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .THIS .acm-header .profileMenuRegion {
    display: block;
    align-items: center;
    position: absolute;
    right: 0;
    width: 50%;
    top: 60px;
    margin-right: 0;
    height: auto;
    padding: 17px;
  }

  .THIS .acm-header .profileMenuRegion .selfServiceProfileMenuTrigger .trigger-link:before {
    font-size: 13px;
  }

  .THIS .acm-header .profileMenuRegion .menuList{
    top: 45px!important;
  }
  
  .THIS .acm-header .utilityicon {
    width: 1.375rem;
    height: 1.375rem;
    margin-right: 0px;
  }

  .THIS .acm-region-container {
    padding: 0 16px;
    position: absolute;
    top: 60px;
    width: 50%;
  }

  .THIS .acm-region-container .apicountryRegion {
    display: flex;
    justify-content: center;
    max-width: 1368px;
    margin: auto;
    position: relative;
  }

  .THIS .acm-region-container .apicountryRegion .api-countries{
    margin-right: 0;
  }
  .THIS .acm-region-container .apicountryRegion .api-countries span{
    margin-right: 0;
  }
  .THIS .acm-region-container .apicountryRegion .api-countries img{
    height: 25px;
    width: auto;
    margin-right: .325rem;
  }

  .THIS .acm-header .navicon {
    display: flex;
  }

  .THIS .acm-header .utilityicon.cross {
    margin-top: 0.15rem;
    margin-left: 0.5rem;
  }

  .THIS .acm-header .sbglogo-image {
    height: 35px;
    width: 136px;
  }
  .THIS .acm-header .sbglogo img{
    height: auto;
    width: 8.3125rem;
  }

}