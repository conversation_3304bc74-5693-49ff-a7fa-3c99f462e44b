<aura:component controller="Angola_CloseCaseController" implements="force:hasRecordId,force:lightningQuickAction" access="global" >
    <aura:attribute name="recordId" type="String"  />
    <aura:attribute name="caseRecord" type="Case" />
    <aura:attribute name="options" type="Angola_StatusPicklistController[]"  />
    <aura:attribute name="isLoading" type="Boolean" default="true" />
    <aura:attribute name="errorMsg" type="String"/>
    <aura:attribute name="selectedRecord" type="sObject" default="{}" description="Use,for store SELECTED sObject Record"/>
    <aura:attribute name="listOfSearchRecords" type="List" description="Use,for store the list of search records which returns from apex class"/>
    <aura:attribute name="SearchKeyWord" type="string"/>
    <aura:attribute name="objectAPIName" type="string" default="Contact"/>
    <aura:attribute name="label" type="string" default="Resolvido por"/>
    <aura:attribute name="Message" type="String" default=""/>
    <aura:attribute name="caseNumber" type="String" default=""/>
    <aura:attribute name="isModalOpen" type="boolean" default="false"/>
    <aura:attribute name="selectedLookUpRecord" type="sObject" default="{}"/>
    <!--declare events hendlers-->  
    <aura:handler name="oSelectedRecordEvent" event="c:Angola_QuickCaseCloseEvent" action="{!c.handleComponentEvent}"/>
    <aura:handler name="init" value="{!this}" action="{!c.doInit}" />
    <aura:if isTrue="{!v.errorMsg != null}">
        <div class="slds-notify slds-notify_alert slds-theme_alert-texture slds-theme_error slds-text-heading_small" role="alert">
            <h4>{!v.errorMsg}</h4>
        </div>
    </aura:if>
    <div class="slds-grid slds-grid_vertical ">
        <div class="slds-container__medium">
            <lightning:select aura:id="select" label="Estado" name="Status" value="{!v.caseRecord.Status}">
                <aura:iteration items="{!v.options}" var="status">
                    <option  value="{!status.value}" text="{!status.label}"/>
                </aura:iteration>
            </lightning:select>
        </div>
        <div class="slds-container__medium">
            <ui:inputtextarea label="Comentário de Resolução" value="{!v.caseRecord.Resolution_Comment__c}" />
        </div>
        <div class="slds-container__medium">
            <ui:inputtextarea label="Comentários de Resolução Interna" value="{!v.caseRecord.Next_Steps__c}" />
        </div> 
        
        <!--Lookup-->
        <div onmouseleave="{!c.onblur}" aura:id="searchRes" class="slds-form-element slds-lookup slds-is-close" data-select="single">
            <label class="slds-form-element__label" for="lookup-348">{!v.label}</label>
            <!--This part is for display search bar for lookup-->  
            <div class="slds-form-element__control">
                
                <div class="slds-input-has-icon slds-input-has-icon_right">
                    <!-- This markup is for when an record is selected -->
                    <div aura:id="lookup-pill" class="slds-pill-container slds-hide">
                        <lightning:pill class="pillSize" label="{!v.selectedRecord.Name}" name="{!v.selectedRecord.Name}" onremove="{! c.clear }">
                            <aura:set attribute="media">
                                <lightning:icon iconName="{!v.IconName}" size="x-small" alternativeText="{!v.IconName}"/>
                            </aura:set>
                        </lightning:pill>
                    </div>
                    <div aura:id="lookupField" class="slds-show">
                        <lightning:icon class="slds-input__icon slds-show" iconName="utility:search" size="x-small" alternativeText="search"/>
                        <span class="slds-icon_container  slds-combobox__input-entity-icon" title="record">
                            <lightning:icon class="slds-icon slds-icon slds-icon_small slds-icon-text-default" iconName="{!v.IconName}" size="x-small" alternativeText="icon"/>
                            <span class="slds-assistive-text"></span>
                        </span>
                        <ui:inputText click="{!c.onfocus}" updateOn="keyup" keyup="{!c.keyPressController}" class="slds-lookup__search-input slds-input leftPaddingClass" value="{!v.SearchKeyWord}" placeholder="Procurar Contactos...."/>
                    </div>   
                </div>
            </div>
            <!--This part is for Display typehead lookup result List-->  
            <ul style="min-height:40px;margin-top:0px !important" class="slds-listbox slds-listbox_vertical slds-dropdown slds-dropdown_fluid slds-lookup__menu slds" role="listbox">
                <lightning:spinner class="slds-hide" variant="brand" size="small" aura:id="mySpinner"/>
                <center> {!v.Message}</center>
                <aura:iteration items="{!v.listOfSearchRecords}" var="singleRec">
                    <c:Angola_CaseResolveContactList oRecord="{!singleRec}" IconName="{!v.IconName}"/>
                </aura:iteration>
            </ul>
        </div>
        <br></br>
        <!---Lookup end-->
        <div class="slds-container__medium">
            <lightning:button class="slds-float_right slds-m-left_medium slds-m-top_medium slds-button slds-button_brand" label="Guardar" onclick="{!c.openModel}"/>
            <aura:if isTrue="{!v.isModalOpen}">
                <!-- Modal/Popup Box starts here-->
                <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
                    <div class="slds-modal__container">
                        <!-- Modal/Popup Box Header Starts here-->
                        <header class="slds-modal__header">
                            <lightning:buttonIcon iconName="utility:close"
                                                  onclick="{! c.closeModel }"
                                                  alternativeText="close"
                                                  variant="bare-inverse"
                                                  class="slds-modal__close"/>
                            <h2 id="modal-heading-01" class="slds-text-heading_medium slds-hyphenate">Fechar o Caso</h2>
                        </header>
                        <!--Modal/Popup Box Body Starts here-->
                        <div class="slds-modal__content slds-p-around_medium" id="modal-content-id-1">
                            <H3>Confirma que quer fechar o Caso?</H3>
                        </div>
                        <!--Modal/Popup Box Footer Starts here-->
                        <footer class="slds-modal__footer">
                            <lightning:button variant="brand"
                                              label="Sim"
                                              title="Yes"
                                              onclick="{!c.submitDetails}"/>
                            <lightning:button variant="brand"
                                              label="Não"
                                              title="No"
                                              onclick="{!c.closeModel}"/>
                        </footer>
                    </div>
                </section>
                <div class="slds-backdrop slds-backdrop_open"></div>
            </aura:if>
        </div>
    </div>
</aura:component>