.THIS .exit {
    font-family: BentonSans,sans-serif;
    font-size: 18px;
    font-weight: 500;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.35;
    letter-spacing: 1px;
    color: white;
    cursor: pointer;
}

.THIS .d-flex_space {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.THIS .d-flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    width: 100%;
}

.THIS .d-flex span {
    color: var(--white);
    cursor: pointer;
    font-size: 20px;
    font-family: 'bentonsans-medium-webfont',sans-serif;
    font-weight: 500;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.35;
    letter-spacing: 1px;
}

.THIS .d-flex span:hover {
    text-decoration: none;
}

.THIS .footerAlignment {
    justify-content: space-between;
    display: flex;

}

.THIS .textalign {
    font-size: 22px;
    margin-left: 30px;


}

.THIS.sb .footer .app-card__icon-container {
    position: relative;
    width: 550px;
    display: flex;
    justify-content: space-between;
}

@media (max-width: 590px) {
    .THIS .ao-header-logo {
        height: 53px;
    }

    .THIS .exit {
        font-size: 16px;
    }
}

@media (max-width: 1199px) {
    .THIS.sb .footer .app-card__icon-container {
        width: 400px;

    }
}

@media (max-width: 991px) {
    .THIS .container {
        width: 100%;
        max-width: 100%;

    }
}

@media (max-width: 874px) {
    .THIS .container {
        width: 100%;
        max-width: 100%;
    }

    .THIS .footer-devices-img img {
        width: 108px;
        height: 107px;
    }

    .THIS.sb .footer .app-card__icon-container a {
        margin: 0px 25px;

    }
}

@media (max-width: 645px) {
    .THIS .footerAlignment {
        display: inline;
    }

    .THIS.sb .footer .app-card__icon-container {
        justify-content: space-between;
        width: 100%;

    }
}