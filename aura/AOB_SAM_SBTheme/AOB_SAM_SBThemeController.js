({
    handleShowBackButton: function (component, event) {
        let showBackButton = event.getParam("showBackButton");
        component.set("v.showBackButton", showBackButton);
    },
    handleBackClicked: function(component){
        component.set("v.showCloseModal", true);
    },
    handleModalClose: function(component){
        component.set("v.showCloseModal", false);
    },
    openModel: function(component, event, helper) {
        component.find('aob_sam_comp_exit').LWCFunction ();  
    }

    
})