({
    onInit: function(component, event, helper) {
        
        
        helper.setupPathValidation(component);

        let userId = $A.get("$SObjectType.CurrentUser.Id");
        if(userId){
            component.set("v.isUserLoggedIn",true);
        }
        
        /**
         * The function `createLink` dynamically creates a link element in the HTML head with specified
         * attributes such as rel, href, type, sizes, and color.
         * @param rel - The `rel` parameter specifies the relationship between the current document and
         * the linked resource. It is commonly used to define the relationship of the linked document
         * to the current document, such as stylesheet, icon, etc.
         * @param href - The `href` parameter in the `createLink` function represents the URL of the
         * linked resource. It specifies the location of the linked document or resource.
         * @param type - The `type` parameter in the `createLink` function is used to specify the MIME
         * type of the linked resource. It indicates the type of content that is being linked to. For
         * example, for a CSS file, the `type` could be set to "text/css".
         * @param sizes - The `sizes` parameter in the `createLink` function is used to specify the
         * sizes of the icons for the link element. It is typically used when creating icon links for
         * different devices or screen resolutions. The `sizes` attribute takes a space-separated list
         * of icon sizes in the format "widthx
         * @param color - The `color` parameter in the `createLink` function is used to specify the
         * color of the link. This parameter allows you to set the color attribute of the link element
         * being created.
         */
        function createLink(rel, href, type, sizes, color) {
            let link = document.createElement('link');
            link.rel = rel;
            if (href) link.href = href;
            if (type) link.type = type;
            if (sizes) link.sizes = sizes;
            if (color) link.color = color;
            document.head.appendChild(link);
        }

        
        /**
         * The function `createMeta` creates a meta tag with specified name and content and appends it
         * to the document head.
         * @param name - The `name` parameter is a string that represents the name attribute of the
         * meta tag to be created.
         * @param content - The `content` parameter in the `createMeta` function represents the value
         * that will be assigned to the `content` attribute of the created `<meta>` element. This value
         * typically provides additional information or metadata related to the `name` attribute of the
         * `<meta>` element.
         */
        function createMeta(name, content) {
            let meta = document.createElement('meta');
            meta.name = name;
            meta.content = content;
            document.head.appendChild(meta);
        }

        // Get the base URL from component property
        let baseUrl = component.get("v.brandFaviconUrl");

        // Call the functions to create link and meta tags using the base URL
        createLink('apple-touch-icon', baseUrl + '/favicons/apple-touch-icon.png', null, '180x180');
        createLink('icon', baseUrl + '/favicons/favicon-32x32.png', 'image/png', '32x32');
        createLink('icon', baseUrl + '/favicons/favicon-16x16.png', 'image/png', '16x16');
        createLink('manifest', baseUrl + '/favicons/site.webmanifest');
        createLink('mask-icon', baseUrl + '/favicons/safari-pinned-tab.svg', null, null, '#0033aa');
        createMeta('msapplication-TileColor', '#0033aa');
        createMeta('theme-color', '#ffffff');
    }
})