<aura:component implements="forceCommunity:themeLayout" description="UI Library Layout" access="global">
  <aura:attribute name="uiLibraryVersion" type="string" default="3.0" access="public" />
  <aura:attribute name="muleSoftStyleVariant" type="string" default="None" access="public" />
  <aura:attribute name="cacheBang" type="string" default="?v=0.650" access="public" />
  <aura:attribute name="header" type="Aura.Component[]" access="public" />
  <aura:attribute name="body" type="Aura.Component[]" access="public" />
  <aura:attribute name="footer" type="Aura.Component[]" required="false" access="public"></aura:attribute>

  <aura:attribute name="search" type="Aura.Component[]" required="false" access="public" />
  <aura:attribute name="useCompactHeader" type="Boolean" default="false" access="public" />
  <aura:attribute name="profileMenu" type="Aura.Component[]" required="false" access="public" />
  <aura:attribute name="importantNotices" type="string" required="false" access="public" />
  <aura:attribute name="isUserLoggedIn" type="Boolean" default="false" access="public" />
  <aura:attribute name="ispathExternal" type="Boolean" default = "true"/>
    
  <aura:attribute
    name="brandFaviconUrl"
    type="string"
    default="{!$Resource.sbdsAssets}"
    access="public"
    />

    <aura:if isTrue="{!v.uiLibraryVersion == '3.0'}">
        <aura:if isTrue="{!v.ispathExternal}">
            <ltng:require styles="{!'/APIMarketplace/sfsites/c/resource/SBDS_3_0_100/Styles/dist/style.css' + v.cacheBang}" />
            <aura:set attribute = "else">
                <ltng:require styles="{!'/sfsites/c/resource/SBDS_3_0_100/Styles/dist/style.css' + v.cacheBang}" />
            </aura:set>
        </aura:if>
    </aura:if>

  <aura:handler name="init" value="{!this}" action="{!c.onInit}"/>

  <div class="theme-page" data-mulesoft-variant="{!v.muleSoftStyleVariant}" data-sbds-ui-v="{!v.uiLibraryVersion}">
    <div class="allContent">
      <header class="header">
        <aura:if isTrue="{! !v.isUserLoggedIn}">
            <div role="group" aria-label="Other notices">{! v.importantNotices }</div>
        </aura:if>
        <aura:if isTrue="{!v.useCompactHeader}">
            <div class="slds_grid">
                <div class="slds-col slds-small-size_12-of-12 slds-medium-size_9-of-12 slds-large-size_10-of-12 ">
                    <div role="group" aria-label="Other notices">
                        <div role="group" aria-label="Header with main navigation">
                            {!v.header}
                        </div>
                    </div>
                </div>
            </div>
            <aura:set attribute="else">
                <c:ACM_NavigationMenu hideCountrySelection="true" profileMenu="{! v.profileMenu }" search="{!v.search}" apiCountryUrl=""></c:ACM_NavigationMenu>
            </aura:set>
        </aura:if>
    </header>
      <!-- <div role="group" aria-label="Header with main navigation" class="header-region">
        {!v.header}
      </div> -->
      <div role="main" aura-id="main" class="main-region">
        {!v.body}
      </div>
    </div>
    <footer class="footer-region">
      {!v.footer}
    </footer>
  </div>
</aura:component>