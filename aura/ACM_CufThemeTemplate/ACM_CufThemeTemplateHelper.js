({
	setupPathValidation: function(component) {
        try {
            const pathName = this.getSecurePath();
            
            if (pathName && pathName.includes('internalapimarketplace')) {
                component.set("v.ispathExternal", false);
            }
            
        } catch (error) {
            component.set("v.ispathExternal", true);
        }
    },
    
    getSecurePath: function() {
        // Sanitize and validate path
        const path = window.location.pathname;
        return path;
    }
})