({
    init : function(component, event, helper) {
        let userId = $A.get("$SObjectType.CurrentUser.Id");
        if(userId){
            component.set("v.isUserLoggedIn",true);
        }else{
            var urlString = window.location.href;
            if(urlString.indexOf('/s/') > -1){
                let baseURL = urlString.substring(0, urlString.indexOf("/s/"));
                component.set("v.apiCountryUrl", baseURL+'/s/countries'); 
            }
        }
    },    
})