<aura:component implements="forceCommunity:availableForAllPageTypes" access="global">
    
    <aura:attribute  name="headerText" type="String" access="global"/>
    <aura:attribute  name="ShieldType" type="Boolean" access="global"/>
    <aura:attribute  name="ShieldBackingicon1svg" type="String" access="global"/>
    <aura:attribute  name="ACMHomepageicon1svg" type="String" access="global"/>
    <aura:attribute  name="ACMHomepageicon2svg" type="String" access="global"/>
    <aura:attribute  name="ACMHomepageicon3svg" type="String" access="global"/>
    <aura:attribute  name="ACMHomepageicon4svg" type="String" access="global"/>


    <aura:attribute name="section1api" type="string" access="global"/>
    <aura:attribute name="section1section" type="string" access="global"/>
    <aura:attribute name="section1selectedPage" type="string" access="global"/>

    
    <aura:attribute name="section2api" type="string" access="global"/>
    <aura:attribute name="section2section" type="string" access="global"/>
    <aura:attribute name="section2selectedPage" type="string" access="global"/>

    <aura:attribute name="section3api" type="string" access="global"/>
    <aura:attribute name="section3section" type="string" access="global"/>
    <aura:attribute name="section3selectedPage" type="string" access="global"/>

    <aura:attribute name="section4api" type="string" access="global"/>
    <aura:attribute name="section4section" type="string" access="global"/>
    <aura:attribute name="section4selectedPage" type="string" access="global"/>

        <aura:attribute name="showButtons" type="Boolean" default="true" access="global" />
        <aura:attribute name="cardHeight" type="String" default="Medium" access="global" />
        <aura:attribute name="cardMargin" type="String" default="Medium" access="global" />
        <aura:attribute name="cardBorder" type="String" default="Thin" access="global" />
        <aura:attribute name="cardBorderRadius" type="String" default="Small" access="global" />
        <aura:attribute name="cardBoxShadow" type="String" default="Medium" access="global" />
        <aura:attribute name="detailsLabel" type="String" access="global" />
        <aura:attribute name="groupDetailsLabel" default="Group Details" type="String" access="global" />
        <aura:attribute name="learnMoreLabel" type="String" default="Learn more" access="global" />
        <aura:attribute name="iconPosition" type="String" access="global" />
        <aura:attribute name="record" type="Object" access="global" />
        <aura:attribute name="recordId" type="String" access="global" />
        <aura:attribute name="recordDataFields" type="String[]" access="global"/>
        <aura:attribute name="isLoadingApiVersion" type="Boolean" access="global" default="true" />
        <aura:attribute name="isLoadingScript" type="Boolean" access="global" default="true" />
        <aura:attribute name="page" type="String" access="global" />
        <aura:if isTrue="{!or(v.isLoadingApiVersion, v.isLoadingScript)}">
        </aura:if>
       
            <force:recordData
                aura:id="record"
                recordId="a6t0E0000006c7uQAA"
                targetFields="{!v.record}"
                recordUpdated="{!c.recordUpdated}"
                mode="VIEW"
                fields="{!v.recordDataFields}" />
        <div aura:id="ApiVersionCard" />
</aura:component>