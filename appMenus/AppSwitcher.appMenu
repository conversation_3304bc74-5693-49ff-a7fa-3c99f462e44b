<?xml version="1.0" encoding="UTF-8"?>
<AppMenu xmlns="http://soap.sforce.com/2006/04/metadata">
    <appMenuItems>
        <name>standard__Platform</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>standard__Sales</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>standard__Service</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>GMDCApplication</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>StandardBankPipeline</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>SFL20__CampaignTimeline</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>PMApplication</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>standard__Ideas</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>Cases</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>South_Africa</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>standard__Content</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>standard__ServiceConsole</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>Portfolio_Management</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>standard__Chatter</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>Standard_Bank</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>Shared_Calendar</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>Dataloader_Bulk</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>Dataloader_Partner</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>Forcecom_IDE</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>Forcecom_Migration_Tool</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>Salesforce_Mobile_Dashboards</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>Salesforce_Touch</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>Salesforce_for_Outlook</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>Workbench</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>Chatter_Desktop</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>Chatter_for_Android</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>Chatter_for_BlackBerry</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>Chatter_for_iOS</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>Salesforce_Chatterbox</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>SalesforceA</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>Salesforce1_for_Windows</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>Helpdesk_Console</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>The_Chatter_Game</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>StdBank_Ltn_NavigationMenu</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>standard__LightningSales</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>standard__LightningService</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>standard__LightningSalesConsole</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>MobileAdmin_Android</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>MobileAdmin_iOS</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>ltnadptn__Lightning_Adoption_Reporting</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>standard__AppLauncher</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>CIB_Investor_Services_Console</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>Account_Onboarding</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>standard__AllTabSet</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>standard__LightningInstrumentation</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>CRM_Nigeria_Login</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>SingletrackCMS__Roadshow</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>SingletrackCMS__SingletrackCMS</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>SingletrackCMS__SingletrackCRM</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>SingletrackCMS__Singletrack_Research</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>Singletrack</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>SingleTrack_Research_CDP</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>Service_Cloud_Angola</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>SingletrackCMS__Singletrack_Lightning</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>Service_Lite</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>standard__SalesforceCMS</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>lightningbuddy__Lightning_Buddy</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>OIQ_Integration</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>Salesforce_to_SalesforceIQ_Secure_Connected_App</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>MyAccess</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>Cross_Border_Service</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>OneHub</name>
        <type>Network</type>
    </appMenuItems>
    <appMenuItems>
        <name>Customer Service</name>
        <type>Network</type>
    </appMenuItems>
    <appMenuItems>
        <name>OneHub_Management</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>IB_Online_Portal</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>standard__Optimizer</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>LEXMAGICMOVER__Attachments_and_Notes_migrator</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>standard__InsuranceConsole</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>standard__OnlineSales</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>PBB_SE_Lifestyle_App</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>PartnerSyncApp</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>Salesforce_Marketing_Cloud</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>standard__BranchManagementConsole</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>IBM_Integration_Bus</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>DCS_Permissions_Manager</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>Pocket_RSVP</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>IBM_ESB_Connected_App</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>Partner Portal</name>
        <type>Network</type>
    </appMenuItems>
    <appMenuItems>
        <name>FinServ__BankingConsoleFinancialServicesCloud</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>FinServ__CommBankingConsoleFinancialServicesCloud</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>FinServ__FSC_Lightning</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>FinServ__FinancialServicesCloud</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>FinServ__FinancialServicesCloudRetailBanking</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>FinServ__InsuranceConsoleFinancialServicesCloud</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>Financial_Services_Bot</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>PP_PartnerRelationshipManagement</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>OneHub_Service_Console</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>Akili_Insights</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>Akili_Insights_App</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>Account_origination_Management</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>RAS_DCP</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>acm_pkg__ACMDiagnostics</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>acm_pkg__ACMGuidedSetup</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>acm_pkg__API_Community_Manager</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>API Marketplace</name>
        <type>Network</type>
    </appMenuItems>
    <appMenuItems>
        <name>ACM_APICommunity_Administration</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>LIMITMON__Limits_Monitor</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>grax__GRAX</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>grax__GRAX_Classic</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>CIB_Online_CST_Integration</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>Africa_Regions_Sales</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>standard__ExpressionSetConsole</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>IBM_ESB_Integration_Connected_Application</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>amazonconnect__Lightning_Console_App_2</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>amazonconnect__Sales_Console_App</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>SVC_AmazonWebServicesAndConnect</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>standard__Insights</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>standard__DataManager</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>Events App Experience Cloud</name>
        <type>Network</type>
    </appMenuItems>
    <appMenuItems>
        <name>Internal API Marketplace</name>
        <type>Network</type>
    </appMenuItems>
    <appMenuItems>
        <name>EAP_Events_App</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>BCWPROD</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>Service_Team_Setup</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>sbgplatform__rflib_Ops_Center</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>Namibia Marketplace</name>
        <type>Network</type>
    </appMenuItems>
    <appMenuItems>
        <name>mp_00DDn000008hQX5_0004</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>Product Business Pages</name>
        <type>Network</type>
    </appMenuItems>
    <appMenuItems>
        <name>Account Origination</name>
        <type>Network</type>
    </appMenuItems>
    <appMenuItems>
        <name>mp_00DDn000008hQX5_0048</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>Salesforce_POPIA_Connection_App</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>sbgui__SBG_Common_UI_Setup</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>sbgsvc__SBG_Services_Setup</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>Atonit_Mktplace__Marketplace</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>Namibia_Commerce_Marketplace_Integration</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>zafin_rrp__Zafin_Pricing_for_Salesforce</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>Zafin_Deal_Manager_Connector</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>ACM_Publish_API_to_community</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>guar__GUAR_Admin</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>Profile2PermSet__Profiles_to_Permission_Sets</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>echosign_dev1__EchoSign</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>echosign_dev1__Adobe_Sign_Salesforce</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>Onboard_My_Fund</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>Managed_Fund_Onboarding</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>MAU</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>sbg__Anvil</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>sbgpers__Recommendation_Manager</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>SBG_Business_Org_Connector</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>sbgbase__Batch_Job_Scheduler</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>Splunk_Integration</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>OMFIntegration</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>AppOmni</name>
        <type>ConnectedApp</type>
    </appMenuItems>
    <appMenuItems>
        <name>The Mall</name>
        <type>Network</type>
    </appMenuItems>
    <appMenuItems>
        <name>marketing_cal__Marketing_Calendar</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>marketing_cal__Ltg_Marketing_Calendar</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>SBG_Mall</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>standard__FlowsApp</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>standard__IndustriesEpc</name>
        <type>CustomApplication</type>
    </appMenuItems>
    <appMenuItems>
        <name>sbgac__Action_Centre_Setup</name>
        <type>CustomApplication</type>
    </appMenuItems>
</AppMenu>
