<?xml version="1.0" encoding="UTF-8"?>
<CustomApplication xmlns="http://soap.sforce.com/2006/04/metadata">
    <formFactors>Small</formFactors>
    <formFactors>Large</formFactors>
    <isNavAutoTempTabsDisabled>false</isNavAutoTempTabsDisabled>
    <isNavPersonalizationDisabled>false</isNavPersonalizationDisabled>
    <isNavTabPersistenceDisabled>false</isNavTabPersistenceDisabled>
    <isOmniPinnedViewEnabled>false</isOmniPinnedViewEnabled>
    <label>Service Console</label>
    <navType>Console</navType>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Change_Request</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Change_Request</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Client_Case</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Client_Case</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.CIB_Customer_Query</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.CIB_Customer_Query</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.CommB_Client_Case</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.CommB_Client_Case</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Production_Incident</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Production_Incident</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.User_Case</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.User_Case</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Commercial_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Commercial_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Contact_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Contact</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Contact_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Contact</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Contact_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Contact</pageOrSobjectType>
        <recordType>Contact.SA_Bank_Contact_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Contact_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Contact</pageOrSobjectType>
        <recordType>Contact.SA_Bank_Contact_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Contact_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Contact</pageOrSobjectType>
        <recordType>Contact.SA_Client_Contact_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Contact_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Contact</pageOrSobjectType>
        <recordType>Contact.SA_Client_Contact_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Contact_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Contact</pageOrSobjectType>
        <recordType>Contact.Inactive_Client_Contact_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Contact_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Contact</pageOrSobjectType>
        <recordType>Contact.Inactive_Client_Contact_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_PBB_ROA_Entity_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>PBB_ROA_Entity__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_PBB_ROA_Entity_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>PBB_ROA_Entity__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_PBB_ROA_Entity_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>PBB_ROA_Entity__c</pageOrSobjectType>
        <recordType>PBB_ROA_Entity__c.Commercial_Client</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_PBB_ROA_Entity_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>PBB_ROA_Entity__c</pageOrSobjectType>
        <recordType>PBB_ROA_Entity__c.Commercial_Client</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_PBB_ROA_Entity_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>PBB_ROA_Entity__c</pageOrSobjectType>
        <recordType>PBB_ROA_Entity__c.SME_Client</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_PBB_ROA_Entity_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>PBB_ROA_Entity__c</pageOrSobjectType>
        <recordType>PBB_ROA_Entity__c.SME_Client</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Event_Report_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Event_Report_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Event_Report_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <recordType>Call_Report__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Event_Report_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <recordType>Call_Report__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Event_Report_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <recordType>Call_Report__c.NBAC_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Event_Report_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <recordType>Call_Report__c.NBAC_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>Tab</actionName>
        <content>Client_Coverage_Executive_Home_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>Tab</actionName>
        <content>Client_Coverage_Home_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>Tab</actionName>
        <content>Lightning_Generic_Home_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>Tab</actionName>
        <content>Credit_Credit_Analyst_Home_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Credit Credit Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>Tab</actionName>
        <content>CommB_CSU_Home_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>Tab</actionName>
        <content>CommB_RM_Home_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>Tab</actionName>
        <content>CommB_RM_Home_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <setupExperience>service</setupExperience>
    <tabs>standard-Case</tabs>
    <tabs>standard-Contact</tabs>
    <tabs>standard-Account</tabs>
    <tabs>standard-report</tabs>
    <tabs>standard-Dashboard</tabs>
    <tabs>standard-home</tabs>
    <tabs>Knowledge__kav</tabs>
    <tabs>standard-WaveHomeLightning</tabs>
    <uiType>Lightning</uiType>
    <utilityBar>LightningService_UtilityBar</utilityBar>
    <workspaceConfig>
        <mappings>
            <tab>Knowledge__kav</tab>
        </mappings>
        <mappings>
            <tab>standard-Account</tab>
        </mappings>
        <mappings>
            <tab>standard-Case</tab>
        </mappings>
        <mappings>
            <tab>standard-Contact</tab>
        </mappings>
        <mappings>
            <tab>standard-Dashboard</tab>
        </mappings>
        <mappings>
            <tab>standard-WaveHomeLightning</tab>
        </mappings>
        <mappings>
            <tab>standard-home</tab>
        </mappings>
        <mappings>
            <tab>standard-report</tab>
        </mappings>
    </workspaceConfig>
</CustomApplication>
