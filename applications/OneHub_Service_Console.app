<?xml version="1.0" encoding="UTF-8"?>
<CustomApplication xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Wholesale_Service_LEX</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Case</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Wholesale_Service_LEX</content>
        <formFactor>Small</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Case</pageOrSobjectType>
    </actionOverrides>
    <brand>
        <headerColor>#0070D2</headerColor>
        <shouldOverrideOrgTheme>false</shouldOverrideOrgTheme>
    </brand>
    <description>SFP-6660: This app is for OneHub Support Team to maintain all the OneHub Service Requests.</description>
    <formFactors>Small</formFactors>
    <formFactors>Large</formFactors>
    <isNavAutoTempTabsDisabled>false</isNavAutoTempTabsDisabled>
    <isNavPersonalizationDisabled>false</isNavPersonalizationDisabled>
    <isNavTabPersistenceDisabled>false</isNavTabPersistenceDisabled>
    <isOmniPinnedViewEnabled>false</isOmniPinnedViewEnabled>
    <label>OneHub Service Console</label>
    <navType>Console</navType>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_LEX</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_LEX</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>OneHub Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>OneHub Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>Tab</actionName>
        <content>OneHub_Service_Home_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Api User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>Tab</actionName>
        <content>OneHub_Service_Home_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>Tab</actionName>
        <content>OneHub_Service_Home_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>OneHub Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>Tab</actionName>
        <content>OneHub_Service_Home_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_Closed_LEX</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Closed_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>OneHub Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_Closed_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Closed_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>OneHub Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_Closed_LEX</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Closed_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_Closed_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Closed_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_LEX</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Hand_Off</recordType>
        <type>Flexipage</type>
        <profile>OneHub Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Hand_Off</recordType>
        <type>Flexipage</type>
        <profile>OneHub Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_LEX</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Hand_Off</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Hand_Off</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_LEX</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Hand_Off</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Hand_Off</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_Closed_LEX</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Closed_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_Closed_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Closed_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_LEX</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <setupExperience>all</setupExperience>
    <tabs>standard-home</tabs>
    <tabs>standard-Case</tabs>
    <tabs>standard-Contact</tabs>
    <tabs>standard-Account</tabs>
    <tabs>standard-report</tabs>
    <tabs>standard-Dashboard</tabs>
    <tabs>Knowledge__kav</tabs>
    <tabs>Subscribed_Solutions__c</tabs>
    <tabs>Log__c</tabs>
    <tabs>Notification__c</tabs>
    <uiType>Lightning</uiType>
    <utilityBar>OneHub_Service_Console_UtilityBar</utilityBar>
    <workspaceConfig>
        <mappings>
            <tab>Knowledge__kav</tab>
        </mappings>
        <mappings>
            <tab>Log__c</tab>
        </mappings>
        <mappings>
            <tab>Notification__c</tab>
        </mappings>
        <mappings>
            <tab>Subscribed_Solutions__c</tab>
        </mappings>
        <mappings>
            <tab>standard-Account</tab>
        </mappings>
        <mappings>
            <tab>standard-Case</tab>
        </mappings>
        <mappings>
            <tab>standard-Contact</tab>
        </mappings>
        <mappings>
            <tab>standard-Dashboard</tab>
        </mappings>
        <mappings>
            <tab>standard-home</tab>
        </mappings>
        <mappings>
            <tab>standard-report</tab>
        </mappings>
    </workspaceConfig>
</CustomApplication>
