<?xml version="1.0" encoding="UTF-8"?>
<CustomApplication xmlns="http://soap.sforce.com/2006/04/metadata">
    <brand>
        <headerColor>#0070D2</headerColor>
        <shouldOverrideOrgTheme>false</shouldOverrideOrgTheme>
    </brand>
    <formFactors>Small</formFactors>
    <formFactors>Large</formFactors>
    <isNavAutoTempTabsDisabled>false</isNavAutoTempTabsDisabled>
    <isNavPersonalizationDisabled>false</isNavPersonalizationDisabled>
    <isNavTabPersistenceDisabled>false</isNavTabPersistenceDisabled>
    <isOmniPinnedViewEnabled>false</isOmniPinnedViewEnabled>
    <label>SBG Mall</label>
    <navType>Standard</navType>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Promotion_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Promotion__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>BCB Platform Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Promotion_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Promotion__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>BCB Platform Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Tag_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Tag__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>BCB Platform Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Tag_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Tag__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>BCB Platform Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Mall_Product_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Offering__c</pageOrSobjectType>
        <recordType>Offering__c.Service</recordType>
        <type>Flexipage</type>
        <profile>BCB Platform Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Mall_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Offering__c</pageOrSobjectType>
        <recordType>Offering__c.Service</recordType>
        <type>Flexipage</type>
        <profile>BCB Platform Administrator</profile>
    </profileActionOverrides>
    <tabs>Tag__c</tabs>
    <tabs>standard-Account</tabs>
    <tabs>Offering__c</tabs>
    <tabs>Promotion__c</tabs>
    <tabs>Business_Event__c</tabs>
    <tabs>Success_Story__c</tabs>
    <tabs>Provider__c</tabs>
    <tabs>Knowledge__kav</tabs>
    <tabs>Link__c</tabs>
    <tabs>Content_Unit_Translation__c</tabs>
    <tabs>Publishing_Unit__c</tabs>
    <uiType>Lightning</uiType>
    <utilityBar>SBG_Mall_UtilityBar</utilityBar>
</CustomApplication>
