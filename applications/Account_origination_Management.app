<?xml version="1.0" encoding="UTF-8"?>
<CustomApplication xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Application_Record_Page</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>AOB_Application__c</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Application_Record_Page</content>
        <formFactor>Small</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>AOB_Application__c</pageOrSobjectType>
    </actionOverrides>
    <brand>
        <headerColor>#ED0303</headerColor>
        <logo>standard_bank_logo</logo>
        <logoVersion>1</logoVersion>
        <shouldOverrideOrgTheme>false</shouldOverrideOrgTheme>
    </brand>
    <formFactors>Small</formFactors>
    <formFactors>Large</formFactors>
    <isNavAutoTempTabsDisabled>false</isNavAutoTempTabsDisabled>
    <isNavPersonalizationDisabled>false</isNavPersonalizationDisabled>
    <isNavTabPersistenceDisabled>false</isNavTabPersistenceDisabled>
    <isOmniPinnedViewEnabled>false</isOmniPinnedViewEnabled>
    <label>Account origination Management</label>
    <navType>Standard</navType>
    <tabs>AOB_Application__c</tabs>
    <tabs>standard-Product2</tabs>
    <tabs>standard-report</tabs>
    <tabs>standard-Case</tabs>
    <tabs>AOB_FlowScreen__c</tabs>
    <tabs>AOB_ScreenSection__c</tabs>
    <tabs>AOB_Field__c</tabs>
    <uiType>Lightning</uiType>
    <utilityBar>Account_origination_Management_UtilityBar</utilityBar>
</CustomApplication>
