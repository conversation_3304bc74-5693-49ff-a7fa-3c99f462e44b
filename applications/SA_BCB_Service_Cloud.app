<?xml version="1.0" encoding="UTF-8"?>
<CustomApplication xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>SVC_3ColumnOpenCaseRecordpage</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Case</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>SVC_3ColumnOpenCaseRecordpage</content>
        <formFactor>Small</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Case</pageOrSobjectType>
    </actionOverrides>
    <brand>
        <headerColor>#0070D2</headerColor>
        <logo>standard_bank_logo</logo>
        <logoVersion>1</logoVersion>
        <shouldOverrideOrgTheme>false</shouldOverrideOrgTheme>
    </brand>
    <description>SFP-46730 - Service Management application for SA BCB Client Service users.</description>
    <formFactors>Small</formFactors>
    <formFactors>Large</formFactors>
    <isNavAutoTempTabsDisabled>false</isNavAutoTempTabsDisabled>
    <isNavPersonalizationDisabled>false</isNavPersonalizationDisabled>
    <isNavTabPersistenceDisabled>false</isNavTabPersistenceDisabled>
    <isOmniPinnedViewEnabled>false</isOmniPinnedViewEnabled>
    <label>SA BCB Service Cloud</label>
    <navType>Console</navType>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>SVC_3ColumnOpenCaseRecordpage</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>BCB SA Minimum Access Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>SVC_3ColumnOpenCaseRecordpage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>BCB SA Minimum Access Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>SVC_3ColumnOpenCaseRecordpage</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>SVC_3ColumnOpenCaseRecordpage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>SVC_3ColumnOpenCaseRecordpage</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>BCC Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>SVC_3ColumnOpenCaseRecordpage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>BCC Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>SVC_3ColumnOpenCaseRecordpage</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>SVC_3ColumnOpenCaseRecordpage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_Closed_LEX</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Closed_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_Closed_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Closed_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_Closed_LEX</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Closed_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>BCB SA Minimum Access Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_Closed_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Closed_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>BCB SA Minimum Access Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_Closed_LEX</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Closed_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>BCC Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_Closed_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Closed_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>BCC Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>SVC_3ColumnOpenCaseRecordpage</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Personal Business Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>SVC_3ColumnOpenCaseRecordpage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Personal Business Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_Closed_LEX</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Closed_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Personal Business Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_Closed_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Closed_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Personal Business Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <setupExperience>service</setupExperience>
    <tabs>standard-home</tabs>
    <tabs>standard-Case</tabs>
    <tabs>standard-Account</tabs>
    <tabs>standard-Contact</tabs>
    <tabs>standard-OmniSupervisorLightning</tabs>
    <tabs>Knowledge__kav</tabs>
    <tabs>Service_Type__c</tabs>
    <uiType>Lightning</uiType>
    <utilityBar>SA_BCB_Service_Cloud_UtilityBar</utilityBar>
    <workspaceConfig>
        <mappings>
            <tab>Knowledge__kav</tab>
        </mappings>
        <mappings>
            <tab>Service_Type__c</tab>
        </mappings>
        <mappings>
            <tab>standard-Account</tab>
        </mappings>
        <mappings>
            <tab>standard-Case</tab>
        </mappings>
        <mappings>
            <fieldName>AccountId</fieldName>
            <tab>standard-Contact</tab>
        </mappings>
        <mappings>
            <tab>standard-OmniSupervisorLightning</tab>
        </mappings>
        <mappings>
            <tab>standard-home</tab>
        </mappings>
    </workspaceConfig>
</CustomApplication>
