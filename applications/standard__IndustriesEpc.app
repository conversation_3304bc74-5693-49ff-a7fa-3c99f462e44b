<?xml version="1.0" encoding="UTF-8"?>
<CustomApplication xmlns="http://soap.sforce.com/2006/04/metadata">
    <formFactors>Large</formFactors>
    <isNavAutoTempTabsDisabled>false</isNavAutoTempTabsDisabled>
    <isNavPersonalizationDisabled>false</isNavPersonalizationDisabled>
    <isNavTabPersistenceDisabled>false</isNavTabPersistenceDisabled>
    <isOmniPinnedViewEnabled>false</isOmniPinnedViewEnabled>
    <label>IndustriesEpc</label>
    <navType>Console</navType>
    <tabs>standard-home</tabs>
    <tabs>standard-Product2</tabs>
    <tabs>standard-ProductClassification</tabs>
    <tabs>standard-ProductSellingModel</tabs>
    <tabs>standard-AttributeDefinition</tabs>
    <tabs>standard-AttributeCategory</tabs>
    <tabs>standard-AttributePicklist</tabs>
    <tabs>standard-ProductCatalog</tabs>
    <tabs>standard-ProductCategory</tabs>
    <tabs>standard-QualificationProcedure</tabs>
    <tabs>standard-ProductConfigurationRule</tabs>
    <uiType>Lightning</uiType>
    <workspaceConfig>
        <mappings>
            <tab>standard-AttributeCategory</tab>
        </mappings>
        <mappings>
            <tab>standard-AttributeDefinition</tab>
        </mappings>
        <mappings>
            <tab>standard-AttributePicklist</tab>
        </mappings>
        <mappings>
            <tab>standard-Product2</tab>
        </mappings>
        <mappings>
            <tab>standard-ProductCatalog</tab>
        </mappings>
        <mappings>
            <tab>standard-ProductCategory</tab>
        </mappings>
        <mappings>
            <tab>standard-ProductClassification</tab>
        </mappings>
        <mappings>
            <tab>standard-ProductConfigurationRule</tab>
        </mappings>
        <mappings>
            <tab>standard-ProductSellingModel</tab>
        </mappings>
        <mappings>
            <tab>standard-QualificationProcedure</tab>
        </mappings>
        <mappings>
            <tab>standard-home</tab>
        </mappings>
    </workspaceConfig>
</CustomApplication>
