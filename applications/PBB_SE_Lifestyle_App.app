<?xml version="1.0" encoding="UTF-8"?>
<CustomApplication xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Conversation_Record_Page</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Conversation__c</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Account_Information_Record_Page</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Account_Information__c</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override updated by Lightning App Builder during activation.</comment>
        <content>PBB_Lifestyie_Client_Page</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Account</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <content>PBB_Lifestyle_Home_Page</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
    </actionOverrides>
    <brand>
        <headerColor>#0070D2</headerColor>
        <shouldOverrideOrgTheme>false</shouldOverrideOrgTheme>
    </brand>
    <description>Lightning app for Personal Business Banking Lifestyle User</description>
    <formFactors>Large</formFactors>
    <isNavAutoTempTabsDisabled>false</isNavAutoTempTabsDisabled>
    <isNavPersonalizationDisabled>false</isNavPersonalizationDisabled>
    <isNavTabPersistenceDisabled>false</isNavTabPersistenceDisabled>
    <isOmniPinnedViewEnabled>false</isOmniPinnedViewEnabled>
    <label>Enterprise Banking</label>
    <navType>Console</navType>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Conversation_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Conversation__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Conversation_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Conversation__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Conversation_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Conversation__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Personal Business Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Personal Business Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_Closed_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Closed_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Personal Business Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AOB_Staff_Assisted_Application</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AOB_Application__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Application_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AOB_Application__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>BCC Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AOB_Staff_Assisted_Application</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AOB_Application__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Personal Business Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>BCB_SA_Opportunity_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.BCB_SA_Opportunity_Path</recordType>
        <type>Flexipage</type>
        <profile>BCC Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>BCB_SA_Opportunity_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.BCB_SA_Opportunity_Path</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>BCB_SA_Opportunity_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.BCB_SA_Opportunity_Path</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>BCB_SA_Opportunity_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.BCB_SA_Opportunity_Path</recordType>
        <type>Flexipage</type>
        <profile>Personal Business Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>BCB_SA_Opportunity_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.BCB_SA_Opportunity_Path</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Event_Report_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <recordType>Call_Report__c.BCB_SA_Meeting</recordType>
        <type>Flexipage</type>
        <profile>BCC Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Event_Report_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <recordType>Call_Report__c.BCB_SA_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Event_Report_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <recordType>Call_Report__c.BCB_SA_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Event_Report_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <recordType>Call_Report__c.BCB_SA_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Event_Report_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <recordType>Call_Report__c.BCB_SA_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Personal Business Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AOB_Staff_Assisted_Application</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AOB_Application__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>BCB_Customer_Onboarding_Application</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AOB_Application__c</pageOrSobjectType>
        <recordType>AOB_Application__c.Customer_Onboarding</recordType>
        <type>Flexipage</type>
        <profile>BCC Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>BCB_Customer_Onboarding_Application</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AOB_Application__c</pageOrSobjectType>
        <recordType>AOB_Application__c.Customer_Onboarding</recordType>
        <type>Flexipage</type>
        <profile>Personal Business Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <tabs>standard-home</tabs>
    <tabs>standard-Account</tabs>
    <tabs>Conversation__c</tabs>
    <tabs>standard-Case</tabs>
    <tabs>AOB_Application__c</tabs>
    <uiType>Lightning</uiType>
    <utilityBar>SE_Lifestyle_App_UtilityBar</utilityBar>
    <workspaceConfig>
        <mappings>
            <tab>AOB_Application__c</tab>
        </mappings>
        <mappings>
            <fieldName>Client__c</fieldName>
            <tab>Conversation__c</tab>
        </mappings>
        <mappings>
            <fieldName>SingletrackREV__Account_Budgets__c</fieldName>
            <tab>standard-Account</tab>
        </mappings>
        <mappings>
            <fieldName>AccountId</fieldName>
            <tab>standard-Case</tab>
        </mappings>
        <mappings>
            <tab>standard-home</tab>
        </mappings>
    </workspaceConfig>
</CustomApplication>
