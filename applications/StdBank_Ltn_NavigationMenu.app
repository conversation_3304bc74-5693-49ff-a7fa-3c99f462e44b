<?xml version="1.0" encoding="UTF-8"?>
<CustomApplication xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Business_Assessment_Record_Page</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <content>Client_Coverage_Home_Page</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
    </actionOverrides>
    <brand>
        <headerColor>#0070D2</headerColor>
        <logo>sgfd7</logo>
        <logoVersion>1</logoVersion>
        <shouldOverrideOrgTheme>false</shouldOverrideOrgTheme>
    </brand>
    <description>Custom view of the tabs you see on the CRM platform, specific to Lightning Experience (LEX)</description>
    <formFactors>Large</formFactors>
    <isNavAutoTempTabsDisabled>false</isNavAutoTempTabsDisabled>
    <isNavPersonalizationDisabled>false</isNavPersonalizationDisabled>
    <isNavTabPersistenceDisabled>false</isNavTabPersistenceDisabled>
    <isOmniPinnedViewEnabled>false</isOmniPinnedViewEnabled>
    <label>Standard Bank</label>
    <navType>Standard</navType>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Lending_deposits_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Lending_Deposits</recordType>
        <type>Flexipage</type>
        <profile>CRM Analytics</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_CommB_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_CommB</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.Business_Assessment</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.Business_Assessment_CommB</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Advisory_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Advisory</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Insurance</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CIB_GM_Single_Risk</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.CIBGM_Single_Risk</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CIB_GM_Multi_Risk</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.CIBGM_Multi_Risk</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Transactional</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Meeting_Console</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <recordType>Call_Report__c.NBAC_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_CommB_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_CommB_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_CommB_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_CommB_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture_READONLY</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>Customer Community Plus User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Contact_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Contact</pageOrSobjectType>
        <recordType>Contact.SA_Bank_Contact_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>Company Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>Customer Community Plus User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Company Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture_READONLY</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture_READONLY</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>System Administrator - Premier Support</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Transactional_2014_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Transactional_New</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights_CRM_Analytics</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesHousehold</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights_CRM_Analytics</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Customer Community Plus User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Captial_markets_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Capital_Markets</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>CFX Live Account Application Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Transactional_2014_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Transactional_New</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>CFX Practice Account Application Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Transactional</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Meeting_Console</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <recordType>Call_Report__c.NBAC_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Transactional</recordType>
        <type>Flexipage</type>
        <profile>Api User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Transactional</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>Chatter Moderator User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture_READONLY</recordType>
        <type>Flexipage</type>
        <profile>System Administrator - Premier Support</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Insurance</recordType>
        <type>Flexipage</type>
        <profile>System Administrator - Premier Support</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Guided_Onboarding</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>MarketingProfile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Transactional_2014_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Transactional_New</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Guest License User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Onboarding API</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Premier Support User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Security User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>API Only User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Chatter Moderator User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>Custom Read Only Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Credit Credit Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Premier Support User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>MarketingProfile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Onboarding API</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Consultant</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Custom Marketing User Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Custom Read Only Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Force.com - Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Guest License User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Standard Bank Employee Community</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>Onboarding API</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Consultant</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>Premier Support User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>Standard Bank Employee Community</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>StandardAul</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>System Administrator - Premier Support</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>Api User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Force.com - Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>System Administrator - Premier Support</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Onboarding API</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Standard Bank Employee Community</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>StandardAul</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Customer Community Plus User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>TPS_2022_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.TPS_2022</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>TPS_2022_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.TPS_2022</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Premier Support User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>Onboarding API</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>MarketingProfile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>Premier Support User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>Onboarding API</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>API Only User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Custom Read Only Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Custom KYC User Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Security User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>System Administrator - Premier Support</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture_READONLY</recordType>
        <type>Flexipage</type>
        <profile>CFX Live Account Application Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Api User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>Standard Bank Employee Community</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>API Only User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture_READONLY</recordType>
        <type>Flexipage</type>
        <profile>Chatter External User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture_READONLY</recordType>
        <type>Flexipage</type>
        <profile>Chatter Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture_READONLY</recordType>
        <type>Flexipage</type>
        <profile>Chatter Moderator User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture_READONLY</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture_READONLY</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture_READONLY</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture_READONLY</recordType>
        <type>Flexipage</type>
        <profile>Customer Community Plus User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CIB_GM_Multi_Risk</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.CIBGM_Multi_Risk</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CIB_GM_Multi_Risk</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.CIBGM_Multi_Risk</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CIB_GM_Multi_Risk</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.CIBGM_Multi_Risk</recordType>
        <type>Flexipage</type>
        <profile>Api User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture_READONLY</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Custom Marketing User Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture_READONLY</recordType>
        <type>Flexipage</type>
        <profile>Credit Credit Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Customer Community Plus User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Credit Credit Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Captial_markets_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Capital_Markets</recordType>
        <type>Flexipage</type>
        <profile>ReadOnly</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Captial_markets_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Capital_Markets</recordType>
        <type>Flexipage</type>
        <profile>Onboarding API</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Captial_markets_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Capital_Markets</recordType>
        <type>Flexipage</type>
        <profile>Partner Relationship Management Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Captial_markets_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Capital_Markets</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights_CRM_Analytics</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Individual_Service_Client</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights_CRM_Analytics</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesInstitution</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights_CRM_Analytics</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights_CRM_Analytics</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>Credit Credit Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>API Only User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Credit Credit Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>Credit Credit Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Commercial_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Contact_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Contact</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Contact_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Contact</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Custom KYC User Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>Api User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>TPS_2022_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.TPS_2022</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>TPS_2022_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.TPS_2022</recordType>
        <type>Flexipage</type>
        <profile>Api User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>TPS_2022_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.TPS_2022</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Consultant</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Premier Support User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Consultant</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>TPS_2022_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.TPS_2022</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Chatter Moderator User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>CFX Live Account Application Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>CFX Practice Account Application Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>Chatter External User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>Chatter Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Product_Distribution_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Product_Distribution__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Security User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Product_Distribution_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Product_Distribution__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Api User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Chatter Moderator User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Guided_Onboarding</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Chatter Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Customer Community Plus User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Force.com - Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>TPS_2022_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.TPS_2022</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>StandardAul</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Product_Distribution_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Product_Distribution__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>CFX Live Account Application Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture_READONLY</recordType>
        <type>Flexipage</type>
        <profile>Force.com - Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Security User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>CFX Live Account Application Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>CFX Practice Account Application Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Custom Marketing User Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Custom Read Only Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>Tab</actionName>
        <content>BCC_Home_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>BCC Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>BCC_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Commercial_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>BCC Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>BCC_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Commercial_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>Standard Bank Employee Community</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>BCC_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Commercial_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>StandardAul</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Advisory_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Advisory</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Advisory_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Advisory</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Advisory_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Advisory</recordType>
        <type>Flexipage</type>
        <profile>System Administrator - Premier Support</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights_CRM_Analytics</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect_Partner</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights_CRM_Analytics</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights_CRM_Analytics</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights_CRM_Analytics</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Consultant</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Consultant</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Guided_Onboarding</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Guided_Onboarding</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Guided_Onboarding</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Guided_Onboarding</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>MarketingProfile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>Custom Read Only Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Security User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>API Only User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Guided_Onboarding</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Premier Support User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Chatter Moderator User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Chatter Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Force.com - Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Guest License User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>MarketingProfile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Guided_Onboarding</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>System Administrator - Premier Support</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Commercial_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Customer Community Plus User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Force.com - Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Guest License User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Insurance</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Insurance</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Insurance</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>Standard Bank Employee Community</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Custom KYC User Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Force.com - Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>Custom Marketing User Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture_READONLY</recordType>
        <type>Flexipage</type>
        <profile>Api User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture_READONLY</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture_READONLY</recordType>
        <type>Flexipage</type>
        <profile>CFX Practice Account Application Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture_READONLY</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>Api User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>Chatter Moderator User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Security User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Security User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_Closed_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Closed_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Primary_Arranging_Advisory_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Primary_Arranging_Advisory</recordType>
        <type>Flexipage</type>
        <profile>Custom Read Only Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Primary_Arranging_Advisory_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Primary_Arranging_Advisory</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Primary_Arranging_Advisory_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Primary_Arranging_Advisory</recordType>
        <type>Flexipage</type>
        <profile>Onboarding API</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Advisory_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Advisory</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Captial_markets_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Capital_Markets</recordType>
        <type>Flexipage</type>
        <profile>BCC AR Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Product_Distribution_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Product_Distribution__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>System Administrator - Premier Support</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Chatter Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Change_Request</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Company Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>Standard Bank Employee Community</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>MarketingProfile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Api User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>API Only User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Meeting_Console</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <recordType>Call_Report__c.NBAC_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Meeting_Console</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <recordType>Call_Report__c.NBAC_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Meeting_Console</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <recordType>Call_Report__c.NBAC_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Joint_Venture_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Consultant</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>Customer Community Plus User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>StandardAul</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>Guest License User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>Custom KYC User Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>Custom Read Only Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Consultant</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Security User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>Tab</actionName>
        <content>Client_Coverage_Executive_Home_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CIB_GM_Multi_Risk</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.CIBGM_Multi_Risk</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>Tab</actionName>
        <content>Client_Coverage_Home_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Transactional</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Transactional</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CIB_GM_Single_Risk</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.CIBGM_Single_Risk</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CIB_GM_Single_Risk</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.CIBGM_Single_Risk</recordType>
        <type>Flexipage</type>
        <profile>Api User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CIB_GM_Single_Risk</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.CIBGM_Single_Risk</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CIB_GM_Single_Risk</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.CIBGM_Single_Risk</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CIB_GM_Multi_Risk</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.CIBGM_Multi_Risk</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CIB_GM_Multi_Risk</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.CIBGM_Multi_Risk</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Transactional</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>Tab</actionName>
        <content>Lightning_Generic_Home_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Transactional</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Lending_deposits_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Lending_Deposits</recordType>
        <type>Flexipage</type>
        <profile>Api User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>Tab</actionName>
        <content>Credit_Credit_Analyst_Home_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Credit Credit Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_PBB_ROA_Entity_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>PBB_ROA_Entity__c</pageOrSobjectType>
        <recordType>PBB_ROA_Entity__c.SME_Client</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>StandardAul</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_PBB_ROA_Entity_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>PBB_ROA_Entity__c</pageOrSobjectType>
        <recordType>PBB_ROA_Entity__c.Commercial_Client</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_PBB_ROA_Entity_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>PBB_ROA_Entity__c</pageOrSobjectType>
        <recordType>PBB_ROA_Entity__c.Commercial_Client</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_PBB_ROA_Entity_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>PBB_ROA_Entity__c</pageOrSobjectType>
        <recordType>PBB_ROA_Entity__c.SME_Client</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Customer Community Plus User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>Force.com - Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>API Only User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Api User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Advisory_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Advisory</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Guided_Onboarding</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>System Administrator - Premier Support</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>Premier Support User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Security User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Custom Marketing User Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Chatter External User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_Closed_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Closed_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Primary_Arranging_Advisory_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Primary_Arranging_Advisory</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_Closed_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Closed_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_Closed_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Closed_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_Closed_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Closed_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_Closed_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Closed_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>Chatter Moderator User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Primary_Arranging_Advisory_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Primary_Arranging_Advisory</recordType>
        <type>Flexipage</type>
        <profile>Api User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>MarketingProfile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>Custom Read Only Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Production_Incident</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.User_Case</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.User_Case</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>CFX Live Account Application Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>CFX Practice Account Application Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Security User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Chatter Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Change_Request</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Client_Case</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>Custom Marketing User Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>Chatter Moderator User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>Company Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>Chatter External User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Consultant</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Chatter Moderator User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Company Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Custom KYC User Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Lending_deposits_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Lending_Deposits</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Lending_deposits_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Lending_Deposits</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>CFX Practice Account Application Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Chatter External User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>Force.com - Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>StandardAul</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Lending_deposits_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Lending_Deposits</recordType>
        <type>Flexipage</type>
        <profile>Custom Read Only Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Consultant</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>CFX Live Account Application Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>System Administrator - Premier Support</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Joint_Venture_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Joint_Venture_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>StandardAul</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Onboarding API</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Guest License User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>Custom KYC User Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>Company Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>Credit Credit Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>MarketingProfile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Onboarding API</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>CFX Practice Account Application Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>CFX Live Account Application Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture_READONLY</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture_READONLY</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture_READONLY</recordType>
        <type>Flexipage</type>
        <profile>Standard Bank Employee Community</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Joint_Venture_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>Onboarding API</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture_READONLY</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>Custom Marketing User Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Joint_Venture_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>API Only User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>Customer Community Plus User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>CFX Live Account Application Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>Force.com - Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>CFX Live Account Application Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>CFX Practice Account Application Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>Chatter External User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>Chatter External User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>Chatter Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>Chatter Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>Guest License User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>MarketingProfile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Company Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Credit Credit Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>StandardAul</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>Tab</actionName>
        <content>CommB_RM_Home_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Lending_deposits_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Lending_Deposits</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Lending_deposits_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Lending_Deposits</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Lending_deposits_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Lending_Deposits</recordType>
        <type>Flexipage</type>
        <profile>Onboarding API</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Lending_deposits_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Lending_Deposits</recordType>
        <type>Flexipage</type>
        <profile>Partner Relationship Management Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Lending_deposits_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Lending_Deposits</recordType>
        <type>Flexipage</type>
        <profile>ReadOnly</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Primary_Arranging_Advisory_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Primary_Arranging_Advisory</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Primary_Arranging_Advisory_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Primary_Arranging_Advisory</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Product_Distribution_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Product_Distribution__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>CFX Practice Account Application Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Primary_Arranging_Advisory_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Primary_Arranging_Advisory</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Company Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Custom KYC User Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Custom Marketing User Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Product_Distribution_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Product_Distribution__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>API Only User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Standard Bank Employee Community</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Chatter External User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>CFX Live Account Application Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>CFX Practice Account Application Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Client_Case</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Chatter External User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>Onboarding API</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>Premier Support User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Premier Support User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>System Administrator - Premier Support</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>Api User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>API Only User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>CFX Live Account Application Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Custom Read Only Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Contact_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Contact</pageOrSobjectType>
        <recordType>Contact.SA_Client_Contact_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Contact_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Contact</pageOrSobjectType>
        <recordType>Contact.SA_Client_Contact_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Custom Marketing User Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture_READONLY</recordType>
        <type>Flexipage</type>
        <profile>Company Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture_READONLY</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Product_Distribution_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Product_Distribution__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Chatter External User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>CFX Practice Account Application Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Product_Distribution_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Product_Distribution__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Lending_deposits_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Lending_Deposits</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Chatter Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Chatter Moderator User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Company Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Premier Support User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Captial_markets_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Capital_Markets</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Security User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Captial_markets_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Capital_Markets</recordType>
        <type>Flexipage</type>
        <profile>API Only User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Custom Marketing User Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Guided_Onboarding</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Standard Bank Employee Community</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>StandardAul</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Guided_Onboarding</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>System Administrator - Premier Support</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Premier Support User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>CFX Practice Account Application Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>MarketingProfile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Standard Bank Employee Community</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>Custom KYC User Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Custom KYC User Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Chatter External User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Chatter Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Security User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>API Only User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Api User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Security User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Standard Bank Employee Community</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>MarketingProfile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>Customer Community Plus User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Guest License User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>Premier Support User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Contact_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Contact</pageOrSobjectType>
        <recordType>Contact.SA_Bank_Contact_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>Chatter Moderator User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Security User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Api User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>System Administrator - Premier Support</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Meeting_Console</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <recordType>Call_Report__c.NBAC_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Consultant</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture_READONLY</recordType>
        <type>Flexipage</type>
        <profile>Custom KYC User Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture_READONLY</recordType>
        <type>Flexipage</type>
        <profile>Custom Marketing User Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Meeting_Console</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <recordType>Call_Report__c.NBAC_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Custom KYC User Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Security User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>Credit Credit Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Captial_markets_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Capital_Markets</recordType>
        <type>Flexipage</type>
        <profile>CST Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Captial_markets_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Capital_Markets</recordType>
        <type>Flexipage</type>
        <profile>Custom Read Only Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Captial_markets_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Capital_Markets</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>Credit Credit Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Chatter External User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Api User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>Standard Bank Employee Community</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Lending_deposits_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Lending_Deposits</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>CFX Practice Account Application Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>Custom Marketing User Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>Force.com - Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Custom KYC User Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Credit Credit Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>CFX Live Account Application Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>Premier Support User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Credit Credit Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_PBB_ROA_Entity_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>PBB_ROA_Entity__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_PBB_ROA_Entity_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>PBB_ROA_Entity__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Chatter Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Guided_Onboarding</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Company Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>Tab</actionName>
        <content>CommB_CSU_Home_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>Tab</actionName>
        <content>CommB_RM_Home_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>StandardAul</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>CFX Live Account Application Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Transactional_2014_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Transactional_New</recordType>
        <type>Flexipage</type>
        <profile>Api User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Transactional_2014_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Transactional_New</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Transactional_2014_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Transactional_New</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CIB_GM_Multi_Risk</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.CIBGM_Multi_Risk</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Consultant</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Force.com - Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Guest License User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>Guest License User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Force.com - Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Captial_markets_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Capital_Markets</recordType>
        <type>Flexipage</type>
        <profile>Credit Credit Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>API Only User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Lending_deposits_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Lending_Deposits</recordType>
        <type>Flexipage</type>
        <profile>Credit Credit Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Primary_Arranging_Advisory_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Primary_Arranging_Advisory</recordType>
        <type>Flexipage</type>
        <profile>Credit Credit Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Captial_markets_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Capital_Markets</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Lending_deposits_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Lending_Deposits</recordType>
        <type>Flexipage</type>
        <profile>CST Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>Tab</actionName>
        <content>Wholesale_Client_Coverage_Homepage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights_CRM_Analytics</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights_CRM_Analytics</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights_CRM_Analytics</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Guided_Onboarding</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>Company Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>Guest License User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Force.com - Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Guest License User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>Custom Marketing User Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>Chatter Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Chatter Moderator User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Api User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Customer Community Plus User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Standard Bank Employee Community</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture_READONLY</recordType>
        <type>Flexipage</type>
        <profile>Custom Read Only Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>Company Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>Company Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Primary_Arranging_Advisory_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Primary_Arranging_Advisory</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Primary_Arranging_Advisory_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Primary_Arranging_Advisory</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CIB_GM_Single_Risk</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.CIBGM_Single_Risk</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CIB_GM_Single_Risk</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.CIBGM_Single_Risk</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Customer Community Plus User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CIB_GM_Single_Risk</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.CIBGM_Single_Risk</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Captial_markets_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Capital_Markets</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Captial_markets_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Capital_Markets</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_CommB_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Lending_deposits_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Lending_Deposits</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Chatter Moderator User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Chatter Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_CommB_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Company Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Custom Marketing User Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Contact_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Contact</pageOrSobjectType>
        <recordType>Contact.Inactive_Client_Contact_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>System Administrator - Premier Support</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Contact_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Contact</pageOrSobjectType>
        <recordType>Contact.Inactive_Client_Contact_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Custom Read Only Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>Custom KYC User Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>System Administrator - Premier Support</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Premier Support User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Standard Bank Employee Community</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>StandardAul</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>Onboarding API</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture_READONLY</recordType>
        <type>Flexipage</type>
        <profile>Premier Support User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>System Administrator - Premier Support</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>Chatter Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>Guest License User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture_READONLY</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Security User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>CFX Practice Account Application Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>Chatter External User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>Chatter Moderator User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>MarketingProfile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Security User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Chatter External User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>Api User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>API Only User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Lending_deposits_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Lending_Deposits</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Security User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Lending_deposits_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Lending_Deposits</recordType>
        <type>Flexipage</type>
        <profile>API Only User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Transactional_2014_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Transactional_New</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>Custom KYC User Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>Custom KYC User Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Consultant</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Security User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Guided_Onboarding</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>System Administrator - Premier Support</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Guided_Onboarding</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Standard Bank Employee Community</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>Custom Read Only Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>StandardAul</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Primary_Arranging_Advisory_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Primary_Arranging_Advisory</recordType>
        <type>Flexipage</type>
        <profile>BCC AR Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Lending_deposits_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Lending_Deposits</recordType>
        <type>Flexipage</type>
        <profile>BCC AR Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Meeting_Console</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <recordType>Call_Report__c.NBAC_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Api User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>API Only User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>Force.com - Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Credit Credit Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Credit Credit Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Custom KYC User Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Custom Read Only Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Captial_markets_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Capital_Markets</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Captial_markets_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Capital_Markets</recordType>
        <type>Flexipage</type>
        <profile>Api User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Captial_markets_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Capital_Markets</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>TPS_2022_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.TPS_2022</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Risk_Intelligence</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Risk_Intelligence</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Risk_Intelligence</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Risk_Intelligence</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Risk_Intelligence</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Risk_Intelligence</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>CRM Analytics</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Transactional_2014_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Transactional_New</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Primary_Arranging_Advisory_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Primary_Arranging_Advisory</recordType>
        <type>Flexipage</type>
        <profile>Partner Relationship Management Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Primary_Arranging_Advisory_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Primary_Arranging_Advisory</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Primary_Arranging_Advisory_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Primary_Arranging_Advisory</recordType>
        <type>Flexipage</type>
        <profile>ReadOnly</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture_READONLY</recordType>
        <type>Flexipage</type>
        <profile>Guest License User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>CRM Analytics</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>CRM Analytics</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>CRM Analytics</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>CRM Analytics</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>CRM Analytics</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Primary_Arranging_Advisory_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Primary_Arranging_Advisory</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Security User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Primary_Arranging_Advisory_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Primary_Arranging_Advisory</recordType>
        <type>Flexipage</type>
        <profile>API Only User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Primary_Arranging_Advisory_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Primary_Arranging_Advisory</recordType>
        <type>Flexipage</type>
        <profile>CST Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture_READONLY</recordType>
        <type>Flexipage</type>
        <profile>MarketingProfile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>Tab</actionName>
        <content>Client_Coverage_Home_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>Tab</actionName>
        <content>Client_Coverage_Home_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>Guest License User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>Customer Community Plus User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Customer Community Plus User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>Force.com - Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>MarketingProfile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Custom Read Only Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Consultant</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Company Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Credit Credit Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Advisory_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Advisory</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Custom KYC User Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Product_Distribution_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Product_Distribution__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights_CRM_Analytics</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Api User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>Chatter External User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>Chatter Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>Custom Marketing User Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>Custom Read Only Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>System Administrator - Premier Support</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights_CRM_Analytics</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Commercial_Banking_Individual_Client</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights_CRM_Analytics</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Commercial_Banking_Africa_Regions</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights_CRM_Analytics</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights_CRM_Analytics</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Commercial_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights_CRM_Analytics</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Ecosystem</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights_CRM_Analytics</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights_CRM_Analytics</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Onboarded_Partner</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Event_Report_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <recordType>Call_Report__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Event_Report_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Event_Report_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Onboarding API</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Event_Report_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <recordType>Call_Report__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Guided_Onboarding</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Guided_Onboarding</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_CommB_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Custom Read Only Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>MarketingProfile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>CFX Live Account Application Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>CFX Practice Account Application Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Chatter External User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>MarketingProfile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Guest License User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights_CRM_Analytics</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights_CRM_Analytics</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Chatter Moderator User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Onboarding API</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Meeting_Console</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <recordType>Call_Report__c.NBAC_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture</recordType>
        <type>Flexipage</type>
        <profile>Credit Credit Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Meeting_Console</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <recordType>Call_Report__c.NBAC_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Custom Read Only Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Meeting_Console</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <recordType>Call_Report__c.NBAC_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Meeting_Console</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <recordType>Call_Report__c.NBAC_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Meeting_Console</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <recordType>Call_Report__c.NBAC_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_CommB</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_CommB</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_CommB</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Assessment_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.Business_Assessment_CommB</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Chatter Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>CFX Live Account Application Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>CFX Practice Account Application Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Prospect_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>Api User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Product_Distribution_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Product_Distribution__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights_CRM_Analytics</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Enterprise_Banking_Africa_Regions</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.CIB_Customer_Query</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.CIB_Customer_Query</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.CommB_Client_Case</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.CommB_Client_Case</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Production_Incident</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Lending_deposits_Product_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>SB_Product__c</pageOrSobjectType>
        <recordType>SB_Product__c.Lending_Deposits</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC_Joint_Venture_READONLY</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Custom Marketing User Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Custom Read Only Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>BCC Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_Closed_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Closed_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>BCC Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>BCB_SA_Opportunity_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.BCB_SA_Opportunity_Path</recordType>
        <type>Flexipage</type>
        <profile>BCC Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>BCB_SA_Opportunity_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.BCB_SA_Opportunity_Path</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>BCB_SA_Opportunity_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.BCB_SA_Opportunity_Path</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>BCB_SA_Opportunity_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.BCB_SA_Opportunity_Path</recordType>
        <type>Flexipage</type>
        <profile>Personal Business Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>BCB_SA_Opportunity_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.BCB_SA_Opportunity_Path</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Event_Report_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <recordType>Call_Report__c.BCB_SA_Meeting</recordType>
        <type>Flexipage</type>
        <profile>BCC Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Event_Report_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <recordType>Call_Report__c.BCB_SA_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Event_Report_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <recordType>Call_Report__c.BCB_SA_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Event_Report_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <recordType>Call_Report__c.BCB_SA_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Event_Report_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <recordType>Call_Report__c.BCB_SA_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Personal Business Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Enhanced_CST_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.Enhanced_CST</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Enhanced_CST_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.Enhanced_CST</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Enhanced_CST_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.Enhanced_CST</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Enhanced_CST_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.Enhanced_CST</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Security User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Enhanced_CST_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.Enhanced_CST</recordType>
        <type>Flexipage</type>
        <profile>API Only User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Enhanced_CST_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.Enhanced_CST</recordType>
        <type>Flexipage</type>
        <profile>Api User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Enhanced_CST_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.Enhanced_CST</recordType>
        <type>Flexipage</type>
        <profile>Credit Credit Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Enhanced_CST_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.Enhanced_CST</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Enhanced_CST_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.Enhanced_CST</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Enhanced_CST_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.Enhanced_CST</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Enhanced_CST_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.Enhanced_CST</recordType>
        <type>Flexipage</type>
        <profile>System Administrator - Premier Support</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Enhanced_CST_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.Enhanced_CST</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Enhanced_CST_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.Enhanced_CST</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Enhanced_CST_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.Enhanced_CST</recordType>
        <type>Flexipage</type>
        <profile>Premier Support User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Enhanced_CST_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.Enhanced_CST</recordType>
        <type>Flexipage</type>
        <profile>Personal Business Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Enhanced_CST_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.Enhanced_CST_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Enhanced_CST_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.Enhanced_CST_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Enhanced_CST_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.Enhanced_CST_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Enhanced_CST_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.Enhanced_CST_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Enhanced_CST_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.Enhanced_CST_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Enhanced_CST_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.Enhanced_CST</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Enhanced_CST_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.Enhanced_CST_Read_Only</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <tabs>standard-home</tabs>
    <tabs>Power_BI_Summary</tabs>
    <tabs>standard-Feed</tabs>
    <tabs>standard-Account</tabs>
    <tabs>PBB_ROA_Entity__c</tabs>
    <tabs>standard-Contact</tabs>
    <tabs>standard-Opportunity</tabs>
    <tabs>standard-Case</tabs>
    <tabs>Call_Report__c</tabs>
    <tabs>standard-Campaign</tabs>
    <tabs>standard-report</tabs>
    <tabs>standard-Dashboard</tabs>
    <tabs>standard-File</tabs>
    <tabs>QuickSilver</tabs>
    <tabs>Concession__c</tabs>
    <tabs>Ecosystem__c</tabs>
    <tabs>Product_Distribution__c</tabs>
    <tabs>Akili_Insights</tabs>
    <uiType>Lightning</uiType>
    <utilityBar>Standard_Bank_UtilityBar1</utilityBar>
</CustomApplication>
