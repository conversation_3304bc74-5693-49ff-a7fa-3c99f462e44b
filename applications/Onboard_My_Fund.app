<?xml version="1.0" encoding="UTF-8"?>
<CustomApplication xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override updated by Lightning App Builder during activation.</comment>
        <content>Fund_to_Agreement_Record_Page</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>OMF_FundToAgreement__c</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override updated by Lightning App Builder during activation.</comment>
        <content>Fund_to_Agreement_Record_Page</content>
        <formFactor>Small</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>OMF_FundToAgreement__c</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Managed_Fund_Rule_Record_Page</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>OMF_ManagedFundRules__c</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Managed_Fund_Rule_Record_Page</content>
        <formFactor>Small</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>OMF_ManagedFundRules__c</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Managed_Fund_Status_Record_Page</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>OMF_ManagedFundStatus__c</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Managed_Fund_Status_Record_Page</content>
        <formFactor>Small</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>OMF_ManagedFundStatus__c</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override updated by Lightning App Builder during activation.</comment>
        <content>Single_Onboarding_Record_Page</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>OMF_ManagedFund__c</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override updated by Lightning App Builder during activation.</comment>
        <content>Single_Onboarding_Record_Page</content>
        <formFactor>Small</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>OMF_ManagedFund__c</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Account</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Small</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Account</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Contract_Record_Page1</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Contract</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Contract_Record_Page1</content>
        <formFactor>Small</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Contract</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <content>Managed_Fund_Home_Page</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
    </actionOverrides>
    <brand>
        <headerColor>#0070D2</headerColor>
        <logo>MicrosoftTeamsimage_21</logo>
        <logoVersion>1</logoVersion>
        <shouldOverrideOrgTheme>false</shouldOverrideOrgTheme>
    </brand>
    <description>The managed fund onboarding solution.</description>
    <formFactors>Small</formFactors>
    <formFactors>Large</formFactors>
    <isNavAutoTempTabsDisabled>false</isNavAutoTempTabsDisabled>
    <isNavPersonalizationDisabled>false</isNavPersonalizationDisabled>
    <isNavTabPersistenceDisabled>false</isNavTabPersistenceDisabled>
    <isOmniPinnedViewEnabled>false</isOmniPinnedViewEnabled>
    <label>Onboard My Fund</label>
    <navType>Standard</navType>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Fund_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Fund</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Fund_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Fund</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Managed_Fund_Status_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>OMF_ManagedFundStatus__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>OMF Relationship Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Managed_Fund_Status_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>OMF_ManagedFundStatus__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Fund_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Fund</recordType>
        <type>Flexipage</type>
        <profile>Operations Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Fund_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Fund</recordType>
        <type>Flexipage</type>
        <profile>Operations Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Single_Onboarding_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>OMF_ManagedFund__c</pageOrSobjectType>
        <recordType>OMF_ManagedFund__c.SingleOnboarding</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Single_Onboarding_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>OMF_ManagedFund__c</pageOrSobjectType>
        <recordType>OMF_ManagedFund__c.SingleOnboarding</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Single_Onboarding_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>OMF_ManagedFund__c</pageOrSobjectType>
        <recordType>OMF_ManagedFund__c.SingleOnboarding</recordType>
        <type>Flexipage</type>
        <profile>OMF Relationship Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Single_Onboarding_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>OMF_ManagedFund__c</pageOrSobjectType>
        <recordType>OMF_ManagedFund__c.SingleOnboarding</recordType>
        <type>Flexipage</type>
        <profile>OMF Relationship Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Single_Onboarding_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>OMF_ManagedFund__c</pageOrSobjectType>
        <recordType>OMF_ManagedFund__c.SingleOnboarding</recordType>
        <type>Flexipage</type>
        <profile>Operations Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Single_Onboarding_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>OMF_ManagedFund__c</pageOrSobjectType>
        <recordType>OMF_ManagedFund__c.SingleOnboarding</recordType>
        <type>Flexipage</type>
        <profile>Operations Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Bulk_Onboarding_Record_page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>OMF_ManagedFund__c</pageOrSobjectType>
        <recordType>OMF_ManagedFund__c.Bulk_Onboarding</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Bulk_Onboarding_Record_page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>OMF_ManagedFund__c</pageOrSobjectType>
        <recordType>OMF_ManagedFund__c.Bulk_Onboarding</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Bulk_Onboarding_Record_page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>OMF_ManagedFund__c</pageOrSobjectType>
        <recordType>OMF_ManagedFund__c.Bulk_Onboarding</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Bulk_Onboarding_Record_page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>OMF_ManagedFund__c</pageOrSobjectType>
        <recordType>OMF_ManagedFund__c.Bulk_Onboarding</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Bulk_Onboarding_Record_page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>OMF_ManagedFund__c</pageOrSobjectType>
        <recordType>OMF_ManagedFund__c.Bulk_Onboarding</recordType>
        <type>Flexipage</type>
        <profile>OMF Relationship Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Bulk_Onboarding_Record_page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>OMF_ManagedFund__c</pageOrSobjectType>
        <recordType>OMF_ManagedFund__c.Bulk_Onboarding</recordType>
        <type>Flexipage</type>
        <profile>OMF Relationship Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Bulk_Onboarding_Record_page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>OMF_ManagedFund__c</pageOrSobjectType>
        <recordType>OMF_ManagedFund__c.Bulk_Onboarding</recordType>
        <type>Flexipage</type>
        <profile>Operations Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Bulk_Onboarding_Record_page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>OMF_ManagedFund__c</pageOrSobjectType>
        <recordType>OMF_ManagedFund__c.Bulk_Onboarding</recordType>
        <type>Flexipage</type>
        <profile>Operations Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Fund_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Fund</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Fund_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Fund</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Bulk_Onboarding_Record_page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>OMF_ManagedFund__c</pageOrSobjectType>
        <recordType>OMF_ManagedFund__c.Bulk_Onboarding</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Bulk_Onboarding_Record_page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>OMF_ManagedFund__c</pageOrSobjectType>
        <recordType>OMF_ManagedFund__c.Bulk_Onboarding</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Single_Onboarding_Record_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>OMF_ManagedFund__c</pageOrSobjectType>
        <recordType>OMF_ManagedFund__c.SingleOnboarding</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Single_Onboarding_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>OMF_ManagedFund__c</pageOrSobjectType>
        <recordType>OMF_ManagedFund__c.SingleOnboarding</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <tabs>standard-home</tabs>
    <tabs>standard-Account</tabs>
    <tabs>OMF_ManagedFund__c</tabs>
    <tabs>standard-Contract</tabs>
    <tabs>standard-Contact</tabs>
    <tabs>standard-report</tabs>
    <tabs>standard-Dashboard</tabs>
    <uiType>Lightning</uiType>
</CustomApplication>
