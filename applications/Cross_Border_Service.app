<?xml version="1.0" encoding="UTF-8"?>
<CustomApplication xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Knowledge_Record_Page</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Knowledge__kav</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Knowledge_Record_Page</content>
        <formFactor>Small</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Knowledge__kav</pageOrSobjectType>
    </actionOverrides>
    <brand>
        <headerColor>#0070D2</headerColor>
        <shouldOverrideOrgTheme>false</shouldOverrideOrgTheme>
    </brand>
    <description>SFP7231 - Service Management application for Client Service users.</description>
    <formFactors>Small</formFactors>
    <formFactors>Large</formFactors>
    <isNavAutoTempTabsDisabled>false</isNavAutoTempTabsDisabled>
    <isNavPersonalizationDisabled>false</isNavPersonalizationDisabled>
    <isNavTabPersistenceDisabled>false</isNavTabPersistenceDisabled>
    <isOmniPinnedViewEnabled>false</isOmniPinnedViewEnabled>
    <label>Service Cloud</label>
    <navType>Console</navType>
    <profileActionOverrides>
        <actionName>Tab</actionName>
        <content>Service_Team_Home_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Consultant</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>BCC_AR_Enterprise_Banking_Account_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Commercial_Banking_Africa_Regions</recordType>
        <type>Flexipage</type>
        <profile>Client Service User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>BCC_AR_Enterprise_Banking_Account_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Commercial_Banking_Africa_Regions</recordType>
        <type>Flexipage</type>
        <profile>Client Service User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_Closed_LEX</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Closed_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_Closed_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Closed_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>Tab</actionName>
        <content>Service_Team_Home_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_LEX</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Consultant</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Consultant</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>Tab</actionName>
        <content>Service_Team_Home_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Client Service User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_LEX</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Angola_CCC_Case_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.CCC_Angola</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_LEX</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>System Administrator - Premier Support</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Angola_CCC_Case_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.CCC_Angola</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_Closed_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Closed_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Personal Business Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Hand_Off_LEX_page_layout</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Hand_Off</recordType>
        <type>Flexipage</type>
        <profile>Client Service User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Personal Business Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_LEX</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Personal Business Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Angola_CCC_Case_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.CCC_Angola</recordType>
        <type>Flexipage</type>
        <profile>Client Service User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Hand_Off_LEX_page_layout</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Hand_Off</recordType>
        <type>Flexipage</type>
        <profile>Client Service User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>BCC_AR_Enterprise_Banking_Account_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Enterprise_Banking_Africa_Regions</recordType>
        <type>Flexipage</type>
        <profile>Client Service User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>BCC_AR_Enterprise_Banking_Account_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Enterprise_Banking_Africa_Regions</recordType>
        <type>Flexipage</type>
        <profile>Client Service User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_Closed_LEX</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Closed_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Personal Business Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_Closed_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Closed_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Consultant</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_Closed_LEX</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Closed_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Consultant</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_Closed_LEX</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Closed_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Angola_CCC_Case_Page</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.CCC_Angola</recordType>
        <type>Flexipage</type>
        <profile>Client Service User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_Closed_LEX</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Closed_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_Closed_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Closed_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Client Service User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>Tab</actionName>
        <content>Service_Team_Home_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_Closed_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Closed_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Hand_Off_LEX_page_layout</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Hand_Off</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Hand_Off_LEX_page_layout</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Hand_Off</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_Closed_LEX</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Closed_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Client Service User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_Closed_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Closed_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_Closed_LEX</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Closed_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>System Administrator - Premier Support</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_Closed_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Closed_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>System Administrator - Premier Support</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>System Administrator - Premier Support</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_LEX</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Client Service User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_LEX</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Client Service User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Wholesale_Service_LEX</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Cross_Border_CoE_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Client_Record_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Commercial_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Client Service User</profile>
    </profileActionOverrides>
    <setupExperience>service</setupExperience>
    <tabs>standard-home</tabs>
    <tabs>standard-Feed</tabs>
    <tabs>standard-Case</tabs>
    <tabs>standard-Account</tabs>
    <tabs>standard-Contact</tabs>
    <tabs>standard-report</tabs>
    <tabs>standard-Dashboard</tabs>
    <tabs>standard-OmniSupervisorLightning</tabs>
    <tabs>Knowledge__kav</tabs>
    <tabs>Service_Type__c</tabs>
    <tabs>standard-Task</tabs>
    <uiType>Lightning</uiType>
    <utilityBar>Cross_Border_Service_UtilityBar</utilityBar>
    <workspaceConfig>
        <mappings>
            <tab>Knowledge__kav</tab>
        </mappings>
        <mappings>
            <tab>Service_Type__c</tab>
        </mappings>
        <mappings>
            <tab>standard-Account</tab>
        </mappings>
        <mappings>
            <tab>standard-Case</tab>
        </mappings>
        <mappings>
            <fieldName>AccountId</fieldName>
            <tab>standard-Contact</tab>
        </mappings>
        <mappings>
            <tab>standard-Dashboard</tab>
        </mappings>
        <mappings>
            <tab>standard-Feed</tab>
        </mappings>
        <mappings>
            <tab>standard-OmniSupervisorLightning</tab>
        </mappings>
        <mappings>
            <tab>standard-Task</tab>
        </mappings>
        <mappings>
            <tab>standard-home</tab>
        </mappings>
        <mappings>
            <tab>standard-report</tab>
        </mappings>
    </workspaceConfig>
</CustomApplication>
