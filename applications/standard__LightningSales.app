<?xml version="1.0" encoding="UTF-8"?>
<CustomApplication xmlns="http://soap.sforce.com/2006/04/metadata">
    <formFactors>Small</formFactors>
    <formFactors>Medium</formFactors>
    <formFactors>Large</formFactors>
    <isNavAutoTempTabsDisabled>false</isNavAutoTempTabsDisabled>
    <isNavPersonalizationDisabled>false</isNavPersonalizationDisabled>
    <isNavTabPersistenceDisabled>false</isNavTabPersistenceDisabled>
    <isOmniPinnedViewEnabled>false</isOmniPinnedViewEnabled>
    <label>Sales</label>
    <navType>Standard</navType>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Custom Tableau Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Security User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Api User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>API Only User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Business Administrator</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>CFX Live Account Application Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>CFX Practice Account Application Profile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Chatter External User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Chatter Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Chatter Moderator User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Company Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Credit Credit Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Custom KYC User Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Consultant</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Custom Lightning Client Services Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Guest License User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>MarketingProfile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Onboarding API</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Premier Support User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Standard Bank Employee Community</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Customer Community Plus User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Force.com - Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>StandardAul</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>System Administrator - Premier Support</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.CIB_Customer_Query</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.CIB_Customer_Query</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.CommB_Client_Case</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.CommB_Client_Case</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Production_Incident</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Production_Incident</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.User_Case</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.User_Case</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Child</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.TopParent</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Client</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Personal_Business_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Change_Request</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Change_Request</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Client_Case</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Case_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.Client_Case</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Commercial_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Commercial_Banking_Client</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Immediate_Parent</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_Goldtier</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Locked_Potential_CIF</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Custom Marketing User Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>NBAC_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Business_Assessment__c</pageOrSobjectType>
        <recordType>Business_Assessment__c.NBAC</recordType>
        <type>Flexipage</type>
        <profile>Custom Read Only Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>Tab</actionName>
        <content>Client_Coverage_Executive_Home_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Exec Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>Tab</actionName>
        <content>Client_Coverage_Home_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Client Coverage Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>Tab</actionName>
        <content>Lightning_Generic_Home_Page_Insights</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Custom Standard Lightning Generic Business Unit Mobile User Insights</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>Tab</actionName>
        <content>Credit_Credit_Analyst_Home_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Credit Credit Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>Tab</actionName>
        <content>CommB_CSU_Home_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Communities User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>Tab</actionName>
        <content>CommB_RM_Home_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>Tab</actionName>
        <content>CommB_RM_Home_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Client_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Inactive</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_PBB_ROA_Entity_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>PBB_ROA_Entity__c</pageOrSobjectType>
        <recordType>PBB_ROA_Entity__c.Commercial_Client</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_PBB_ROA_Entity_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>PBB_ROA_Entity__c</pageOrSobjectType>
        <recordType>PBB_ROA_Entity__c.Commercial_Client</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_PBB_ROA_Entity_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>PBB_ROA_Entity__c</pageOrSobjectType>
        <recordType>PBB_ROA_Entity__c.SME_Client</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_PBB_ROA_Entity_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>PBB_ROA_Entity__c</pageOrSobjectType>
        <recordType>PBB_ROA_Entity__c.SME_Client</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Event_Report_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Event_Report_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Event_Report_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <recordType>Call_Report__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Event_Report_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <recordType>Call_Report__c.Meeting</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Event_Report_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <recordType>Call_Report__c.NBAC_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Event_Report_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Call_Report__c</pageOrSobjectType>
        <recordType>Call_Report__c.NBAC_Meeting</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_PBB_ROA_Entity_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>PBB_ROA_Entity__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Contact_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Contact</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Contact_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Contact</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Contact_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Contact</pageOrSobjectType>
        <recordType>Contact.SA_Bank_Contact_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Contact_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Contact</pageOrSobjectType>
        <recordType>Contact.SA_Bank_Contact_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Contact_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Contact</pageOrSobjectType>
        <recordType>Contact.SA_Client_Contact_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Contact_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Contact</pageOrSobjectType>
        <recordType>Contact.SA_Client_Contact_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Contact_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Contact</pageOrSobjectType>
        <recordType>Contact.Inactive_Client_Contact_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_Contact_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Contact</pageOrSobjectType>
        <recordType>Contact.Inactive_Client_Contact_Record_Type</recordType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>CommB_PBB_ROA_Entity_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>PBB_ROA_Entity__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Commercial Banking Custom Std User - Mobile</profile>
    </profileActionOverrides>
    <tabs>standard-home</tabs>
    <tabs>standard-Opportunity</tabs>
    <tabs>standard-Lead</tabs>
    <tabs>standard-Task</tabs>
    <tabs>standard-File</tabs>
    <tabs>standard-ContentNote</tabs>
    <tabs>standard-Account</tabs>
    <tabs>standard-Contact</tabs>
    <tabs>standard-Campaign</tabs>
    <tabs>standard-Dashboard</tabs>
    <tabs>standard-report</tabs>
    <tabs>standard-Feed</tabs>
    <tabs>standard-CollaborationGroup</tabs>
    <tabs>standard-Event</tabs>
    <tabs>standard-OtherUserProfile</tabs>
    <tabs>standard-Case</tabs>
    <tabs>standard-Forecasting3</tabs>
    <tabs>standard-WaveHomeLightning</tabs>
    <uiType>Lightning</uiType>
    <utilityBar>LightningSales_UtilityBar</utilityBar>
</CustomApplication>
