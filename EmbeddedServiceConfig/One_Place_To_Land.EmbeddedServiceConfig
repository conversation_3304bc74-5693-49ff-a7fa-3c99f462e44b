<?xml version="1.0" encoding="UTF-8"?>
<EmbeddedServiceConfig xmlns="http://soap.sforce.com/2006/04/metadata">
    <areGuestUsersAllowed>false</areGuestUsersAllowed>
    <deploymentFeature>LiveAgent</deploymentFeature>
    <deploymentType>Web</deploymentType>
    <embeddedServiceCustomComponents>
        <componentBundleType>AuraDefinitionBundle</componentBundleType>
        <customComponent>OSB_VA_PreChat</customComponent>
        <customComponentType>LA_Prechat</customComponentType>
    </embeddedServiceCustomComponents>
    <embeddedServiceCustomLabels>
        <customLabel>LA_Waiting_WithQueuePos_WaitingQueuePosMaxMessageSecondL_04I1X0000008OR7_6541143</customLabel>
        <labelKey>LA_Waiting_WithQueuePos_WaitingQueuePosMaxMessageSecondLine</labelKey>
    </embeddedServiceCustomLabels>
    <embeddedServiceCustomLabels>
        <customLabel>LA_Waiting_WithoutQueuePos_WaitingMessage_04I1X0000008OR7_7055728</customLabel>
        <labelKey>LA_Waiting_WithoutQueuePos_WaitingMessage</labelKey>
    </embeddedServiceCustomLabels>
    <embeddedServiceCustomLabels>
        <customLabel>LA_Chat_Ended_ChatEndChatbot_04I1X0000008OR7_2825645</customLabel>
        <labelKey>LA_Chat_Ended_ChatEndChatbot</labelKey>
    </embeddedServiceCustomLabels>
    <embeddedServiceCustomLabels>
        <customLabel>LA_Waiting_WithoutQueuePos_WaitingGreeting_04I1X0000008OR7_9832401</customLabel>
        <labelKey>LA_Waiting_WithoutQueuePos_WaitingGreeting</labelKey>
    </embeddedServiceCustomLabels>
    <embeddedServiceCustomLabels>
        <customLabel>LA_Chat_Body_ChatWindowAgent_04I1X0000008OR7_7658089</customLabel>
        <labelKey>LA_Chat_Body_ChatWindowAgent</labelKey>
    </embeddedServiceCustomLabels>
    <embeddedServiceCustomLabels>
        <customLabel>LA_Chat_Body_AgentTypingUpdate_04I1X0000008OR7_8138270</customLabel>
        <labelKey>LA_Chat_Body_AgentTypingUpdate</labelKey>
    </embeddedServiceCustomLabels>
    <isEnabled>true</isEnabled>
    <isTermsAndConditionsEnabled>false</isTermsAndConditionsEnabled>
    <isTermsAndConditionsRequired>false</isTermsAndConditionsRequired>
    <masterLabel>OneHub</masterLabel>
    <shouldHideAuthDialog>false</shouldHideAuthDialog>
    <site>OneHub</site>
</EmbeddedServiceConfig>
