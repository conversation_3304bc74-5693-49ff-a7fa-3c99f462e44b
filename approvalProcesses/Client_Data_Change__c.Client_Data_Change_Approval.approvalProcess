<?xml version="1.0" encoding="UTF-8"?>
<ApprovalProcess xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>false</active>
    <allowRecall>true</allowRecall>
    <allowedSubmitters>
        <submitter>Corporate_Investment_Banking</submitter>
        <type>roleSubordinates</type>
    </allowedSubmitters>
    <approvalPageFields>
        <field>Name</field>
        <field>Current_Client_Coordinator__c</field>
        <field>Current_Client_Coordinator_Role__c</field>
        <field>New_Client_Coordinator__c</field>
        <field>New_Client_Coordinator_Role__c</field>
        <field>Status__c</field>
        <field>Owner</field>
    </approvalPageFields>
    <approvalStep>
        <allowDelegate>true</allowDelegate>
        <assignedApprover>
            <approver>
                <name>New_Client_Coordinator__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <description>The approval step so that the approval process is submitted to cc and new cc for approval and rejection.</description>
        <entryCriteria>
            <criteriaItems>
                <field>Client_Data_Change__c.RecordType</field>
                <operation>equals</operation>
                <value>Client Coordinator Request</value>
            </criteriaItems>
            <criteriaItems>
                <field>Client_Data_Change__c.Current_Client_Coordinator__c</field>
                <operation>equals</operation>
                <value>Salesforce Administration</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Submitted to new CC</label>
        <name>Submitted_to_new_CC1</name>
    </approvalStep>
    <approvalStep>
        <allowDelegate>true</allowDelegate>
        <assignedApprover>
            <approver>
                <name>Current_Client_Coordinator__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>New_Client_Coordinator__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>Unanimous</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Client_Data_Change__c.RecordType</field>
                <operation>equals</operation>
                <value>Client Coordinator Request</value>
            </criteriaItems>
            <criteriaItems>
                <field>Client_Data_Change__c.Current_Client_Coordinator__c</field>
                <operation>notEqual</operation>
                <value>Salesforce Administration</value>
            </criteriaItems>
        </entryCriteria>
        <label>Submitted to Current And New CC</label>
        <name>Submitted_to_Current_And_New_CC</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <description>Approval Process to change the CC on the Client. EN-0676</description>
    <emailTemplate>Client_Data_Change_Request_Templates/Client_Coordinator_Approval_Request</emailTemplate>
    <enableMobileDeviceAccess>false</enableMobileDeviceAccess>
    <entryCriteria>
        <criteriaItems>
            <field>Client_Data_Change__c.Status__c</field>
            <operation>equals</operation>
            <value>New</value>
        </criteriaItems>
    </entryCriteria>
    <finalApprovalActions>
        <action>
            <name>CDC_Status_to_Approved</name>
            <type>FieldUpdate</type>
        </action>
    </finalApprovalActions>
    <finalApprovalRecordLock>true</finalApprovalRecordLock>
    <finalRejectionActions>
        <action>
            <name>CDC_status_to_Rejected</name>
            <type>FieldUpdate</type>
        </action>
    </finalRejectionActions>
    <finalRejectionRecordLock>false</finalRejectionRecordLock>
    <initialSubmissionActions>
        <action>
            <name>CDC_status_to_Submitted_For_Approval</name>
            <type>FieldUpdate</type>
        </action>
    </initialSubmissionActions>
    <label>Client Data Change Approval</label>
    <processOrder>1</processOrder>
    <recallActions>
        <action>
            <name>CDC_Update_To_New</name>
            <type>FieldUpdate</type>
        </action>
    </recallActions>
    <recordEditability>AdminOnly</recordEditability>
    <showApprovalHistory>true</showApprovalHistory>
</ApprovalProcess>
