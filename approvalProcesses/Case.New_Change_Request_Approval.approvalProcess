<?xml version="1.0" encoding="UTF-8"?>
<ApprovalProcess xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <allowRecall>true</allowRecall>
    <allowedSubmitters>
        <type>owner</type>
    </allowedSubmitters>
    <approvalPageFields>
        <field>CaseNumber</field>
        <field>Owner</field>
        <field>Subject</field>
        <field>Description</field>
    </approvalPageFields>
    <approvalStep>
        <allowDelegate>true</allowDelegate>
        <approvalActions>
            <action>
                <name>Set_Case_Status_to_Approved</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>Unanimous</whenMultipleApprovers>
        </assignedApprover>
        <description>Request for approval will be sent to the following : with the New case details as per the above step 
Approvers: Sanchia Judge
Delegated Approvers: <PERSON></description>
        <label>CRM Solutions Management Approval</label>
        <name>CRM_Solutions_Management_Approval</name>
        <rejectionActions>
            <action>
                <name>Set_Case_Status_to_Not_Approved</name>
                <type>FieldUpdate</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <description>Approval process for level 1 &amp; 2 new change requests 

(CRM Enhanced Cases Change Requests C Reinecke 2010/08)
(CRM Enhanced Cases Case#597 C Reinecke 2010/09)
(CRM Enhanced Cases Case#848 A Heath 2011/01)</description>
    <emailTemplate>CRM_Change_Request_templates/CRM_New_Case_Approval_Request</emailTemplate>
    <enableMobileDeviceAccess>false</enableMobileDeviceAccess>
    <entryCriteria>
        <criteriaItems>
            <field>Case.RecordType</field>
            <operation>equals</operation>
            <value>Change Request</value>
        </criteriaItems>
        <criteriaItems>
            <field>Case.Status</field>
            <operation>equals</operation>
            <value>New,On Hold</value>
        </criteriaItems>
        <criteriaItems>
            <field>Case.Change_Request_Level__c</field>
            <operation>equals</operation>
            <value>1. Minor - Look &amp; Feel,2. Minor - Business Development</value>
        </criteriaItems>
    </entryCriteria>
    <finalApprovalActions>
        <action>
            <name>Case_Stage_Update</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Reset_Submitted_for_Approval_Flag</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Send_Approval_Response_Email_to_Case_Owner</name>
            <type>Alert</type>
        </action>
    </finalApprovalActions>
    <finalApprovalRecordLock>false</finalApprovalRecordLock>
    <finalRejectionActions>
        <action>
            <name>Reset_Submitted_for_Approval_Flag</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Send_Rejection_Response_Email_to_Case_Owner_and_Approvers_Change_Request_Cases</name>
            <type>Alert</type>
        </action>
    </finalRejectionActions>
    <finalRejectionRecordLock>false</finalRejectionRecordLock>
    <initialSubmissionActions>
        <action>
            <name>Set_Submitted_for_Approval_Flag</name>
            <type>FieldUpdate</type>
        </action>
    </initialSubmissionActions>
    <label>New Change Request Approval Level 1 &amp; 2</label>
    <processOrder>1</processOrder>
    <recallActions>
        <action>
            <name>Reset_Submitted_for_Approval_Flag</name>
            <type>FieldUpdate</type>
        </action>
    </recallActions>
    <recordEditability>AdminOnly</recordEditability>
    <showApprovalHistory>true</showApprovalHistory>
</ApprovalProcess>
