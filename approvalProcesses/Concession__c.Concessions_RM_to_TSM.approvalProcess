<?xml version="1.0" encoding="UTF-8"?>
<ApprovalProcess xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <allowRecall>true</allowRecall>
    <allowedSubmitters>
        <type>owner</type>
    </allowedSubmitters>
    <approvalPageFields>
        <field>Name</field>
        <field>Owner</field>
        <field>Sub_Status__c</field>
        <field>Pending_Approval_Status__c</field>
        <field>Rejected_Status__c</field>
    </approvalPageFields>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name>UG_BCB_TSM</name>
                <type>queue</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <description>TSM to review and approve.  Once approved this is submitted to the Sector Head</description>
        <label>TSM Approval</label>
        <name>TSM_Approval</name>
    </approvalStep>
    <description>[SFP-36385] - Approval process for concessions for Uganda Team. Submitted by the RM</description>
    <emailTemplate>UG_BCB_General_Email_Templates/Concession_Approval</emailTemplate>
    <enableMobileDeviceAccess>false</enableMobileDeviceAccess>
    <entryCriteria>
        <booleanFilter>(1 AND (2 or 3 or 4 or 5) )or 6</booleanFilter>
        <criteriaItems>
            <field>Concession__c.Status__c</field>
            <operation>equals</operation>
            <value>New</value>
        </criteriaItems>
        <criteriaItems>
            <field>Concession__c.Sub_Status__c</field>
            <operation>equals</operation>
            <value>With RM</value>
        </criteriaItems>
        <criteriaItems>
            <field>Concession__c.Sub_Status__c</field>
            <operation>equals</operation>
            <value>RM Review and Engage</value>
        </criteriaItems>
        <criteriaItems>
            <field>Concession__c.Sub_Status__c</field>
            <operation>equals</operation>
            <value>Recalled</value>
        </criteriaItems>
        <criteriaItems>
            <field>Concession__c.Sub_Status__c</field>
            <operation>equals</operation>
            <value>With TSM</value>
        </criteriaItems>
        <criteriaItems>
            <field>Concession__c.Sub_Status__c</field>
            <operation>equals</operation>
            <value>With RM</value>
        </criteriaItems>
    </entryCriteria>
    <finalApprovalActions>
        <action>
            <name>Concession_Approval</name>
            <type>Alert</type>
        </action>
        <action>
            <name>Remove_Rejected</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Sector_Head_Approval</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Sub_Status_with_Sector_head</name>
            <type>FieldUpdate</type>
        </action>
    </finalApprovalActions>
    <finalApprovalRecordLock>false</finalApprovalRecordLock>
    <finalRejectionActions>
        <action>
            <name>Concession_Rejection</name>
            <type>Alert</type>
        </action>
        <action>
            <name>Send_Back_to_RM</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>TSM_Rejected</name>
            <type>FieldUpdate</type>
        </action>
    </finalRejectionActions>
    <finalRejectionRecordLock>false</finalRejectionRecordLock>
    <initialSubmissionActions>
        <action>
            <name>Concession_Approval</name>
            <type>Alert</type>
        </action>
        <action>
            <name>Pending_TSM_approval</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Status_to_pending</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Sub_status_pending_TSM</name>
            <type>FieldUpdate</type>
        </action>
    </initialSubmissionActions>
    <label>Concessions RM to TSM</label>
    <processOrder>1</processOrder>
    <recallActions>
        <action>
            <name>Cancel_Approval</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Pending_Approval_removed</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Recall_and_Reject</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Update_Status_to_Closed</name>
            <type>FieldUpdate</type>
        </action>
    </recallActions>
    <recordEditability>AdminOrCurrentApprover</recordEditability>
    <showApprovalHistory>true</showApprovalHistory>
</ApprovalProcess>
