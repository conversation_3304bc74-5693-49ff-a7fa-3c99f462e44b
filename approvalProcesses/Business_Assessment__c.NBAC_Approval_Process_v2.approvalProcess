<?xml version="1.0" encoding="UTF-8"?>
<ApprovalProcess xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <allowRecall>true</allowRecall>
    <allowedSubmitters>
        <type>creator</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <type>owner</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <submitter>Corporate_Investment_Banking</submitter>
        <type>roleSubordinates</type>
    </allowedSubmitters>
    <approvalPageFields>
        <field>Name</field>
        <field>NBAC_Committee__c</field>
        <field>Owner</field>
        <field>Account__c</field>
    </approvalPageFields>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.NBAC_Committee__c</field>
                <operation>equals</operation>
                <value>Africa Regional NBAC</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Africa Region Committtee</label>
        <name>Africa_Region_Committtee</name>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.NBAC_Committee__c</field>
                <operation>equals</operation>
                <value>Global NBAC</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Global Committtee</label>
        <name>Global_NBAC</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.NBAC_Committee__c</field>
                <operation>equals</operation>
                <value>South Africa NBAC</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>South Africa Committtee</label>
        <name>South_Africa_NBAC</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.NBAC_Committee__c</field>
                <operation>equals</operation>
                <value>Real Estate Regional NBAC</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Real Estate Regional Committtee</label>
        <name>Property_Finance_Regional_NBAC</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.NBAC_Committee__c</field>
                <operation>equals</operation>
                <value>Nigeria NBAC</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Nigeria Committtee</label>
        <name>Nigeria_NBAC</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.NBAC_Committee__c</field>
                <operation>equals</operation>
                <value>Ghana NBAC</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Ghana Committtee</label>
        <name>Ghana_NBAC</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.NBAC_Committee__c</field>
                <operation>equals</operation>
                <value>Botswana NBAC</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Botswana Committee</label>
        <name>Botswana_Committee</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.NBAC_Committee__c</field>
                <operation>equals</operation>
                <value>Cote D&apos;Ivoire NBAC</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Cote D&apos;Ivoire Committee</label>
        <name>Cote_D_Ivoire_Committee</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.NBAC_Committee__c</field>
                <operation>equals</operation>
                <value>DRC NBAC</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>DRC Committee</label>
        <name>DRC_Committee</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.NBAC_Committee__c</field>
                <operation>equals</operation>
                <value>Kenya NBAC</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Kenya Committee</label>
        <name>Kenya_Committee</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.NBAC_Committee__c</field>
                <operation>equals</operation>
                <value>Lesotho NBAC</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Lesotho Committee</label>
        <name>Lesotho_Committee</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.NBAC_Committee__c</field>
                <operation>equals</operation>
                <value>Malawi NBAC</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Malawi Committee</label>
        <name>Malawi_Committee</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.NBAC_Committee__c</field>
                <operation>equals</operation>
                <value>Mozambique NBAC</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Mozambique Committee</label>
        <name>Mozambique_Committee</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.NBAC_Committee__c</field>
                <operation>equals</operation>
                <value>Tanzania NBAC</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Tanzania Committee</label>
        <name>Tanzania_Committee</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.NBAC_Committee__c</field>
                <operation>equals</operation>
                <value>South Africa CommB NBAC</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>South Africa CommB Committtee</label>
        <name>South_Africa_CommB_Committtee</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.NBAC_Committee__c</field>
                <operation>equals</operation>
                <value>Zimbabwe NBAC</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Zimbabwe Committee</label>
        <name>Zimbabwe_Committee</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.NBAC_Committee__c</field>
                <operation>equals</operation>
                <value>Angola NBAC</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Angola Committee</label>
        <name>Angola_Committee</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name>Dynamic_Approver_01__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>Unanimous</whenMultipleApprovers>
        </assignedApprover>
        <description>Activates when ECST only has one approver</description>
        <entryCriteria>
            <booleanFilter>1 AND 2 AND 3 AND 4 AND 5 AND 6 AND 7 AND 8 AND 9 AND 10 AND 11</booleanFilter>
            <criteriaItems>
                <field>Business_Assessment__c.Submission_Type__c</field>
                <operation>equals</operation>
                <value>Enhanced CST</value>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_01__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_02__c</field>
                <operation>equals</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_03__c</field>
                <operation>equals</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_04__c</field>
                <operation>equals</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_05__c</field>
                <operation>equals</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_06__c</field>
                <operation>equals</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_07__c</field>
                <operation>equals</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_08__c</field>
                <operation>equals</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_09__c</field>
                <operation>equals</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_10__c</field>
                <operation>equals</operation>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Enhanced CST Quorum</label>
        <name>ECST_One</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name>Dynamic_Approver_01__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_02__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>Unanimous</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <booleanFilter>1 AND 2 AND 3 AND 4 AND 5 AND 6 AND 7 AND 8 AND 9 AND 10 AND 11</booleanFilter>
            <criteriaItems>
                <field>Business_Assessment__c.Submission_Type__c</field>
                <operation>equals</operation>
                <value>Enhanced CST</value>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_01__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_02__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_03__c</field>
                <operation>equals</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_04__c</field>
                <operation>equals</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_05__c</field>
                <operation>equals</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_06__c</field>
                <operation>equals</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_07__c</field>
                <operation>equals</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_08__c</field>
                <operation>equals</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_09__c</field>
                <operation>equals</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_10__c</field>
                <operation>equals</operation>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Enhanced CST Quorum</label>
        <name>ECST_Two</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name>Dynamic_Approver_01__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_02__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_03__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>Unanimous</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <booleanFilter>1 AND 2 AND 3 AND 4 AND 5 AND 6 AND 7 AND 8 AND 9 AND 10 AND 11</booleanFilter>
            <criteriaItems>
                <field>Business_Assessment__c.Submission_Type__c</field>
                <operation>equals</operation>
                <value>Enhanced CST</value>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_01__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_02__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_03__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_04__c</field>
                <operation>equals</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_05__c</field>
                <operation>equals</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_06__c</field>
                <operation>equals</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_07__c</field>
                <operation>equals</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_08__c</field>
                <operation>equals</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_09__c</field>
                <operation>equals</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_10__c</field>
                <operation>equals</operation>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Enhanced CST Quorum</label>
        <name>ECST_Three</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name>Dynamic_Approver_01__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_02__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_03__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_04__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>Unanimous</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.Submission_Type__c</field>
                <operation>equals</operation>
                <value>Enhanced CST</value>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_01__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_02__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_03__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_04__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_05__c</field>
                <operation>equals</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_06__c</field>
                <operation>equals</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_07__c</field>
                <operation>equals</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_08__c</field>
                <operation>equals</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_09__c</field>
                <operation>equals</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_10__c</field>
                <operation>equals</operation>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Enhanced CST Quorum</label>
        <name>ECST_Four</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name>Dynamic_Approver_01__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_02__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_03__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_04__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_05__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>Unanimous</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <booleanFilter>1 AND 2 AND 3 AND 4 AND 5 AND 6 AND 7 AND 8 AND 9 AND 10 AND 11</booleanFilter>
            <criteriaItems>
                <field>Business_Assessment__c.Submission_Type__c</field>
                <operation>equals</operation>
                <value>Enhanced CST</value>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_01__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_02__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_03__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_04__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_05__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_06__c</field>
                <operation>equals</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_07__c</field>
                <operation>equals</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_08__c</field>
                <operation>equals</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_09__c</field>
                <operation>equals</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_10__c</field>
                <operation>equals</operation>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Enhanced CST Quorum</label>
        <name>ECST_Five</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name>Dynamic_Approver_01__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_02__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_03__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_04__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_05__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_06__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>Unanimous</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <booleanFilter>1 AND 2 AND 3 AND 4 AND 5 AND 6 AND 7 AND 8 AND 9 AND 10 AND 11</booleanFilter>
            <criteriaItems>
                <field>Business_Assessment__c.Submission_Type__c</field>
                <operation>equals</operation>
                <value>Enhanced CST</value>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_01__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_02__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_03__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_04__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_05__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_06__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_07__c</field>
                <operation>equals</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_08__c</field>
                <operation>equals</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_09__c</field>
                <operation>equals</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_10__c</field>
                <operation>equals</operation>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Enhanced CST Quorum</label>
        <name>ECST_Six</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name>Dynamic_Approver_05__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_06__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_07__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_01__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_02__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_03__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_04__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>Unanimous</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <booleanFilter>1 AND 2 AND 3 AND 4 AND 5 AND 6 AND 7 AND 8 AND 9 AND 10 AND 11</booleanFilter>
            <criteriaItems>
                <field>Business_Assessment__c.Submission_Type__c</field>
                <operation>equals</operation>
                <value>Enhanced CST</value>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_01__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_02__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_03__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_04__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_05__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_06__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_07__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_08__c</field>
                <operation>equals</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_09__c</field>
                <operation>equals</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_10__c</field>
                <operation>equals</operation>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Enhanced CST Quorum</label>
        <name>ECST_Seven</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name>Dynamic_Approver_05__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_06__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_07__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_08__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_01__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_02__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_03__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_04__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>Unanimous</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <booleanFilter>1 AND 2 AND 3 AND 4 AND 5 AND 6 AND 7 AND 8 AND 9 AND 10 AND 11</booleanFilter>
            <criteriaItems>
                <field>Business_Assessment__c.Submission_Type__c</field>
                <operation>equals</operation>
                <value>Enhanced CST</value>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_01__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_02__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_03__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_04__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_05__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_06__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_07__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_08__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_09__c</field>
                <operation>equals</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_10__c</field>
                <operation>equals</operation>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Enhanced CST Quorum</label>
        <name>ECST_Eight</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name>Dynamic_Approver_09__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_05__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_06__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_07__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_08__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_01__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_02__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_03__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_04__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>Unanimous</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <booleanFilter>1 AND 2 AND 3 AND 4 AND 5 AND 6 AND 7 AND 8 AND 9 AND 10 AND 11</booleanFilter>
            <criteriaItems>
                <field>Business_Assessment__c.Submission_Type__c</field>
                <operation>equals</operation>
                <value>Enhanced CST</value>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_01__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_02__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_03__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_04__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_05__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_06__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_07__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_08__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_09__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_10__c</field>
                <operation>equals</operation>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Enhanced CST Quorum</label>
        <name>ECST_Nine</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name>Dynamic_Approver_09__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_10__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_05__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_06__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_07__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_08__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_01__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_02__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_03__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Dynamic_Approver_04__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>Unanimous</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <booleanFilter>1 AND 2 AND 3 AND 4 AND 5 AND 6 AND 7 AND 8 AND 9 AND 10 AND 11</booleanFilter>
            <criteriaItems>
                <field>Business_Assessment__c.Submission_Type__c</field>
                <operation>equals</operation>
                <value>Enhanced CST</value>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_01__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_02__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_03__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_04__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_05__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_06__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_07__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_08__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_09__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Business_Assessment__c.Dynamic_Approver_10__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
        </entryCriteria>
        <label>Enhanced CST Quorum</label>
        <name>ECST_Ten</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <description>SFP-43278: Addition of Africa Region Committee &amp; 
Removal of:  East Africa, South &amp; Central Africa and West Africa

Approval process for submitting NBACs US-3155
SFP-38151: Adding Support for ECST

Nocks added South Africa CommB Committtee step
Balint added Angola Approval criteria</description>
    <emailTemplate>Business_Assessment_Notifications/NBAC_submitted</emailTemplate>
    <enableMobileDeviceAccess>false</enableMobileDeviceAccess>
    <entryCriteria>
        <criteriaItems>
            <field>Business_Assessment__c.Milestone__c</field>
            <operation>equals</operation>
            <value>Submitted</value>
        </criteriaItems>
        <criteriaItems>
            <field>Business_Assessment__c.NBAC_Committee__c</field>
            <operation>notEqual</operation>
        </criteriaItems>
    </entryCriteria>
    <finalApprovalActions>
        <action>
            <name>MilestoneApproved</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>NBAC_approved</name>
            <type>Alert</type>
        </action>
        <action>
            <name>Set_Approval_Status_to_Approved</name>
            <type>FieldUpdate</type>
        </action>
    </finalApprovalActions>
    <finalApprovalRecordLock>true</finalApprovalRecordLock>
    <finalRejectionActions>
        <action>
            <name>MilestoneApproved</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>NBAC_rejected</name>
            <type>Alert</type>
        </action>
        <action>
            <name>Set_Approval_Status_to_Rejected</name>
            <type>FieldUpdate</type>
        </action>
    </finalRejectionActions>
    <finalRejectionRecordLock>false</finalRejectionRecordLock>
    <label>NBAC Approval Process</label>
    <processOrder>1</processOrder>
    <recallActions>
        <action>
            <name>Move_back_to_finalisation</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Set_Approval_Status_to_Recalled</name>
            <type>FieldUpdate</type>
        </action>
    </recallActions>
    <recordEditability>AdminOnly</recordEditability>
    <showApprovalHistory>true</showApprovalHistory>
</ApprovalProcess>
