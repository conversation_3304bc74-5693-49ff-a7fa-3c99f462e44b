<?xml version="1.0" encoding="UTF-8"?>
<ApprovalProcess xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>false</active>
    <allowRecall>true</allowRecall>
    <allowedSubmitters>
        <type>creator</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <type>owner</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <submitter>Corporate_Investment_Banking</submitter>
        <type>roleSubordinates</type>
    </allowedSubmitters>
    <approvalPageFields>
        <field>Name</field>
        <field>NBAC_Committee__c</field>
        <field>Owner</field>
        <field>Account__c</field>
    </approvalPageFields>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.NBAC_Committee__c</field>
                <operation>equals</operation>
                <value>Global NBAC</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Global Committtee</label>
        <name>Global_NBAC</name>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.NBAC_Committee__c</field>
                <operation>equals</operation>
                <value>South Africa NBAC</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>South Africa Committtee</label>
        <name>South_Africa_NBAC</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.NBAC_Committee__c</field>
                <operation>equals</operation>
                <value>South &amp; Central Africa NBAC</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>South &amp; Central Africa Committtee</label>
        <name>South_Central_Africa_NBAC</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.NBAC_Committee__c</field>
                <operation>equals</operation>
                <value>Real Estate Regional NBAC</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Real Estate Regional Committtee</label>
        <name>Property_Finance_Regional_NBAC</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.NBAC_Committee__c</field>
                <operation>equals</operation>
                <value>Nigeria NBAC</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Nigeria Committtee</label>
        <name>Nigeria_NBAC</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.NBAC_Committee__c</field>
                <operation>equals</operation>
                <value>Ghana NBAC</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Ghana Committtee</label>
        <name>Ghana_NBAC</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.NBAC_Committee__c</field>
                <operation>equals</operation>
                <value>East Africa NBAC</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>East Africa Committtee</label>
        <name>East_Africa_NBAC</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.NBAC_Committee__c</field>
                <operation>equals</operation>
                <value>Botswana NBAC</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Botswana Committee</label>
        <name>Botswana_Committee</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.NBAC_Committee__c</field>
                <operation>equals</operation>
                <value>Cote D&apos;Ivoire NBAC</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Cote D&apos;Ivoire Committee</label>
        <name>Cote_D_Ivoire_Committee</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.NBAC_Committee__c</field>
                <operation>equals</operation>
                <value>DRC NBAC</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>DRC Committee</label>
        <name>DRC_Committee</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.NBAC_Committee__c</field>
                <operation>equals</operation>
                <value>Kenya NBAC</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Kenya Committee</label>
        <name>Kenya_Committee</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.NBAC_Committee__c</field>
                <operation>equals</operation>
                <value>Lesotho NBAC</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Lesotho Committee</label>
        <name>Lesotho_Committee</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.NBAC_Committee__c</field>
                <operation>equals</operation>
                <value>Malawi NBAC</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Malawi Committee</label>
        <name>Malawi_Committee</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.NBAC_Committee__c</field>
                <operation>equals</operation>
                <value>Mozambique NBAC</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Mozambique Committee</label>
        <name>Mozambique_Committee</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.NBAC_Committee__c</field>
                <operation>equals</operation>
                <value>Tanzania NBAC</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Tanzania Committee</label>
        <name>Tanzania_Committee</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.NBAC_Committee__c</field>
                <operation>equals</operation>
                <value>West Africa NBAC</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>West Africa Committee</label>
        <name>West_Africa_Committee</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.NBAC_Committee__c</field>
                <operation>equals</operation>
                <value>South Africa CommB NBAC</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>South Africa CommB Committtee</label>
        <name>South_Africa_CommB_Committtee</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.NBAC_Committee__c</field>
                <operation>equals</operation>
                <value>Zimbabwe NBAC</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Zimbabwe Committee</label>
        <name>Zimbabwe_Committee</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Business_Assessment__c.NBAC_Committee__c</field>
                <operation>equals</operation>
                <value>Angola NBAC</value>
            </criteriaItems>
        </entryCriteria>
        <label>Angola Committee</label>
        <name>Angola_Committee</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <description>Approval process for submitting NBACs US-3155,

Nocks added South Africa CommB Committtee step
Balint added Angola Approval criteria</description>
    <emailTemplate>Business_Assessment_Notifications/NBAC_submitted</emailTemplate>
    <enableMobileDeviceAccess>false</enableMobileDeviceAccess>
    <entryCriteria>
        <criteriaItems>
            <field>Business_Assessment__c.Milestone__c</field>
            <operation>equals</operation>
            <value>Submitted</value>
        </criteriaItems>
        <criteriaItems>
            <field>Business_Assessment__c.NBAC_Committee__c</field>
            <operation>notEqual</operation>
        </criteriaItems>
    </entryCriteria>
    <finalApprovalActions>
        <action>
            <name>MilestoneApproved</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>NBAC_approved</name>
            <type>Alert</type>
        </action>
        <action>
            <name>Set_Approval_Status_to_Approved</name>
            <type>FieldUpdate</type>
        </action>
    </finalApprovalActions>
    <finalApprovalRecordLock>true</finalApprovalRecordLock>
    <finalRejectionActions>
        <action>
            <name>MilestoneApproved</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>NBAC_rejected</name>
            <type>Alert</type>
        </action>
        <action>
            <name>Set_Approval_Status_to_Rejected</name>
            <type>FieldUpdate</type>
        </action>
    </finalRejectionActions>
    <finalRejectionRecordLock>false</finalRejectionRecordLock>
    <label>NBAC Approval Process</label>
    <processOrder>1</processOrder>
    <recallActions>
        <action>
            <name>Set_Approval_Status_to_Recalled</name>
            <type>FieldUpdate</type>
        </action>
    </recallActions>
    <recordEditability>AdminOnly</recordEditability>
    <showApprovalHistory>true</showApprovalHistory>
</ApprovalProcess>
