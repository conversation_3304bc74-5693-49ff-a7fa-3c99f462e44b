<?xml version="1.0" encoding="UTF-8"?>
<ApprovalProcess xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <allowRecall>true</allowRecall>
    <allowedSubmitters>
        <submitter>UG_BCB_TSM</submitter>
        <type>group</type>
    </allowedSubmitters>
    <approvalPageFields>
        <field>Name</field>
        <field>Owner</field>
        <field>Sub_Status__c</field>
        <field>Pending_Approval_Status__c</field>
        <field>Rejected_Status__c</field>
    </approvalPageFields>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Concession_Approval</name>
                <type>Alert</type>
            </action>
            <action>
                <name>Pending_Manager_Products_approval</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Remove_Rejected</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Sub_Status_with_Mgr_Products</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name>UG_BCB_Sector_Head</name>
                <type>queue</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <description>Sector Head to review and approve.  Once approved this is submitted to the Manager Products</description>
        <label>Sector Head Approval</label>
        <name>Sector_Head_Approval</name>
        <rejectionActions>
            <action>
                <name>Concession_Rejection</name>
                <type>Alert</type>
            </action>
            <action>
                <name>Pending_TSM_Update</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Sector_Head_Reject</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Sub_status_pending_TSM</name>
                <type>FieldUpdate</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Concession_Approval</name>
                <type>Alert</type>
            </action>
            <action>
                <name>Pending_Head_TPS_Approval</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Remove_Rejected</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Sub_Status_with_TPs</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name>UG_BCB_Manager_Products</name>
                <type>queue</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <description>Manager Products to review and approve.  Once approved this is submitted to the Head TPS</description>
        <entryCriteria>
            <criteriaItems>
                <field>Concession__c.Pending_Approval_Status__c</field>
                <operation>equals</operation>
                <value>Pending Manager Products Approval</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Manager Products Approval</label>
        <name>Manager_Products_Approval</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
        <rejectionActions>
            <action>
                <name>Concession_Rejection</name>
                <type>Alert</type>
            </action>
            <action>
                <name>Pending_TSM_Update</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Rejected_by_Manager_Products</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Sub_status_pending_TSM</name>
                <type>FieldUpdate</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Approved_by_Head_TPS</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Concession_Approval</name>
                <type>Alert</type>
            </action>
            <action>
                <name>Pending_Approval_removed</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Set_Active_Status</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name>UG_BCB_Head_TPS</name>
                <type>queue</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <description>Head TPS to review and approve.</description>
        <entryCriteria>
            <criteriaItems>
                <field>Concession__c.Pending_Approval_Status__c</field>
                <operation>equals</operation>
                <value>Pending Head TPS Approval</value>
            </criteriaItems>
        </entryCriteria>
        <label>Head TPS Approval</label>
        <name>Head_TPS_Approval</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
        <rejectionActions>
            <action>
                <name>Concession_Rejection</name>
                <type>Alert</type>
            </action>
            <action>
                <name>Pending_TSM_Update</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Rejected_by_Head_TPS</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Sub_status_pending_TSM</name>
                <type>FieldUpdate</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <description>[SFP-36385] - Approval process for concessions for Uganda Team. Submitted for approval by the TSM</description>
    <emailTemplate>UG_BCB_General_Email_Templates/Concession_Approval</emailTemplate>
    <enableMobileDeviceAccess>false</enableMobileDeviceAccess>
    <entryCriteria>
        <booleanFilter>1 or 2 or 3</booleanFilter>
        <criteriaItems>
            <field>Concession__c.Pending_Approval_Status__c</field>
            <operation>equals</operation>
            <value>Pending Sector Head Approval</value>
        </criteriaItems>
        <criteriaItems>
            <field>Concession__c.Pending_Approval_Status__c</field>
            <operation>equals</operation>
            <value>Pending TSM Update</value>
        </criteriaItems>
        <criteriaItems>
            <field>Concession__c.Pending_Approval_Status__c</field>
            <operation>equals</operation>
            <value>Pending TSM Approval</value>
        </criteriaItems>
    </entryCriteria>
    <finalApprovalActions>
        <action>
            <name>Update_sub_status_to_approved</name>
            <type>FieldUpdate</type>
        </action>
    </finalApprovalActions>
    <finalApprovalRecordLock>true</finalApprovalRecordLock>
    <finalRejectionRecordLock>false</finalRejectionRecordLock>
    <initialSubmissionActions>
        <action>
            <name>Concession_Approval</name>
            <type>Alert</type>
        </action>
        <action>
            <name>Remove_Rejected</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Sub_Status_with_Sector_head</name>
            <type>FieldUpdate</type>
        </action>
    </initialSubmissionActions>
    <label>Concession_UG_TSM Approval</label>
    <processOrder>2</processOrder>
    <recallActions>
        <action>
            <name>Cancel_Approval</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Pending_Approval_removed</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Recall_and_Reject</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Update_Status_to_Closed</name>
            <type>FieldUpdate</type>
        </action>
    </recallActions>
    <recordEditability>AdminOrCurrentApprover</recordEditability>
    <showApprovalHistory>true</showApprovalHistory>
</ApprovalProcess>
