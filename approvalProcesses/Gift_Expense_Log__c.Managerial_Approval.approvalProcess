<?xml version="1.0" encoding="UTF-8"?>
<ApprovalProcess xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <allowRecall>true</allowRecall>
    <allowedSubmitters>
        <type>owner</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <submitter>Corporate_Investment_Banking</submitter>
        <type>role</type>
    </allowedSubmitters>
    <approvalPageFields>
        <field>Name</field>
        <field>Bank_Contact_Name__c</field>
        <field>Client_Contact_Name__c</field>
        <field>Client_Name__c</field>
        <field>Description__c</field>
        <field>Gift_Expense_Paid_for_By__c</field>
        <field>Gift_Expense_Value__c</field>
        <field>Type__c</field>
        <field>Owner</field>
    </approvalPageFields>
    <approvalStep>
        <allowDelegate>true</allowDelegate>
        <assignedApprover>
            <approver>
                <name>Approver__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <description>Case 1051: Gift / Expense Logs : C Reinecke</description>
        <label>Managerial Approval</label>
        <name>Managerial_Approval</name>
    </approvalStep>
    <description>Case 1051: Gift / Expense Logs : C Reinecke</description>
    <emailTemplate>GEL_Templates/GEL_Approval_Request</emailTemplate>
    <enableMobileDeviceAccess>false</enableMobileDeviceAccess>
    <entryCriteria>
        <criteriaItems>
            <field>Gift_Expense_Log__c.Approver__c</field>
            <operation>notEqual</operation>
        </criteriaItems>
        <criteriaItems>
            <field>Gift_Expense_Log__c.Status__c</field>
            <operation>equals</operation>
            <value>Not Submitted,Rejected</value>
        </criteriaItems>
    </entryCriteria>
    <finalApprovalActions>
        <action>
            <name>GEL_Send_Approval_Notification_to_Request_Submitter</name>
            <type>Alert</type>
        </action>
        <action>
            <name>GEL_Set_Status_Approved</name>
            <type>FieldUpdate</type>
        </action>
    </finalApprovalActions>
    <finalApprovalRecordLock>true</finalApprovalRecordLock>
    <finalRejectionActions>
        <action>
            <name>GEL_Send_Rejection_Notification_to_Request_Submitter</name>
            <type>Alert</type>
        </action>
        <action>
            <name>GEL_Set_Status_Rejected</name>
            <type>FieldUpdate</type>
        </action>
    </finalRejectionActions>
    <finalRejectionRecordLock>false</finalRejectionRecordLock>
    <initialSubmissionActions>
        <action>
            <name>GEL_Clear_Charity_Name</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>GEL_Clear_Rejection_Action</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>GEL_Clear_Rejection_Reason</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>GEL_Set_Status_Submitted</name>
            <type>FieldUpdate</type>
        </action>
    </initialSubmissionActions>
    <label>Managerial Approval</label>
    <processOrder>1</processOrder>
    <recallActions>
        <action>
            <name>GEL_Set_Status_Not_Submitted</name>
            <type>FieldUpdate</type>
        </action>
    </recallActions>
    <recordEditability>AdminOrCurrentApprover</recordEditability>
    <showApprovalHistory>true</showApprovalHistory>
</ApprovalProcess>
