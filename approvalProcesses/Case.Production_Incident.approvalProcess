<?xml version="1.0" encoding="UTF-8"?>
<ApprovalProcess xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <allowRecall>false</allowRecall>
    <allowedSubmitters>
        <type>owner</type>
    </allowedSubmitters>
    <approvalPageFields>
        <field>CaseNumber</field>
        <field>Owner</field>
        <field>CreatedDate</field>
    </approvalPageFields>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <label>Step 1</label>
        <name>Step_1</name>
    </approvalStep>
    <description>Case# C-00005542 - Change Approver</description>
    <enableMobileDeviceAccess>false</enableMobileDeviceAccess>
    <entryCriteria>
        <criteriaItems>
            <field>Case.Project_Workstream__c</field>
            <operation>equals</operation>
            <value>SFDC Production Incident</value>
        </criteriaItems>
        <criteriaItems>
            <field>Case.Case_Stage__c</field>
            <operation>equals</operation>
            <value>Not Approved</value>
        </criteriaItems>
    </entryCriteria>
    <finalApprovalActions>
        <action>
            <name>Case_Stage_Update</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Production_Incident_Approved</name>
            <type>Alert</type>
        </action>
        <action>
            <name>Update_Case_Stuatus</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Update_IT_Resource</name>
            <type>FieldUpdate</type>
        </action>
    </finalApprovalActions>
    <finalApprovalRecordLock>false</finalApprovalRecordLock>
    <finalRejectionActions>
        <action>
            <name>Production_Incident_Case_Rejected</name>
            <type>Alert</type>
        </action>
    </finalRejectionActions>
    <finalRejectionRecordLock>false</finalRejectionRecordLock>
    <initialSubmissionActions>
        <action>
            <name>Production_Incident_submitted_for_approval</name>
            <type>Alert</type>
        </action>
    </initialSubmissionActions>
    <label>Production Incident</label>
    <processOrder>4</processOrder>
    <recordEditability>AdminOnly</recordEditability>
    <showApprovalHistory>true</showApprovalHistory>
</ApprovalProcess>
