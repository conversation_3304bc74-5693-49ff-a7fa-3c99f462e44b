<?xml version="1.0" encoding="UTF-8"?>
<ApprovalProcess xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>false</active>
    <allowRecall>true</allowRecall>
    <allowedSubmitters>
        <submitter>Commercial_Banking</submitter>
        <type>role</type>
    </allowedSubmitters>
    <approvalPageFields>
        <field>Name</field>
        <field>Owner</field>
    </approvalPageFields>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Approval_notification_email_to_Provincial_Pricing_Manager</name>
                <type>Alert</type>
            </action>
            <action>
                <name>Update_sub_status_to_CCM_Approved</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <type>userHierarchyField</type>
            </approver>
        </assignedApprover>
        <description>First approval request to CCM</description>
        <entryCriteria>
            <criteriaItems>
                <field>Concession__c.Status__c</field>
                <operation>equals</operation>
                <value>New</value>
            </criteriaItems>
            <criteriaItems>
                <field>Concession__c.Sub_Status__c</field>
                <operation>equals</operation>
                <value>Pending Approval by CCM</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Approval step for CCM</label>
        <name>Approval_step_for_CCM</name>
        <rejectionActions>
            <action>
                <name>Rejection_notification_email_to_Relationship_Manager</name>
                <type>Alert</type>
            </action>
            <action>
                <name>Update_Status_to_Closed</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Update_sub_status_field_to_CCM_rejected</name>
                <type>FieldUpdate</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>PPM_approval_notification_to_RM</name>
                <type>Alert</type>
            </action>
            <action>
                <name>Update_sub_status_to_PPM_Approved</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name>Pricing_Team_Approver__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <description>Second step for Concession approval once CCM has approved the record</description>
        <entryCriteria>
            <criteriaItems>
                <field>Concession__c.Sub_Status__c</field>
                <operation>equals</operation>
                <value>CCM Approved</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>ApproveRecord</ifCriteriaNotMet>
        <label>Approval step for PPM</label>
        <name>Approval_step_for_PPM</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
        <rejectionActions>
            <action>
                <name>Rejection_notification_email_to_Relationship_Manager</name>
                <type>Alert</type>
            </action>
            <action>
                <name>Update_Status_to_Closed</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Update_sub_status_field_to_PPM_rejected</name>
                <type>FieldUpdate</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>PPM_approval_with_edits_notification_to_RM</name>
                <type>Alert</type>
            </action>
            <action>
                <name>Update_RM_Approved</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name>Relationship_Manager__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <description>Final approval consent to Relationship Manager</description>
        <entryCriteria>
            <criteriaItems>
                <field>Concession__c.Sub_Status__c</field>
                <operation>equals</operation>
                <value>Pricing Team Approved</value>
            </criteriaItems>
            <criteriaItems>
                <field>Concession__c.Modified_By_Pricing_Team__c</field>
                <operation>equals</operation>
                <value>True</value>
            </criteriaItems>
        </entryCriteria>
        <label>Final approval step for Relationship Manager</label>
        <name>Final_approval_step_for_Relationship_Manager</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
        <rejectionActions>
            <action>
                <name>Update_Status_to_Closed</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Update_Sub_Status_to_Cancelled</name>
                <type>FieldUpdate</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <description>US:1615 CommB - CMS - 9 - CCM Approves Concession Request</description>
    <emailTemplate>CRM_User_Support/Concession_PPM_approval_Request_alert</emailTemplate>
    <enableMobileDeviceAccess>false</enableMobileDeviceAccess>
    <finalApprovalActions>
        <action>
            <name>Update_Date_Approved</name>
            <type>FieldUpdate</type>
        </action>
    </finalApprovalActions>
    <finalApprovalRecordLock>true</finalApprovalRecordLock>
    <finalRejectionRecordLock>false</finalRejectionRecordLock>
    <initialSubmissionActions>
        <action>
            <name>Submitter_email_update</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Submitter_name_update</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Update_Status_to_New</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Update_sub_status_field_to_Pending</name>
            <type>FieldUpdate</type>
        </action>
    </initialSubmissionActions>
    <label>Concession record request for approval_3</label>
    <nextAutomatedApprover>
        <useApproverFieldOfRecordOwner>false</useApproverFieldOfRecordOwner>
        <userHierarchyField>Manager</userHierarchyField>
    </nextAutomatedApprover>
    <processOrder>1</processOrder>
    <recallActions>
        <action>
            <name>Untick_Submit_for_Approval</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Update_Status_to_New</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Update_sub_status_field_to_With_RM</name>
            <type>FieldUpdate</type>
        </action>
    </recallActions>
    <recordEditability>AdminOrCurrentApprover</recordEditability>
    <showApprovalHistory>true</showApprovalHistory>
</ApprovalProcess>
