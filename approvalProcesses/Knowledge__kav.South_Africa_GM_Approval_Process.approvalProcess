<?xml version="1.0" encoding="UTF-8"?>
<ApprovalProcess xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <allowRecall>false</allowRecall>
    <allowedSubmitters>
        <type>owner</type>
    </allowedSubmitters>
    <approvalPageFields>
        <field>Title</field>
        <field>ArticleNumber</field>
        <field>Summary</field>
        <field>Owner</field>
        <field>CreatedDate</field>
        <field>IsVisibleInCsp</field>
        <field>RecordType</field>
        <field>Article_Link__c</field>
        <field>OSB_Country__c</field>
    </approvalPageFields>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <type>userHierarchyField</type>
            </approver>
        </assignedApprover>
        <label>Step 1</label>
        <name>Step_1</name>
    </approvalStep>
    <description>SFP-7232 - Approval process for GM approvers to read and check articles created by agents:SFP-10940 -AR Service Business Org: Publishing/ approval of an article(updated to include only South Africa)</description>
    <emailTemplate>Cross_Border_Email_templates/Article_sent_for_approval</emailTemplate>
    <enableMobileDeviceAccess>false</enableMobileDeviceAccess>
    <entryCriteria>
        <criteriaItems>
            <field>Knowledge__kav.RecordType</field>
            <operation>equals</operation>
            <value>How to Guides,Product Info,FAQ</value>
        </criteriaItems>
        <criteriaItems>
            <field>Knowledge__kav.OSB_Country__c</field>
            <operation>equals</operation>
            <value>South Africa</value>
        </criteriaItems>
    </entryCriteria>
    <finalApprovalRecordLock>false</finalApprovalRecordLock>
    <finalRejectionRecordLock>false</finalRejectionRecordLock>
    <label>South Africa GM Approval Process</label>
    <nextAutomatedApprover>
        <useApproverFieldOfRecordOwner>false</useApproverFieldOfRecordOwner>
        <userHierarchyField>Manager</userHierarchyField>
    </nextAutomatedApprover>
    <processOrder>1</processOrder>
    <recordEditability>AdminOrCurrentApprover</recordEditability>
    <showApprovalHistory>true</showApprovalHistory>
</ApprovalProcess>
