<?xml version="1.0" encoding="UTF-8"?>
<ApprovalProcess xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <allowRecall>false</allowRecall>
    <allowedSubmitters>
        <type>owner</type>
    </allowedSubmitters>
    <approvalPageFields>
        <field>Name</field>
        <field>Owner</field>
    </approvalPageFields>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name>Chairperson__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <label>Request Approval from Chairperson</label>
        <name>Request_Approval_from_Chairperson</name>
    </approvalStep>
    <description>SFP-38478 Approval process to get approval from the Chairperson to Send Minutes</description>
    <emailTemplate>Event_Report_Templates/Initial_template_for_Minutes_Approval</emailTemplate>
    <enableMobileDeviceAccess>false</enableMobileDeviceAccess>
    <entryCriteria>
        <criteriaItems>
            <field>Call_Report__c.Approval_Status__c</field>
            <operation>equals</operation>
            <value>Draft</value>
        </criteriaItems>
        <criteriaItems>
            <field>Call_Report__c.RecordType</field>
            <operation>equals</operation>
            <value>NBAC Meeting</value>
        </criteriaItems>
    </entryCriteria>
    <finalApprovalActions>
        <action>
            <name>Approval_Notification_for_Call_Report</name>
            <type>Alert</type>
        </action>
        <action>
            <name>Approval_Status_Approved</name>
            <type>FieldUpdate</type>
        </action>
    </finalApprovalActions>
    <finalApprovalRecordLock>false</finalApprovalRecordLock>
    <finalRejectionActions>
        <action>
            <name>Approval_Status_Rejected</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Rejection_Email_alert_for_Send_Minutes</name>
            <type>Alert</type>
        </action>
    </finalRejectionActions>
    <finalRejectionRecordLock>false</finalRejectionRecordLock>
    <initialSubmissionActions>
        <action>
            <name>Approval_Status_Submitted</name>
            <type>FieldUpdate</type>
        </action>
    </initialSubmissionActions>
    <label>Approve Send Minutes</label>
    <processOrder>1</processOrder>
    <recallActions>
        <action>
            <name>Approval_Status_Draft</name>
            <type>FieldUpdate</type>
        </action>
    </recallActions>
    <recordEditability>AdminOrCurrentApprover</recordEditability>
    <showApprovalHistory>true</showApprovalHistory>
</ApprovalProcess>
