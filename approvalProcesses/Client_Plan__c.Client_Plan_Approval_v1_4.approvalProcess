<?xml version="1.0" encoding="UTF-8"?>
<ApprovalProcess xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <allowRecall>true</allowRecall>
    <allowedSubmitters>
        <type>creator</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <submitter>CIB_Private_Clients</submitter>
        <type>roleSubordinates</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <submitter>Corporate_Investment_Banking</submitter>
        <type>roleSubordinates</type>
    </allowedSubmitters>
    <approvalPageFields>
        <field>Name</field>
        <field>Client_Plan_Status__c</field>
    </approvalPageFields>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Approved_Rejected_by_Running_user</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Approved_Rejected_date_Now</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Update_To_Approve</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name>Additional_Approver_2__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Current_CC_Line_Manager__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Additional_Approver__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <booleanFilter>1 AND 2 AND 3</booleanFilter>
            <criteriaItems>
                <field>Client_Plan__c.Current_CC_Line_Manager__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Client_Plan__c.Additional_Approver__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Client_Plan__c.Additional_Approver_2__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Current CC Line Manager, Executive Sponsor And Sector/Client Coverage Head</label>
        <name>Current_CC_Line_Manager_And_Executive_Sponsor</name>
        <rejectionActions>
            <action>
                <name>Approved_Rejected_by_Running_user</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Approved_Rejected_date_Now</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Update_To_Reject</name>
                <type>FieldUpdate</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Approved_Rejected_by_Running_user</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Approved_Rejected_date_Now</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Update_To_Approve</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name>Current_CC_Line_Manager__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Additional_Approver__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Client_Plan__c.Current_CC_Line_Manager__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Client_Plan__c.Additional_Approver__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Client_Plan__c.Client_Plan_Status__c</field>
                <operation>equals</operation>
                <value>Submitted For Approval</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Current CC Line Manager or Executive Sponsor</label>
        <name>Current_CC_Line_Manager_or_Executive_Sponsor</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
        <rejectionActions>
            <action>
                <name>Approved_Rejected_by_Running_user</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Approved_Rejected_date_Now</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Update_To_Reject</name>
                <type>FieldUpdate</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Approved_Rejected_by_Running_user</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Approved_Rejected_date_Now</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Update_To_Approve</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name>Additional_Approver_2__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Current_CC_Line_Manager__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Client_Plan__c.Current_CC_Line_Manager__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Client_Plan__c.Additional_Approver_2__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Client_Plan__c.Client_Plan_Status__c</field>
                <operation>equals</operation>
                <value>Submitted For Approval</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Current CC Line Manager And Sector/Client Coverage Head</label>
        <name>Current_CC_Line_Manager_And_Sector_Client_Coverage_Head</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
        <rejectionActions>
            <action>
                <name>Approved_Rejected_by_Running_user</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Approved_Rejected_date_Now</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Update_To_Reject</name>
                <type>FieldUpdate</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Approved_Rejected_by_Running_user</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Approved_Rejected_date_Now</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Update_To_Approve</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name>Additional_Approver_2__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Additional_Approver__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Client_Plan__c.Additional_Approver__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Client_Plan__c.Additional_Approver_2__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Client_Plan__c.Client_Plan_Status__c</field>
                <operation>equals</operation>
                <value>Submitted For Approval</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Executive Sponsor And Sector/Client Coverage Head</label>
        <name>Executive_Sponsor_And_Sector_Client_Coverage_Head</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
        <rejectionActions>
            <action>
                <name>Approved_Rejected_by_Running_user</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Approved_Rejected_date_Now</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Update_To_Reject</name>
                <type>FieldUpdate</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Approved_Rejected_by_Running_user</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Approved_Rejected_date_Now</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Update_To_Approve</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name>Current_CC_Line_Manager__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Client_Plan__c.Current_CC_Line_Manager__c</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Client_Plan__c.Client_Plan_Status__c</field>
                <operation>equals</operation>
                <value>Submitted For Approval</value>
            </criteriaItems>
        </entryCriteria>
        <label>Current CC Line Manager</label>
        <name>Current_CC_Line_Manager</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
        <rejectionActions>
            <action>
                <name>Approved_Rejected_by_Running_user</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Approved_Rejected_date_Now</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Update_To_Reject</name>
                <type>FieldUpdate</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <description>SFP-4537-Client Plan approval process when client plan is submitted for approval.
SFP-31349 - fix</description>
    <emailTemplate>unfiled$public/Approval_Notification_Email</emailTemplate>
    <enableMobileDeviceAccess>false</enableMobileDeviceAccess>
    <entryCriteria>
        <criteriaItems>
            <field>Client_Plan__c.Can_be_Submitted__c</field>
            <operation>equals</operation>
            <value>True</value>
        </criteriaItems>
    </entryCriteria>
    <finalApprovalActions>
        <action>
            <name>Can_be_submitted_False</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Client_Plan_Approval</name>
            <type>Alert</type>
        </action>
    </finalApprovalActions>
    <finalApprovalRecordLock>false</finalApprovalRecordLock>
    <finalRejectionActions>
        <action>
            <name>Can_be_submitted_False</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Client_Plan_Rejected</name>
            <type>Alert</type>
        </action>
        <action>
            <name>Update_To_Reject</name>
            <type>FieldUpdate</type>
        </action>
    </finalRejectionActions>
    <finalRejectionRecordLock>false</finalRejectionRecordLock>
    <initialSubmissionActions>
        <action>
            <name>GetSubmitterEmail</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Update_Submitted_for_Approval</name>
            <type>FieldUpdate</type>
        </action>
    </initialSubmissionActions>
    <label>Client Plan Approval v1.4</label>
    <nextAutomatedApprover>
        <useApproverFieldOfRecordOwner>false</useApproverFieldOfRecordOwner>
        <userHierarchyField>Manager</userHierarchyField>
    </nextAutomatedApprover>
    <processOrder>1</processOrder>
    <recallActions>
        <action>
            <name>Recall_Status_Update</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Revaluate_Can_Be_Submitted</name>
            <type>FieldUpdate</type>
        </action>
    </recallActions>
    <recordEditability>AdminOnly</recordEditability>
    <showApprovalHistory>true</showApprovalHistory>
</ApprovalProcess>
