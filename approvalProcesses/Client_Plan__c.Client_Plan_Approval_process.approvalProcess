<?xml version="1.0" encoding="UTF-8"?>
<ApprovalProcess xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>false</active>
    <allowRecall>false</allowRecall>
    <allowedSubmitters>
        <type>creator</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <submitter>CIB_Private_Clients</submitter>
        <type>roleSubordinates</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <submitter>Corporate_Investment_Banking</submitter>
        <type>roleSubordinates</type>
    </allowedSubmitters>
    <approvalPageFields>
        <field>Name</field>
        <field>Client_Plan_Status__c</field>
    </approvalPageFields>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Approved_Rejected_by_Running_user</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Approved_Rejected_date_Now</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Update_To_Approve</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name>Submitter_Line_Manager__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <booleanFilter>1 AND 2</booleanFilter>
            <criteriaItems>
                <field>User.Manager</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Account.Client_Sector__c</field>
                <operation>equals</operation>
                <value>Consumer</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Approval required from Requestor&apos;s Line Manager or Consumer Sector Management</label>
        <name>Approval_required_from_Requestor_s_Line_Manager_or_Consumer_Sector_Management</name>
        <rejectionActions>
            <action>
                <name>Approved_Rejected_by_Running_user</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Approved_Rejected_date_Now</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Update_To_Reject</name>
                <type>FieldUpdate</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Approved_Rejected_by_Running_user</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Approved_Rejected_date_Now</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Update_To_Approve</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name>Submitter_Line_Manager__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>User.Manager</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Account.Client_Sector__c</field>
                <operation>equals</operation>
                <value>Financial Institutions</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Approval required from Requestor&apos;s Line Manager or Financial Institutions Sector Management</label>
        <name>Approval_required_from_Requestor_s_Line_Manager_or_Financial_Institutions_Sector</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
        <rejectionActions>
            <action>
                <name>Approved_Rejected_by_Running_user</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Approved_Rejected_date_Now</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Update_To_Reject</name>
                <type>FieldUpdate</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Approved_Rejected_by_Running_user</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Approved_Rejected_date_Now</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Update_To_Approve</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name>Submitter_Line_Manager__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>User.Manager</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Account.Client_Sector__c</field>
                <operation>equals</operation>
                <value>Telecoms and Media</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Approval required from Requestor&apos;s Line Manager or Telecoms and Media Sector Management</label>
        <name>Approval_required_from_Requestor_s_Line_Manager_or_Telecoms_and_Media_Sector_Man</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
        <rejectionActions>
            <action>
                <name>Approved_Rejected_by_Running_user</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Approved_Rejected_date_Now</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Update_To_Reject</name>
                <type>FieldUpdate</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Approved_Rejected_by_Running_user</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Approved_Rejected_date_Now</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Update_To_Approve</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name>Submitter_Line_Manager__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>User.Manager</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Account.Client_Sector__c</field>
                <operation>equals</operation>
                <value>Mining and Metals</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Approval required from Requestor&apos;s Line Manager or Mining and Metals Sector Management</label>
        <name>Approval_required_from_Requestor_s_Line_Manager_or_Mining_and_Metals_Sector_Mana</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
        <rejectionActions>
            <action>
                <name>Approved_Rejected_by_Running_user</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Approved_Rejected_date_Now</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Update_To_Reject</name>
                <type>FieldUpdate</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Approved_Rejected_by_Running_user</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Approved_Rejected_date_Now</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Update_To_Approve</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name>Submitter_Line_Manager__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>User.Manager</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Account.Client_Sector__c</field>
                <operation>equals</operation>
                <value>Oil and Gas</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Approval required from Requestor&apos;s Line Manager or Oli &amp; Gas Sector Management</label>
        <name>Approval_required_from_Requestor_s_Line_Manager_or_Oli_Gas_Sector_Management</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
        <rejectionActions>
            <action>
                <name>Approved_Rejected_by_Running_user</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Approved_Rejected_date_Now</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Update_To_Reject</name>
                <type>FieldUpdate</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Approved_Rejected_by_Running_user</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Approved_Rejected_date_Now</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Update_To_Approve</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name>Submitter_Line_Manager__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>User.Manager</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Account.Client_Sector__c</field>
                <operation>equals</operation>
                <value>Power and Infrastructure</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Approval required from Requestor&apos;s Line Manager or Power &amp; Infrastructure Sector Management</label>
        <name>Approval_required_from_Requestor_s_Line_Manager_or_Power_Infrastructure_Sector_M</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
        <rejectionActions>
            <action>
                <name>Approved_Rejected_by_Running_user</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Approved_Rejected_date_Now</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Update_To_Reject</name>
                <type>FieldUpdate</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Approved_Rejected_by_Running_user</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Approved_Rejected_date_Now</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Update_To_Approve</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name>Submitter_Line_Manager__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>User.Manager</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Account.Client_Sector__c</field>
                <operation>equals</operation>
                <value>Financial Institutions</value>
            </criteriaItems>
            <criteriaItems>
                <field>Account.Client_Sub_Sector__c</field>
                <operation>equals</operation>
                <value>Asset Managers,Brokers and Exchanges,Central Banks,Insurance,Niche Finance Companies,Pension and Regulated Funds</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Approval required from Requestor&apos;s Line Manager or NBFI Management</label>
        <name>Approval_required_from_Requestor_s_Line_Manager_or_NBFI_Management</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
        <rejectionActions>
            <action>
                <name>Approved_Rejected_by_Running_user</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Approved_Rejected_date_Now</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Update_To_Reject</name>
                <type>FieldUpdate</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Approved_Rejected_by_Running_user</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Approved_Rejected_date_Now</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Update_To_Approve</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name>Submitter_Line_Manager__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>User.Manager</field>
                <operation>notEqual</operation>
            </criteriaItems>
            <criteriaItems>
                <field>Account.Client_Sector__c</field>
                <operation>equals</operation>
                <value>Industrials,Internal / Grouped,Real Estate,Sovereign / Public Sector,Unknown Sector</value>
            </criteriaItems>
        </entryCriteria>
        <label>Approval required from Requestor&apos;s Line Manager</label>
        <name>Approval_required_from_Requestor_s_Line_Manager</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
        <rejectionActions>
            <action>
                <name>Approved_Rejected_by_Running_user</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Approved_Rejected_date_Now</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Update_To_Reject</name>
                <type>FieldUpdate</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <description>Client Plan approval process when client plan is submitted for approval.</description>
    <emailTemplate>unfiled$public/Approval_Notification_Email</emailTemplate>
    <enableMobileDeviceAccess>false</enableMobileDeviceAccess>
    <entryCriteria>
        <criteriaItems>
            <field>Client_Plan__c.Can_be_Submitted__c</field>
            <operation>equals</operation>
            <value>True</value>
        </criteriaItems>
    </entryCriteria>
    <finalApprovalActions>
        <action>
            <name>Can_be_submitted_False</name>
            <type>FieldUpdate</type>
        </action>
    </finalApprovalActions>
    <finalApprovalRecordLock>false</finalApprovalRecordLock>
    <finalRejectionActions>
        <action>
            <name>Can_be_submitted_False</name>
            <type>FieldUpdate</type>
        </action>
    </finalRejectionActions>
    <finalRejectionRecordLock>false</finalRejectionRecordLock>
    <initialSubmissionActions>
        <action>
            <name>Update_Submitted_for_Approval</name>
            <type>FieldUpdate</type>
        </action>
    </initialSubmissionActions>
    <label>Client Plan Approval</label>
    <nextAutomatedApprover>
        <useApproverFieldOfRecordOwner>false</useApproverFieldOfRecordOwner>
        <userHierarchyField>Manager</userHierarchyField>
    </nextAutomatedApprover>
    <processOrder>1</processOrder>
    <recordEditability>AdminOnly</recordEditability>
    <showApprovalHistory>true</showApprovalHistory>
</ApprovalProcess>
