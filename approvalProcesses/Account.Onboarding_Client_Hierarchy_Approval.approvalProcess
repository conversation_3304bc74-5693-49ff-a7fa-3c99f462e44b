<?xml version="1.0" encoding="UTF-8"?>
<ApprovalProcess xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <allowRecall>true</allowRecall>
    <allowedSubmitters>
        <type>owner</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <submitter>Commercial_Banking</submitter>
        <type>roleSubordinates</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <submitter>Corporate_Investment_Banking</submitter>
        <type>roleSubordinates</type>
    </allowedSubmitters>
    <approvalPageFields>
        <field>Owner</field>
        <field>Name</field>
        <field>Client_Relationship_Hierarchy__c</field>
        <field>Proposed_Group_Parent__c</field>
        <field>Proposed_Immediate_Parent__c</field>
        <field>Client_Sector__c</field>
        <field>Client_Sub_Sector__c</field>
        <field>ISIC_C_ode__c</field>
    </approvalPageFields>
    <approvalStep>
        <allowDelegate>true</allowDelegate>
        <approvalActions>
            <action>
                <name>Account_Status_update_to_Approved</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Alert_CC_of_Proposed_Group_Parent</name>
                <type>Alert</type>
            </action>
            <action>
                <name>Onboarding_Hierarchy_Approval_alert</name>
                <type>Alert</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name>Client_Co_ordinator__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <description>Client Relationship Hierarchy, Sector and CC Approval</description>
        <entryCriteria>
            <criteriaItems>
                <field>Account.Client_Relationship_Hierarchy__c</field>
                <operation>contains</operation>
                <value>Group Parent</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Client Relationship Hierarchy, Sector and CC Approval</label>
        <name>Client_Relationship_Hierarchy_Sector_and_CC_Approval</name>
        <rejectionActions>
            <action>
                <name>Account_Status_update_to_Rejected</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Onboarding_Hierarchy_Rejection_Alert</name>
                <type>Alert</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <approvalStep>
        <allowDelegate>true</allowDelegate>
        <approvalActions>
            <action>
                <name>Account_Status_update_to_Approved</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Alert_CC_of_Proposed_Group_Parent</name>
                <type>Alert</type>
            </action>
            <action>
                <name>Onboarding_Hierarchy_Approval_alert</name>
                <type>Alert</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name>Client_Co_ordinator__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <description>Client Relationship Hierarchy, Sector and CC Approval</description>
        <entryCriteria>
            <criteriaItems>
                <field>Account.Client_Relationship_Hierarchy__c</field>
                <operation>contains</operation>
                <value>Immediate Parent</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Client Relationship Hierarchy, Sector and CC Approval</label>
        <name>Client_Relationship_Hierarchy_Sector_and_CC_Approval_2</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
        <rejectionActions>
            <action>
                <name>Account_Status_update_to_Rejected</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Onboarding_Hierarchy_Rejection_Alert</name>
                <type>Alert</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <approvalStep>
        <allowDelegate>true</allowDelegate>
        <approvalActions>
            <action>
                <name>Account_Status_update_to_Approved</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Alert_CC_of_Proposed_Group_Parent</name>
                <type>Alert</type>
            </action>
            <action>
                <name>Onboarding_Hierarchy_Approval_alert</name>
                <type>Alert</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name>Client_Co_ordinator__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <description>Client Relationship Hierarchy, Sector and CC Approval</description>
        <entryCriteria>
            <criteriaItems>
                <field>Account.Client_Relationship_Hierarchy__c</field>
                <operation>contains</operation>
                <value>Child</value>
            </criteriaItems>
        </entryCriteria>
        <label>Client Relationship Hierarchy, Sector and CC Approval</label>
        <name>Client_Relationship_Hierarchy_Sector_and_CC_Approval_3</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
        <rejectionActions>
            <action>
                <name>Account_Status_update_to_Rejected</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Onboarding_Hierarchy_Rejection_Alert</name>
                <type>Alert</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <description>Approval process for the selection of Top Parent or Immediate Parent from the Client Relationship Hierarchy with ability for a Delegated Approver to Approve Client during the process. (EN-0753,EN-808 and US-1655)</description>
    <emailTemplate>Onboarding_Notifications/Group_Parent_Immediate_Parent_Approval_Request</emailTemplate>
    <enableMobileDeviceAccess>false</enableMobileDeviceAccess>
    <entryCriteria>
        <booleanFilter>(1 OR (2 AND 8) OR (4 AND 8)) AND 3 AND 5 AND 6 AND 7</booleanFilter>
        <criteriaItems>
            <field>Account.Client_Relationship_Hierarchy__c</field>
            <operation>contains</operation>
            <value>Group Parent</value>
        </criteriaItems>
        <criteriaItems>
            <field>Account.Client_Relationship_Hierarchy__c</field>
            <operation>contains</operation>
            <value>Immediate Parent</value>
        </criteriaItems>
        <criteriaItems>
            <field>Account.Status__c</field>
            <operation>notContain</operation>
            <value>Approved,Submitted for Onboarding</value>
        </criteriaItems>
        <criteriaItems>
            <field>Account.Client_Relationship_Hierarchy__c</field>
            <operation>contains</operation>
            <value>Child</value>
        </criteriaItems>
        <criteriaItems>
            <field>Account.Client_Sector__c</field>
            <operation>notEqual</operation>
        </criteriaItems>
        <criteriaItems>
            <field>Account.Client_Sub_Sector__c</field>
            <operation>notEqual</operation>
        </criteriaItems>
        <criteriaItems>
            <field>Account.Client_Co_ordinator__c</field>
            <operation>notEqual</operation>
        </criteriaItems>
        <criteriaItems>
            <field>Account.Parent</field>
            <operation>notEqual</operation>
        </criteriaItems>
    </entryCriteria>
    <finalApprovalRecordLock>false</finalApprovalRecordLock>
    <finalRejectionRecordLock>false</finalRejectionRecordLock>
    <initialSubmissionActions>
        <action>
            <name>Status_update_to_Submitted_for_Approval</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Submitter_email_update</name>
            <type>FieldUpdate</type>
        </action>
    </initialSubmissionActions>
    <label>Onboarding: Client Hierarchy Approval</label>
    <nextAutomatedApprover>
        <useApproverFieldOfRecordOwner>true</useApproverFieldOfRecordOwner>
        <userHierarchyField>Manager</userHierarchyField>
    </nextAutomatedApprover>
    <processOrder>1</processOrder>
    <recallActions>
        <action>
            <name>Update_Status_on_Hierarchy_change</name>
            <type>FieldUpdate</type>
        </action>
    </recallActions>
    <recordEditability>AdminOnly</recordEditability>
    <showApprovalHistory>false</showApprovalHistory>
</ApprovalProcess>
