<?xml version="1.0" encoding="UTF-8"?>
<ApprovalProcess xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <allowRecall>false</allowRecall>
    <allowedSubmitters>
        <type>creator</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <type>owner</type>
    </allowedSubmitters>
    <approvalPageFields>
        <field>Title</field>
        <field>Owner</field>
        <field>RecordType</field>
        <field>ArticleNumber</field>
        <field>ArticleType</field>
        <field>OSB_Country__c</field>
        <field>CreatedBy</field>
        <field>Created_date__c</field>
        <field>Info__c</field>
        <field>Summary</field>
        <field>Key_Words__c</field>
        <field>PublishStatus</field>
        <field>IsLatestVersion</field>
        <field>IsVisibleInApp</field>
        <field>IsVisibleInCsp</field>
        <field>IsVisibleInPrm</field>
        <field>Language</field>
    </approvalPageFields>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name>UG_BCC_First_Level_Articles_Approvers</name>
                <type>queue</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <label>First Level Approval</label>
        <name>First_Level_Approval</name>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name>UG_BCC_Final_Level_Articles_Approvers</name>
                <type>queue</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <label>Final Level Approval</label>
        <name>Final_Level_Approval</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <description>SFP-13593 - Approval process for Manager approvers to read and check articles created by agents.</description>
    <emailTemplate>Uganda_BCC_Templates/SVC_UG_BCC_Article_sent_for_approval</emailTemplate>
    <enableMobileDeviceAccess>false</enableMobileDeviceAccess>
    <entryCriteria>
        <criteriaItems>
            <field>Knowledge__kav.RecordType</field>
            <operation>equals</operation>
            <value>FAQ,How to Guides,Product Info</value>
        </criteriaItems>
        <criteriaItems>
            <field>User.Business_Unit__c</field>
            <operation>equals</operation>
            <value>BCC</value>
        </criteriaItems>
        <criteriaItems>
            <field>Knowledge__kav.OSB_Country__c</field>
            <operation>equals</operation>
            <value>Uganda</value>
        </criteriaItems>
    </entryCriteria>
    <finalApprovalActions>
        <action>
            <name>Publish_Article</name>
            <type>KnowledgePublish</type>
        </action>
        <action>
            <name>SVC_Article_Approved_Email_to_Agent</name>
            <type>Alert</type>
        </action>
    </finalApprovalActions>
    <finalApprovalRecordLock>false</finalApprovalRecordLock>
    <finalRejectionRecordLock>false</finalRejectionRecordLock>
    <label>UG BCC Article Approval Process</label>
    <processOrder>3</processOrder>
    <recordEditability>AdminOrCurrentApprover</recordEditability>
    <showApprovalHistory>true</showApprovalHistory>
</ApprovalProcess>
