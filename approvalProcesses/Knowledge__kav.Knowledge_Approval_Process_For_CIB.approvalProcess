<?xml version="1.0" encoding="UTF-8"?>
<ApprovalProcess xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <allowRecall>false</allowRecall>
    <allowedSubmitters>
        <type>owner</type>
    </allowedSubmitters>
    <approvalPageFields>
        <field>Title</field>
        <field>ArticleNumber</field>
        <field>Summary</field>
        <field>Owner</field>
        <field>CreatedDate</field>
        <field>IsVisibleInCsp</field>
        <field>RecordType</field>
        <field>Article_Link__c</field>
        <field>OSB_Country__c</field>
    </approvalPageFields>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name>debbie.van<PERSON><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Knowledge__kav.OSB_Country__c</field>
                <operation>equals</operation>
                <value>Angola</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Step 1</label>
        <name>Step_1</name>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Knowledge__kav.OSB_Country__c</field>
                <operation>equals</operation>
                <value>Botswana</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Step 2</label>
        <name>Step_2</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Knowledge__kav.OSB_Country__c</field>
                <operation>equals</operation>
                <value>Democratic Republic of Congo</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Step 3</label>
        <name>Step_3</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Knowledge__kav.OSB_Country__c</field>
                <operation>equals</operation>
                <value>Ghana</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>step 4</label>
        <name>step_4</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Knowledge__kav.OSB_Country__c</field>
                <operation>equals</operation>
                <value>Lesotho</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>step 5</label>
        <name>step_5</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Knowledge__kav.OSB_Country__c</field>
                <operation>equals</operation>
                <value>Malawi</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Step 6</label>
        <name>Step_6</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Knowledge__kav.OSB_Country__c</field>
                <operation>equals</operation>
                <value>Namibia</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Step 7</label>
        <name>Step_7</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Knowledge__kav.OSB_Country__c</field>
                <operation>equals</operation>
                <value>Mauritius</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Step 8</label>
        <name>Step_8</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Knowledge__kav.OSB_Country__c</field>
                <operation>equals</operation>
                <value>Tanzania</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Step 9</label>
        <name>Step_9</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Knowledge__kav.OSB_Country__c</field>
                <operation>equals</operation>
                <value>Uganda</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Step 10</label>
        <name>Step_10</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Knowledge__kav.OSB_Country__c</field>
                <operation>equals</operation>
                <value>Zambia</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Step 11</label>
        <name>Step_11</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>Knowledge__kav.OSB_Country__c</field>
                <operation>equals</operation>
                <value>Zimbabwe</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Step 12</label>
        <name>Step_12</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <description>for all other except SA</description>
        <entryCriteria>
            <booleanFilter>1 AND 2 AND 3 AND 4 AND 5 AND 6 AND 7 AND 8 AND 9 AND 10 AND 11 AND 12 AND 13</booleanFilter>
            <criteriaItems>
                <field>Knowledge__kav.OSB_Country__c</field>
                <operation>includes</operation>
                <value>Angola,Botswana,Cote d’Ivoire,Democratic Republic of Congo,Ghana,Kenya,Lesotho,Malawi,Mauritius,Namibia,South Sudan,Swaziland/Eswatini,Tanzania,Uganda,Zambia,Zimbabwe</value>
            </criteriaItems>
            <criteriaItems>
                <field>Knowledge__kav.OSB_Country__c</field>
                <operation>notEqual</operation>
                <value>Angola</value>
            </criteriaItems>
            <criteriaItems>
                <field>Knowledge__kav.OSB_Country__c</field>
                <operation>notEqual</operation>
                <value>Botswana</value>
            </criteriaItems>
            <criteriaItems>
                <field>Knowledge__kav.OSB_Country__c</field>
                <operation>notEqual</operation>
                <value>Democratic Republic of Congo</value>
            </criteriaItems>
            <criteriaItems>
                <field>Knowledge__kav.OSB_Country__c</field>
                <operation>notEqual</operation>
                <value>Ghana</value>
            </criteriaItems>
            <criteriaItems>
                <field>Knowledge__kav.OSB_Country__c</field>
                <operation>notEqual</operation>
                <value>Lesotho</value>
            </criteriaItems>
            <criteriaItems>
                <field>Knowledge__kav.OSB_Country__c</field>
                <operation>notEqual</operation>
                <value>Malawi</value>
            </criteriaItems>
            <criteriaItems>
                <field>Knowledge__kav.OSB_Country__c</field>
                <operation>notEqual</operation>
                <value>Mauritius</value>
            </criteriaItems>
            <criteriaItems>
                <field>Knowledge__kav.OSB_Country__c</field>
                <operation>notEqual</operation>
                <value>Namibia</value>
            </criteriaItems>
            <criteriaItems>
                <field>Knowledge__kav.OSB_Country__c</field>
                <operation>notEqual</operation>
                <value>Tanzania</value>
            </criteriaItems>
            <criteriaItems>
                <field>Knowledge__kav.OSB_Country__c</field>
                <operation>notEqual</operation>
                <value>Uganda</value>
            </criteriaItems>
            <criteriaItems>
                <field>Knowledge__kav.OSB_Country__c</field>
                <operation>notEqual</operation>
                <value>Zambia</value>
            </criteriaItems>
            <criteriaItems>
                <field>Knowledge__kav.OSB_Country__c</field>
                <operation>notEqual</operation>
                <value>Zimbabwe</value>
            </criteriaItems>
        </entryCriteria>
        <label>Step 13</label>
        <name>Step_13</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <description>SFP-10940 -AR Service Business Org: Publishing/ approval of an article</description>
    <emailTemplate>Cross_Border_Email_templates/Article_sent_for_approval</emailTemplate>
    <enableMobileDeviceAccess>false</enableMobileDeviceAccess>
    <entryCriteria>
        <criteriaItems>
            <field>Knowledge__kav.RecordType</field>
            <operation>equals</operation>
            <value>How to Guides,Product Info,FAQ</value>
        </criteriaItems>
        <criteriaItems>
            <field>Knowledge__kav.OSB_Country__c</field>
            <operation>notEqual</operation>
            <value>South Africa</value>
        </criteriaItems>
        <criteriaItems>
            <field>User.Business_Unit__c</field>
            <operation>equals</operation>
            <value>CIB</value>
        </criteriaItems>
    </entryCriteria>
    <finalApprovalRecordLock>false</finalApprovalRecordLock>
    <finalRejectionRecordLock>false</finalRejectionRecordLock>
    <label>Knowledge Approval Process For CIB</label>
    <nextAutomatedApprover>
        <useApproverFieldOfRecordOwner>false</useApproverFieldOfRecordOwner>
        <userHierarchyField>Manager</userHierarchyField>
    </nextAutomatedApprover>
    <processOrder>2</processOrder>
    <recordEditability>AdminOrCurrentApprover</recordEditability>
    <showApprovalHistory>true</showApprovalHistory>
</ApprovalProcess>
