<?xml version="1.0" encoding="UTF-8"?>
<ApprovalProcess xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <allowRecall>true</allowRecall>
    <allowedSubmitters>
        <type>owner</type>
    </allowedSubmitters>
    <approvalPageFields>
        <field>Name</field>
        <field>Owner</field>
        <field>New_Client_Coordinator__c</field>
        <field>New_Client_Coordinator_Role__c</field>
        <field>Current_Client_Coordinator__c</field>
        <field>Current_Client_Coordinator_Role__c</field>
    </approvalPageFields>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Approval_Step_Completed</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name>Additional_Approver__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Current_Client_Coordinator__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>New_Client_Coordinator__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>Unanimous</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <formula>ISPICKVAL(Status__c , &apos;Submitted For Approval&apos;) &amp;&amp;
Additional_Approver__c != null &amp;&amp;
Additional_Approver__c != $User.Id &amp;&amp; 
New_Client_Coordinator__c  != null &amp;&amp;
New_Client_Coordinator__c  != $User.Id &amp;&amp;
Current_Client_Coordinator__c  != null &amp;&amp; 
Current_Client_Coordinator__c  != $User.Id &amp;&amp;  
Current_CCBM__c != $User.Id &amp;&amp;
NOT(CONTAINS(Current_Client_Coordinator__r.Username, &apos;<EMAIL>&apos;))</formula>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Approval Request</label>
        <name>All_Approvers_Required</name>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Approval_Step_Completed</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name>Current_Client_Coordinator__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>New_Client_Coordinator__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>Unanimous</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <formula>ISPICKVAL(Status__c , &apos;Submitted For Approval&apos;) &amp;&amp;
(Additional_Approver__c == null ||
Additional_Approver__c  == $User.Id) &amp;&amp;
New_Client_Coordinator__c != null &amp;&amp; 
New_Client_Coordinator__c != $User.Id &amp;&amp; 
Current_Client_Coordinator__c != null &amp;&amp; 
Current_Client_Coordinator__c != $User.Id &amp;&amp;  
Current_CCBM__c != $User.Id &amp;&amp;
NOT(CONTAINS(Current_Client_Coordinator__r.Username, &apos;<EMAIL>&apos;))</formula>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Approval Request</label>
        <name>All_CC_Required</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Approval_Step_Completed</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name>Current_Client_Coordinator__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <formula>ISPICKVAL(Status__c , &apos;Submitted For Approval&apos;) &amp;&amp;
Additional_Approver__c == null &amp;&amp; 
(New_Client_Coordinator__c != null &amp;&amp; 
New_Client_Coordinator__c == $User.Id) &amp;&amp;
Current_Client_Coordinator__c != null &amp;&amp; 
Current_Client_Coordinator__c != $User.Id &amp;&amp; 
Current_CCBM__c != $User.Id</formula>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Approval Request</label>
        <name>New_CC_Not_Required</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Approval_Step_Completed</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name>Additional_Approver__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>Current_Client_Coordinator__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>Unanimous</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <formula>ISPICKVAL(Status__c , &apos;Submitted For Approval&apos;) &amp;&amp;
Additional_Approver__c != null &amp;&amp; 
(New_Client_Coordinator__c != null &amp;&amp; 
New_Client_Coordinator__c == $User.Id) &amp;&amp; 
Current_Client_Coordinator__c != null &amp;&amp; 
Current_CCBM__c != $User.Id &amp;&amp;
Current_Client_Coordinator__c != $User.Id &amp;&amp; 
NOT(CONTAINS(Current_Client_Coordinator__r.Username, &apos;<EMAIL>&apos;))</formula>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Approval Request</label>
        <name>New_CC_Not_Required_Role_Required</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Approval_Step_Completed</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name>New_Client_Coordinator__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>Unanimous</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <formula>ISPICKVAL(Status__c , &apos;Submitted For Approval&apos;) &amp;&amp;
Additional_Approver__c == null &amp;&amp; 
New_Client_Coordinator__c != null &amp;&amp; 
New_Client_Coordinator__c != $User.Id &amp;&amp; 
(Current_Client_Coordinator__c != null &amp;&amp; 
(Current_Client_Coordinator__c == $User.Id || 
Current_CCBM__c == $User.Id ||
CONTAINS(Current_Client_Coordinator__r.Username, &apos;<EMAIL>&apos;)))</formula>
        </entryCriteria>
        <ifCriteriaNotMet>GotoNextStep</ifCriteriaNotMet>
        <label>Approval Request</label>
        <name>Current_CC_Not_Required</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Approval_Step_Completed</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name>Additional_Approver__c</name>
                <type>relatedUserField</type>
            </approver>
            <approver>
                <name>New_Client_Coordinator__c</name>
                <type>relatedUserField</type>
            </approver>
            <whenMultipleApprovers>Unanimous</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <formula>ISPICKVAL(Status__c, &apos;Submitted For Approval&apos;) &amp;&amp;
Additional_Approver__c != null &amp;&amp; 
(New_Client_Coordinator__c != null &amp;&amp; 
New_Client_Coordinator__c != $User.Id) &amp;&amp; 
(Current_Client_Coordinator__c != null &amp;&amp; 
(Current_Client_Coordinator__c == $User.Id || 
Current_CCBM__c == $User.Id ||
CONTAINS(Current_Client_Coordinator__r.Username, &apos;<EMAIL>&apos;)))</formula>
        </entryCriteria>
        <label>Approval Request</label>
        <name>Current_CC_Not_Required_Role_Required</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
    </approvalStep>
    <emailTemplate>unfiled$public/CDC_Approval_Initial_Message</emailTemplate>
    <enableMobileDeviceAccess>false</enableMobileDeviceAccess>
    <entryCriteria>
        <criteriaItems>
            <field>Client_Data_Change__c.Status__c</field>
            <operation>equals</operation>
            <value>New</value>
        </criteriaItems>
    </entryCriteria>
    <finalApprovalActions>
        <action>
            <name>CDC_Status_to_Approved</name>
            <type>FieldUpdate</type>
        </action>
    </finalApprovalActions>
    <finalApprovalRecordLock>true</finalApprovalRecordLock>
    <finalRejectionActions>
        <action>
            <name>CDC_status_to_Rejected</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Send_Change_Rejected_Message</name>
            <type>Alert</type>
        </action>
    </finalRejectionActions>
    <finalRejectionRecordLock>false</finalRejectionRecordLock>
    <initialSubmissionActions>
        <action>
            <name>CDC_status_to_Submitted_For_Approval</name>
            <type>FieldUpdate</type>
        </action>
    </initialSubmissionActions>
    <label>Update CC</label>
    <processOrder>1</processOrder>
    <recallActions>
        <action>
            <name>CDC_Status_to_Cancelled</name>
            <type>FieldUpdate</type>
        </action>
    </recallActions>
    <recordEditability>AdminOnly</recordEditability>
    <showApprovalHistory>false</showApprovalHistory>
</ApprovalProcess>
