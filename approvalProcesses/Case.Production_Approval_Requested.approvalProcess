<?xml version="1.0" encoding="UTF-8"?>
<ApprovalProcess xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <allowRecall>true</allowRecall>
    <allowedSubmitters>
        <type>owner</type>
    </allowedSubmitters>
    <approvalPageFields>
        <field>CaseNumber</field>
        <field>Owner</field>
        <field>Subject</field>
        <field>Description</field>
        <field>Planned_Implementation_Date__c</field>
    </approvalPageFields>
    <approvalStep>
        <allowDelegate>true</allowDelegate>
        <approvalActions>
            <action>
                <name>Set_Case_Status_to_Approved_for_Prod</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name><EMAIL></name>
                <type>user</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <description>Approval for moving a change request solution into production for level 3 &amp; 4 changes requests is required from IT.

Approvers: <PERSON><PERSON><PERSON><PERSON><PERSON>; Viren <PERSON>idu

(CRM Enhanced Cases Change Requests C Reinecke 2010/08)</description>
        <label>Get Production Approval from IT</label>
        <name>Get_Production_Approval_from_IT</name>
        <rejectionActions>
            <action>
                <name>Set_Case_Status_to_Not_Approved</name>
                <type>FieldUpdate</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <description>When a change Request is ready for deployment to production e.g. Status = &apos;UAT Sign-off&apos; or &apos;Submitted for CAB&apos; the Case must be submitted for IT approval.

(CRM Enhanced Cases Change Requests C Reinecke 2010/08)

Case# C-00005542 - Change Approver</description>
    <emailTemplate>CRM_Change_Request_templates/CRM_Production_Approval_Request</emailTemplate>
    <enableMobileDeviceAccess>false</enableMobileDeviceAccess>
    <entryCriteria>
        <booleanFilter>1 AND ((2 AND 3) OR (4 AND 5))</booleanFilter>
        <criteriaItems>
            <field>Case.RecordType</field>
            <operation>equals</operation>
            <value>Change Request</value>
        </criteriaItems>
        <criteriaItems>
            <field>Case.Status</field>
            <operation>equals</operation>
            <value>UAT Sign-off</value>
        </criteriaItems>
        <criteriaItems>
            <field>Case.Change_Request_Level__c</field>
            <operation>equals</operation>
            <value>3. Minor - IT Development</value>
        </criteriaItems>
        <criteriaItems>
            <field>Case.Status</field>
            <operation>equals</operation>
            <value>Submitted for CAB</value>
        </criteriaItems>
        <criteriaItems>
            <field>Case.Change_Request_Level__c</field>
            <operation>equals</operation>
            <value>4. Major - IT Development</value>
        </criteriaItems>
    </entryCriteria>
    <finalApprovalActions>
        <action>
            <name>Reset_Submitted_for_Prod_Approval_Flag</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Send_Production_Approval_Response_Email_to_CRM_IT_User_Team</name>
            <type>Alert</type>
        </action>
    </finalApprovalActions>
    <finalApprovalRecordLock>false</finalApprovalRecordLock>
    <finalRejectionActions>
        <action>
            <name>Reset_Submitted_for_Prod_Approval_Flag</name>
            <type>FieldUpdate</type>
        </action>
        <action>
            <name>Send_Approval_Response_Email_to_Case_Owner</name>
            <type>Alert</type>
        </action>
    </finalRejectionActions>
    <finalRejectionRecordLock>false</finalRejectionRecordLock>
    <initialSubmissionActions>
        <action>
            <name>CRM_Change_Request_Case_Submitted_for_Production_Approval_Notification</name>
            <type>Alert</type>
        </action>
        <action>
            <name>Set_Submitted_for_Prod_Approval_Flag</name>
            <type>FieldUpdate</type>
        </action>
    </initialSubmissionActions>
    <label>Production Approval Requested</label>
    <processOrder>3</processOrder>
    <recallActions>
        <action>
            <name>Reset_Submitted_for_Prod_Approval_Flag</name>
            <type>FieldUpdate</type>
        </action>
    </recallActions>
    <recordEditability>AdminOnly</recordEditability>
    <showApprovalHistory>false</showApprovalHistory>
</ApprovalProcess>
