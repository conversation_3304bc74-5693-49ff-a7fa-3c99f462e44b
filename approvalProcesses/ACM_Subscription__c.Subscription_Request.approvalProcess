<?xml version="1.0" encoding="UTF-8"?>
<ApprovalProcess xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>false</active>
    <allowRecall>false</allowRecall>
    <allowedSubmitters>
        <type>owner</type>
    </allowedSubmitters>
    <approvalPageFields>
        <field>Name</field>
        <field>Owner</field>
    </approvalPageFields>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Set_Marketplace_Manager_Approval_to_true</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Update_Marketplace_Manager_Date_Time</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name>Marketplace_Managers</name>
                <type>queue</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <label>Request Marketplace Manager Approval</label>
        <name>Request_Marketplace_Manager_Approval</name>
        <rejectionActions>
            <action>
                <name>Perform_Client_On_Boarding</name>
                <type>Task</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Set_Product_Owner_Approval_to_true</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Update_Product_Owner_Date_Time</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <type>adhoc</type>
            </approver>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>ACM_Subscription__c.Approved_by_Marketplace_mnager__c</field>
                <operation>equals</operation>
                <value>True</value>
            </criteriaItems>
        </entryCriteria>
        <ifCriteriaNotMet>ApproveRecord</ifCriteriaNotMet>
        <label>Request Product Owner Approval</label>
        <name>Request_Product_Owner_Approval</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
        <rejectionActions>
            <action>
                <name>Decline_the_Subscription</name>
                <type>FieldUpdate</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Set_Authorized_Person_Approval_to_true</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Update_Authorized_User_Date_Time</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <type>adhoc</type>
            </approver>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>ACM_Subscription__c.Approved_by_Product_Owner__c</field>
                <operation>equals</operation>
                <value>True</value>
            </criteriaItems>
            <criteriaItems>
                <field>ACM_Subscription__c.Approved_by_Marketplace_mnager__c</field>
                <operation>equals</operation>
                <value>True</value>
            </criteriaItems>
        </entryCriteria>
        <label>Request Authorized Person Approval</label>
        <name>Request_Authorized_Person_Approval</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
        <rejectionActions>
            <action>
                <name>Decline_the_Subscription</name>
                <type>FieldUpdate</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <enableMobileDeviceAccess>false</enableMobileDeviceAccess>
    <entryCriteria>
        <criteriaItems>
            <field>ACM_Subscription__c.Status__c</field>
            <operation>equals</operation>
            <value>New</value>
        </criteriaItems>
    </entryCriteria>
    <finalApprovalActions>
        <action>
            <name>Approve_the_subcription</name>
            <type>FieldUpdate</type>
        </action>
    </finalApprovalActions>
    <finalApprovalRecordLock>true</finalApprovalRecordLock>
    <finalRejectionActions>
        <action>
            <name>Decline_the_Subscription</name>
            <type>FieldUpdate</type>
        </action>
    </finalRejectionActions>
    <finalRejectionRecordLock>false</finalRejectionRecordLock>
    <initialSubmissionActions>
        <action>
            <name>Set_status_to_pending_approval</name>
            <type>FieldUpdate</type>
        </action>
    </initialSubmissionActions>
    <label>Subscription Request</label>
    <processOrder>2</processOrder>
    <recordEditability>AdminOnly</recordEditability>
    <showApprovalHistory>true</showApprovalHistory>
</ApprovalProcess>
