<?xml version="1.0" encoding="UTF-8"?>
<ApprovalProcess xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>false</active>
    <allowRecall>false</allowRecall>
    <allowedSubmitters>
        <type>creator</type>
    </allowedSubmitters>
    <allowedSubmitters>
        <type>owner</type>
    </allowedSubmitters>
    <approvalPageFields>
        <field>Name</field>
        <field>Owner</field>
        <field>For_manual_treatment__c</field>
    </approvalPageFields>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Set_Marketplace_Manager_Approval_to_true</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Set_Owner_to_CIBProductOwner_Queue</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Update_Marketplace_Manager_Date_Time</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <type>adhoc</type>
            </approver>
        </assignedApprover>
        <label>Approval from Marketing Manager</label>
        <name>Approval_from_Marketing_Manager</name>
        <rejectionActions>
            <action>
                <name>Perform_Client_On_Boarding</name>
                <type>Task</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <approvalStep>
        <allowDelegate>false</allowDelegate>
        <approvalActions>
            <action>
                <name>Set_Authorized_Person_Approval_to_true</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Set_Product_Owner_Approval_to_true</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Update_Authorized_User_Date_Time</name>
                <type>FieldUpdate</type>
            </action>
            <action>
                <name>Update_Product_Owner_Date_Time</name>
                <type>FieldUpdate</type>
            </action>
        </approvalActions>
        <assignedApprover>
            <approver>
                <name>CIBProductOwner</name>
                <type>queue</type>
            </approver>
            <whenMultipleApprovers>FirstResponse</whenMultipleApprovers>
        </assignedApprover>
        <entryCriteria>
            <criteriaItems>
                <field>ACM_Subscription__c.Approved_by_Marketplace_mnager__c</field>
                <operation>equals</operation>
                <value>True</value>
            </criteriaItems>
        </entryCriteria>
        <label>Approval from Product Owner</label>
        <name>Approval_from_Product_Owner</name>
        <rejectBehavior>
            <type>RejectRequest</type>
        </rejectBehavior>
        <rejectionActions>
            <action>
                <name>Decline_the_Subscription</name>
                <type>FieldUpdate</type>
            </action>
        </rejectionActions>
    </approvalStep>
    <enableMobileDeviceAccess>false</enableMobileDeviceAccess>
    <entryCriteria>
        <criteriaItems>
            <field>ACM_Subscription__c.Status__c</field>
            <operation>equals</operation>
            <value>New</value>
        </criteriaItems>
        <criteriaItems>
            <field>ACM_Subscription__c.Approval__c</field>
            <operation>equals</operation>
            <value>Required</value>
        </criteriaItems>
    </entryCriteria>
    <finalApprovalActions>
        <action>
            <name>Approve_the_subcription</name>
            <type>FieldUpdate</type>
        </action>
    </finalApprovalActions>
    <finalApprovalRecordLock>true</finalApprovalRecordLock>
    <finalRejectionActions>
        <action>
            <name>Decline_the_Subscription</name>
            <type>FieldUpdate</type>
        </action>
    </finalRejectionActions>
    <finalRejectionRecordLock>false</finalRejectionRecordLock>
    <label>ACM Subscription Request v1</label>
    <processOrder>1</processOrder>
    <recordEditability>AdminOnly</recordEditability>
    <showApprovalHistory>true</showApprovalHistory>
</ApprovalProcess>
