<?xml version="1.0" encoding="UTF-8"?>
<Audience xmlns="http://soap.sforce.com/2006/04/metadata">
    <audienceName>API Detail Viewer</audienceName>
    <container>Internal API Marketplace</container>
    <criteria>
        <criterion>
            <criteriaNumber>1</criteriaNumber>
            <criterionValue>
                <entityField>RecordType.Name</entityField>
                <entityType>acm_pkg__CommunityApi__c</entityType>
                <fieldValue>Api</fieldValue>
            </criterionValue>
            <operator>Equal</operator>
            <type>FieldBased</type>
        </criterion>
    </criteria>
    <formulaFilterType>AllCriteriaMatch</formulaFilterType>
    <isDefaultAudience>false</isDefaultAudience>
    <targets>
        <target>
            <groupName>538ebb51-8767-4a99-a0ee-ab6470e5d471</groupName>
            <priority>2</priority>
            <targetType>ExperienceVariation</targetType>
            <targetValue>API_Detail_API_Detail_Page</targetValue>
        </target>
    </targets>
</Audience>
