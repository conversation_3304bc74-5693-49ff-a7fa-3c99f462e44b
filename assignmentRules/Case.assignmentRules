<?xml version="1.0" encoding="UTF-8"?>
<AssignmentRules xmlns="http://soap.sforce.com/2006/04/metadata">
    <assignmentRule>
        <fullName>Case Management Assignment</fullName>
        <active>true</active>
        <ruleEntry>
            <criteriaItems>
                <field>Case.IsEscalated</field>
                <operation>equals</operation>
                <value>True</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.RecordTypeId</field>
                <operation>equals</operation>
                <value>Cross Border CoE Record Type</value>
            </criteriaItems>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>DRP_Bonds</assignedTo>
            <assignedToType>Queue</assignedToType>
            <criteriaItems>
                <field>Case.Asset_Class__c</field>
                <operation>equals</operation>
                <value>Bonds</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.RecordTypeId</field>
                <operation>equals</operation>
                <value>CIB Client Case</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.FMA_Discrepancy__c</field>
                <operation>equals</operation>
                <value>True</value>
            </criteriaItems>
            <template>unfiled$public/DRP_Case_Assignment_Notification_Email_Template</template>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>DRP_Cash_Equities_Institutional</assignedTo>
            <assignedToType>Queue</assignedToType>
            <criteriaItems>
                <field>Case.Asset_Class__c</field>
                <operation>equals</operation>
                <value>Cash Equities (Institutional)</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.RecordTypeId</field>
                <operation>equals</operation>
                <value>CIB Client Case</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.FMA_Discrepancy__c</field>
                <operation>equals</operation>
                <value>True</value>
            </criteriaItems>
            <template>unfiled$public/DRP_Case_Assignment_Notification_Email_Template</template>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>DRP_Cash_Equities_Retail</assignedTo>
            <assignedToType>Queue</assignedToType>
            <criteriaItems>
                <field>Case.Asset_Class__c</field>
                <operation>equals</operation>
                <value>Cash Equities (Retail)</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.RecordTypeId</field>
                <operation>equals</operation>
                <value>CIB Client Case</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.FMA_Discrepancy__c</field>
                <operation>equals</operation>
                <value>True</value>
            </criteriaItems>
            <template>unfiled$public/DRP_Case_Assignment_Notification_Email_Template</template>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>DRP_Commodities_FMA_Derivatives</assignedTo>
            <assignedToType>Queue</assignedToType>
            <criteriaItems>
                <field>Case.Asset_Class__c</field>
                <operation>equals</operation>
                <value>Commodities (FMA Derivatives)</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.RecordTypeId</field>
                <operation>equals</operation>
                <value>CIB Client Case</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.FMA_Discrepancy__c</field>
                <operation>equals</operation>
                <value>True</value>
            </criteriaItems>
            <template>unfiled$public/DRP_Case_Assignment_Notification_Email_Template</template>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>DRP_Commodities_Non_FMA_Physically_Sett</assignedTo>
            <assignedToType>Queue</assignedToType>
            <criteriaItems>
                <field>Case.Asset_Class__c</field>
                <operation>equals</operation>
                <value>Commodities (Non FMA Physically Settled)</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.RecordTypeId</field>
                <operation>equals</operation>
                <value>CIB Client Case</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.FMA_Discrepancy__c</field>
                <operation>equals</operation>
                <value>True</value>
            </criteriaItems>
            <template>unfiled$public/DRP_Case_Assignment_Notification_Email_Template</template>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>DRP_Credit_Derivatives</assignedTo>
            <assignedToType>Queue</assignedToType>
            <criteriaItems>
                <field>Case.Asset_Class__c</field>
                <operation>equals</operation>
                <value>Credit Derivatives</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.RecordTypeId</field>
                <operation>equals</operation>
                <value>CIB Client Case</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.FMA_Discrepancy__c</field>
                <operation>equals</operation>
                <value>True</value>
            </criteriaItems>
            <template>unfiled$public/DRP_Case_Assignment_Notification_Email_Template</template>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>DRP_Equity_Derivatives_Retail</assignedTo>
            <assignedToType>Queue</assignedToType>
            <criteriaItems>
                <field>Case.Asset_Class__c</field>
                <operation>equals</operation>
                <value>Equity Derivatives (Retail)</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.RecordTypeId</field>
                <operation>equals</operation>
                <value>CIB Client Case</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.FMA_Discrepancy__c</field>
                <operation>equals</operation>
                <value>True</value>
            </criteriaItems>
            <template>unfiled$public/DRP_Case_Assignment_Notification_Email_Template</template>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>DRP_Equity_Derivatives_Wholesale</assignedTo>
            <assignedToType>Queue</assignedToType>
            <criteriaItems>
                <field>Case.Asset_Class__c</field>
                <operation>equals</operation>
                <value>Equity Derivatives (Wholesale)</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.RecordTypeId</field>
                <operation>equals</operation>
                <value>CIB Client Case</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.FMA_Discrepancy__c</field>
                <operation>equals</operation>
                <value>True</value>
            </criteriaItems>
            <template>unfiled$public/DRP_Case_Assignment_Notification_Email_Template</template>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>DRP_Foreign_Exchange_FMA</assignedTo>
            <assignedToType>Queue</assignedToType>
            <criteriaItems>
                <field>Case.Asset_Class__c</field>
                <operation>equals</operation>
                <value>Foreign Exchange (FMA)</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.RecordTypeId</field>
                <operation>equals</operation>
                <value>CIB Client Case</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.FMA_Discrepancy__c</field>
                <operation>equals</operation>
                <value>True</value>
            </criteriaItems>
            <template>unfiled$public/DRP_Case_Assignment_Notification_Email_Template</template>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>DRP_Foreign_Exchange_Non_FMA</assignedTo>
            <assignedToType>Queue</assignedToType>
            <criteriaItems>
                <field>Case.Asset_Class__c</field>
                <operation>equals</operation>
                <value>Foreign Exchange (Non FMA)</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.RecordTypeId</field>
                <operation>equals</operation>
                <value>CIB Client Case</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.FMA_Discrepancy__c</field>
                <operation>equals</operation>
                <value>True</value>
            </criteriaItems>
            <template>unfiled$public/DRP_Case_Assignment_Notification_Email_Template</template>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>DRP_Interest_Rate_Derivatives</assignedTo>
            <assignedToType>Queue</assignedToType>
            <criteriaItems>
                <field>Case.Asset_Class__c</field>
                <operation>equals</operation>
                <value>Interest Rate Derivatives</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.RecordTypeId</field>
                <operation>equals</operation>
                <value>CIB Client Case</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.FMA_Discrepancy__c</field>
                <operation>equals</operation>
                <value>True</value>
            </criteriaItems>
            <template>unfiled$public/DRP_Case_Assignment_Notification_Email_Template</template>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>DRP_Money_Markets</assignedTo>
            <assignedToType>Queue</assignedToType>
            <criteriaItems>
                <field>Case.Asset_Class__c</field>
                <operation>equals</operation>
                <value>Money Markets</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.RecordTypeId</field>
                <operation>equals</operation>
                <value>CIB Client Case</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.FMA_Discrepancy__c</field>
                <operation>equals</operation>
                <value>True</value>
            </criteriaItems>
            <template>unfiled$public/DRP_Case_Assignment_Notification_Email_Template</template>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>Angola_CCC_Apoiocliente_Queue</assignedTo>
            <assignedToType>Queue</assignedToType>
            <criteriaItems>
                <field>Case.Case_Owner_Team__c</field>
                <operation>equals</operation>
                <value>Africa Regions-Client Services-PBB-Voice Branch</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.OwnerId</field>
                <operation>equals</operation>
                <value>Salesforce Administration</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Origin</field>
                <operation>equals</operation>
                <value>Web</value>
            </criteriaItems>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>AccountServicesQueue</assignedTo>
            <assignedToType>Queue</assignedToType>
            <booleanFilter>1 AND 2 AND (3 OR 4 OR 5 OR 6 OR 7 OR 8 OR 9 OR 10 OR 11 OR 12 OR 13 OR 14 OR 15 OR 16 OR 17 OR 18 OR 19 OR 20 OR 21 OR 22 OR 23 OR 24)</booleanFilter>
            <criteriaItems>
                <field>Case.RecordTypeId</field>
                <operation>equals</operation>
                <value>Cross Border CoE Record Type</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.OwnerId</field>
                <operation>notEqual</operation>
                <value>IS Service Managers Queue</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Description</field>
                <operation>contains</operation>
                <value>Account closure</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Subject</field>
                <operation>contains</operation>
                <value>Account closure</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Description</field>
                <operation>contains</operation>
                <value>Account opening</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Subject</field>
                <operation>contains</operation>
                <value>Account opening</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Description</field>
                <operation>contains</operation>
                <value>Account amendment</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Subject</field>
                <operation>contains</operation>
                <value>Account amendment</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Description</field>
                <operation>contains</operation>
                <value>Changes to account</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Subject</field>
                <operation>contains</operation>
                <value>Changes to account</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Subject</field>
                <operation>contains</operation>
                <value>Add account</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Description</field>
                <operation>contains</operation>
                <value>Add account</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Description</field>
                <operation>contains</operation>
                <value>SWIFT setup</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Subject</field>
                <operation>contains</operation>
                <value>SWIFT setup</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Description</field>
                <operation>contains</operation>
                <value>Change cash account</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Subject</field>
                <operation>contains</operation>
                <value>Change cash account</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Description</field>
                <operation>contains</operation>
                <value>Modification</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Subject</field>
                <operation>contains</operation>
                <value>Modification</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Description</field>
                <operation>contains</operation>
                <value>Name change</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Subject</field>
                <operation>contains</operation>
                <value>Name change</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Description</field>
                <operation>contains</operation>
                <value>Global Custody</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Subject</field>
                <operation>contains</operation>
                <value>Global Custody</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Description</field>
                <operation>contains</operation>
                <value>GC</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Subject</field>
                <operation>contains</operation>
                <value>GC</value>
            </criteriaItems>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>AccountServicesQueue</assignedTo>
            <assignedToType>Queue</assignedToType>
            <booleanFilter>1 AND 2 AND (3 OR 4 OR 5 OR 6 OR 7 OR 8 OR 9 OR 10 OR 11 OR 12 OR 13 OR 14 OR 15 OR 16 OR 17 OR 18 OR 19 OR 20 OR 21 OR 22 OR 23 OR 24)</booleanFilter>
            <criteriaItems>
                <field>Case.RecordTypeId</field>
                <operation>equals</operation>
                <value>Cross Border CoE Record Type</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.OwnerId</field>
                <operation>notEqual</operation>
                <value>IS Service Managers Queue</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Description</field>
                <operation>contains</operation>
                <value>MT599</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Subject</field>
                <operation>contains</operation>
                <value>MT599</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Description</field>
                <operation>contains</operation>
                <value>SDA</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Subject</field>
                <operation>contains</operation>
                <value>SDA</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Description</field>
                <operation>contains</operation>
                <value>Segregated</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Subject</field>
                <operation>contains</operation>
                <value>Segregated</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Description</field>
                <operation>contains</operation>
                <value>INSTRUCTING PARTY</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Subject</field>
                <operation>contains</operation>
                <value>INSTRUCTING PARTY</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Description</field>
                <operation>contains</operation>
                <value>AMEND</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Subject</field>
                <operation>contains</operation>
                <value>AMEND</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Description</field>
                <operation>contains</operation>
                <value>MOVE</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Subject</field>
                <operation>contains</operation>
                <value>MOVE</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Description</field>
                <operation>contains</operation>
                <value>CHANGE</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Subject</field>
                <operation>contains</operation>
                <value>CHANGE</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Description</field>
                <operation>contains</operation>
                <value>EXEMPTION</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Subject</field>
                <operation>contains</operation>
                <value>EXEMPTION</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Description</field>
                <operation>contains</operation>
                <value>CREATION</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Subject</field>
                <operation>contains</operation>
                <value>CREATION</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Description</field>
                <operation>contains</operation>
                <value>REQUEST FOR SWIFT</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Subject</field>
                <operation>contains</operation>
                <value>REQUEST FOR SWIFT</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Description</field>
                <operation>contains</operation>
                <value>HIPORT</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Subject</field>
                <operation>contains</operation>
                <value>HIPORT</value>
            </criteriaItems>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>AccountServicesQueue</assignedTo>
            <assignedToType>Queue</assignedToType>
            <booleanFilter>1 AND 2 AND (3 OR 4 OR 5 OR 6 OR 7 OR 8 OR 9 OR 10 OR 11 OR 12)</booleanFilter>
            <criteriaItems>
                <field>Case.RecordTypeId</field>
                <operation>equals</operation>
                <value>Cross Border CoE Record Type</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.OwnerId</field>
                <operation>notEqual</operation>
                <value>IS Service Managers Queue</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Description</field>
                <operation>contains</operation>
                <value>TICK SENDER NOTIFICATION</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Subject</field>
                <operation>contains</operation>
                <value>TICK SENDER NOTIFICATION</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Description</field>
                <operation>contains</operation>
                <value>Opened</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Subject</field>
                <operation>contains</operation>
                <value>Opened</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Description</field>
                <operation>contains</operation>
                <value>Closing</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Subject</field>
                <operation>contains</operation>
                <value>Closing</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Description</field>
                <operation>contains</operation>
                <value>Close</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Subject</field>
                <operation>contains</operation>
                <value>Close</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Description</field>
                <operation>contains</operation>
                <value>MOVING OF ACCOUNT</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.Subject</field>
                <operation>contains</operation>
                <value>MOVING OF ACCOUNT</value>
            </criteriaItems>
        </ruleEntry>
        <ruleEntry>
            <assignedTo>IS_Customer_Insights_Queue</assignedTo>
            <assignedToType>Queue</assignedToType>
            <criteriaItems>
                <field>Case.RecordTypeId</field>
                <operation>equals</operation>
                <value>Cross Border CoE Record Type</value>
            </criteriaItems>
            <criteriaItems>
                <field>Case.OwnerId</field>
                <operation>notEqual</operation>
                <value>IS Service Managers Queue</value>
            </criteriaItems>
        </ruleEntry>
        <ruleEntry/>
    </assignmentRule>
</AssignmentRules>
