<?xml version="1.0" encoding="UTF-8"?>
<Bot xmlns="http://soap.sforce.com/2006/04/metadata">
    <botVersions>
        <fullName>v9</fullName>
        <articleAnswersGPTEnabled>false</articleAnswersGPTEnabled>
        <botDialogGroups>
            <developerName>Balance_Viewing</developerName>
            <label>Balance Viewing</label>
        </botDialogGroups>
        <botDialogGroups>
            <developerName>Email_statement</developerName>
            <label>Email statement</label>
        </botDialogGroups>
        <botDialogGroups>
            <developerName>Payment_Tracking</developerName>
            <label>Payment Tracking</label>
        </botDialogGroups>
        <botDialogs>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>OSB_RetrieveCommunityUrl</invocationActionName>
                        <invocationActionType>apex</invocationActionType>
                        <invocationMappings>
                            <parameterName>sCommunityUrl</parameterName>
                            <type>Output</type>
                            <variableName>Community_URL</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>8ae9a21b-a114-4386-9020-25589ea6dcb5</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>619e93a1-1a35-4c3d-a44b-d681a88d4e94</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botMessages>
                    <message>Hi there. I’m Stan, the resident virtual assistant at OneHub.</message>
                    <messageIdentifier>12299d30-e0c3-4071-be79-2f7deaeedc57</messageIdentifier>
                </botMessages>
                <stepIdentifier>bc479461-e7bc-427c-81fb-32838bf8fbca</stepIdentifier>
                <type>Message</type>
            </botSteps>
            <botSteps>
                <botMessages>
                    <message>Just so you know, you can always type “Menu” to return to the main menu.</message>
                    <messageIdentifier>927f3ad1-0fc8-48c5-9391-b359fe913257</messageIdentifier>
                </botMessages>
                <stepIdentifier>f28f5c32-b9cc-4177-b54f-7257c2e69d2f</stepIdentifier>
                <type>Message</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>Check_Chat_Bot_Conversation_Token</invocationActionName>
                        <invocationActionType>flow</invocationActionType>
                        <invocationMappings>
                            <parameterName>vChatTranscriptId</parameterName>
                            <type>Input</type>
                            <variableName>RoutableId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>vIsTokenCorrect</parameterName>
                            <type>Output</type>
                            <variableName>Is_Token_Correct</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>5a5d53b8-3ebb-4a57-94c3-a273fbc189c3</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>24a7f36d-1986-4084-8cdd-61f979591a96</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Is_Token_Correct</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>false</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Main_Menu</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>6bd69828-17c0-4140-b1e8-06c9fbf2e66a</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>f94acf6e-7626-46aa-9b58-856cf016ff91</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>OSB_VA_BotRollout_CTRL</invocationActionName>
                        <invocationActionType>apex</invocationActionType>
                        <invocationMappings>
                            <parameterName>contactIds</parameterName>
                            <type>Input</type>
                            <variableName>ContactId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>output</parameterName>
                            <type>Output</type>
                            <variableName>Is_Enhanced_Bot_Enabled</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>33855337-2f30-41c1-b511-da6cb5c34da8</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>09aefc88-58db-4c62-abc4-5c97a7ab01cb</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Is_Enhanced_Bot_Enabled</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>true</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Main_Menu_Authenticated</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>1af063b6-1cc8-4c77-b6b7-73d64a28255a</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>81b33fa4-f77c-41fd-8cb1-db4357066a15</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botNavigation>
                    <botNavigationLinks>
                        <targetBotDialog>Main_Menu</targetBotDialog>
                    </botNavigationLinks>
                    <type>Redirect</type>
                </botNavigation>
                <stepIdentifier>48041e3f-b137-45b3-a1d7-8ba27e205d66</stepIdentifier>
                <type>Navigation</type>
            </botSteps>
            <developerName>Welcome</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Welcome</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Is_Enhanced_Bot_Enabled</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>true</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Main_Menu_Authenticated</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>6b49fa64-aabd-4a52-90d5-4358b2e13da9</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>d9451f47-8ae7-4351-8cd8-c337cc2e6992</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botMessages>
                    <message>I am still learning but I can help you with the following:</message>
                    <messageIdentifier>b3a8f749-de79-41c0-9e29-6a49aa4d6c09</messageIdentifier>
                </botMessages>
                <stepIdentifier>99d36298-e947-4f44-9a88-e976bd3dbdb4</stepIdentifier>
                <type>Message</type>
            </botSteps>
            <botSteps>
                <botNavigation>
                    <botNavigationLinks>
                        <targetBotDialog>What_is_One_Place_to_Land</targetBotDialog>
                    </botNavigationLinks>
                    <botNavigationLinks>
                        <targetBotDialog>Who_can_sign_up_for_OneHub</targetBotDialog>
                    </botNavigationLinks>
                    <botNavigationLinks>
                        <targetBotDialog>Where_is_OneHub_available</targetBotDialog>
                    </botNavigationLinks>
                    <botNavigationLinks>
                        <targetBotDialog>How_do_I_sign_up</targetBotDialog>
                    </botNavigationLinks>
                    <botNavigationLinks>
                        <targetBotDialog>Can_I_access_other_solutions</targetBotDialog>
                    </botNavigationLinks>
                    <botNavigationLinks>
                        <targetBotDialog>Other</targetBotDialog>
                    </botNavigationLinks>
                    <type>Redirect</type>
                </botNavigation>
                <stepIdentifier>dbcef844-e1a2-47a8-a345-9eb26498b079</stepIdentifier>
                <type>Navigation</type>
            </botSteps>
            <developerName>Main_Menu</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Main Menu</label>
            <mlIntent>Main_Menu</mlIntent>
            <mlIntentTrainingEnabled>true</mlIntentTrainingEnabled>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botSteps>
                <conversationSystemMessage>
                    <type>Transfer</type>
                </conversationSystemMessage>
                <stepIdentifier>f3c31cca-dd86-4f90-88f3-0e89415b05c1</stepIdentifier>
                <type>SystemMessage</type>
            </botSteps>
            <developerName>Transfer_To_Agent</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Transfer To Agent</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botSteps>
                <botMessages>
                    <message>To close this chat, click on the close icon in the top right corner of this chat window. Goodbye!</message>
                    <messageIdentifier>7deeba38-1adb-4394-b709-ac5be3cffee0</messageIdentifier>
                </botMessages>
                <stepIdentifier>49e44de1-caf9-4717-8353-dbf82b9f4843</stepIdentifier>
                <type>Message</type>
            </botSteps>
            <botSteps>
                <botSteps>
                    <conversationSystemMessage>
                        <type>EndChat</type>
                    </conversationSystemMessage>
                    <stepIdentifier>fd6d9c6a-9553-430b-a0cf-c35fc4e48d1a</stepIdentifier>
                    <type>SystemMessage</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>1282def3-0c2a-4745-bde2-621d51ff0209</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <stepIdentifier>781130d4-6c60-4561-9fa1-b2dfd2885369</stepIdentifier>
                <type>Wait</type>
            </botSteps>
            <developerName>End_Chat</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>End Chat</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botSteps>
                <botMessages>
                    <message>Sorry, I didn&apos;t understand that.</message>
                    <messageIdentifier>3ab99a27-8936-424e-9305-b782b15f6b7e</messageIdentifier>
                </botMessages>
                <stepIdentifier>bae4b4e1-0da1-4ece-b5b2-dd57922f3927</stepIdentifier>
                <type>Message</type>
            </botSteps>
            <botSteps>
                <stepIdentifier>0dc28b3b-e268-4324-8e6c-6cbe142271e4</stepIdentifier>
                <type>Wait</type>
            </botSteps>
            <developerName>Confused</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Confused</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botSteps>
                <botMessages>
                    <message>It’s a consolidated platform where you can access a range of Standard Bank’s Corporate and Investment Banking digital solutions suited to your needs.</message>
                    <messageIdentifier>3d0fe51a-933a-4126-b5bd-45968939fc6d</messageIdentifier>
                </botMessages>
                <stepIdentifier>3712a34c-25fa-41ae-a7fa-8b8796f680b4</stepIdentifier>
                <type>Message</type>
            </botSteps>
            <botSteps>
                <botMessages>
                    <message>With single sign-on, the platform allows you to have access to everything you previously signed up for or will sign up for, without having to sign in to each and every solution ever again.</message>
                    <messageIdentifier>51e22e45-26ba-4998-a39c-e3b5a126b6fc</messageIdentifier>
                </botMessages>
                <stepIdentifier>9fb018db-ab4f-497a-8e3c-b3211ad9ca9f</stepIdentifier>
                <type>Message</type>
            </botSteps>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>User_Input</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>018f2ad8-6cfa-4502-bfa7-a8581fc3853f</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>51dcbb49-1392-4041-9240-8fe1a7841e55</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>a453852e-7874-4751-9d00-99a19993c71a</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <askCollectIfSet>false</askCollectIfSet>
                    <botMessages>
                        <message>Can I tell you more about who can sign up to OneHub?</message>
                        <messageIdentifier>3b8f1ed0-86a7-47e9-a3ef-f31f2945c7d6</messageIdentifier>
                    </botMessages>
                    <botQuickReplyOptions>
                        <literalValue>Yes</literalValue>
                        <quickReplyOptionIdentifier>2baba801-fee4-42d6-83ed-66a1a1276a54</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botQuickReplyOptions>
                        <literalValue>No</literalValue>
                        <quickReplyOptionIdentifier>81241d75-1336-471f-be05-d0a9a3a1a444</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botVariableOperands>
                        <disableAutoFill>true</disableAutoFill>
                        <sourceName>_Text</sourceName>
                        <sourceType>StandardMlSlotClass</sourceType>
                        <targetName>User_Input</targetName>
                        <targetType>ConversationVariable</targetType>
                    </botVariableOperands>
                    <ignoreIntentRecognition>false</ignoreIntentRecognition>
                    <optionalCollect>false</optionalCollect>
                    <quickReplyType>Static</quickReplyType>
                    <quickReplyWidgetType>Menu</quickReplyWidgetType>
                    <type>Collect</type>
                    <variableOperationIdentifier>ba8a2ca8-3359-4a8b-a966-32c930507418</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>31e4e7af-7011-4860-bc77-233325d47d71</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>Yes</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Who_can_sign_up_for_OneHub</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>04217990-ba42-4988-8953-a87db3cbe85b</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>3109db70-a709-4383-9f9d-e1b8521240a7</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>No</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Confirmation_of_Solution</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>b64fbcba-d397-448f-931c-68a43ede2db8</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>2d692a92-0983-481c-993a-700bc4716b47</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <stepIdentifier>c14acf4d-18a9-4b3b-9e8d-f95654dadc99</stepIdentifier>
                <type>Wait</type>
            </botSteps>
            <developerName>What_is_One_Place_to_Land</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>What is OneHub?</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botSteps>
                <botMessages>
                    <message>As a Standard Bank Corporate &amp; Investment Banking client, you will be invited by your line manager (Authorised or Designated Person within your organisation) to gain access to OneHub.</message>
                    <messageIdentifier>066c371b-79f6-4a71-aaee-c7ac13767909</messageIdentifier>
                </botMessages>
                <stepIdentifier>7275a4bc-dd88-4e8d-bcf5-19a988045381</stepIdentifier>
                <type>Message</type>
            </botSteps>
            <botSteps>
                <botMessages>
                    <message>If you haven’t received an invitation you can either contact your Relationship Manager or the OneHub team to find out more.</message>
                    <messageIdentifier>7a738e3b-7aab-41ae-9580-dc83883f8bf3</messageIdentifier>
                </botMessages>
                <stepIdentifier>ec43e15d-0e27-4245-b560-3e549be6f867</stepIdentifier>
                <type>Message</type>
            </botSteps>
            <botSteps>
                <botMessages>
                    <message>Copy and paste the link below if you want to contact us:
                        {!Community_URL}s/contact-us</message>
                    <messageIdentifier>11ae07da-b6b0-4668-8066-e7adaeb3dc39</messageIdentifier>
                </botMessages>
                <stepIdentifier>0f5e89b1-fedc-468c-b61f-5408325b7860</stepIdentifier>
                <type>Message</type>
            </botSteps>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>User_Input</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>b3e515aa-4e5b-4e15-aea8-205a4f01c404</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>dfff60ea-6970-4d54-a79c-d588fabf6e81</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>d8a0279a-19cc-4bcb-9b81-20e2802d4308</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <askCollectIfSet>false</askCollectIfSet>
                    <botMessages>
                        <message>Would you like to access other solutions?</message>
                        <messageIdentifier>b9ff11eb-e00d-4e41-ace3-2e40ee91f402</messageIdentifier>
                    </botMessages>
                    <botQuickReplyOptions>
                        <literalValue>Yes</literalValue>
                        <quickReplyOptionIdentifier>cc915021-7c7a-46d9-bb81-c51092ed20ea</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botQuickReplyOptions>
                        <literalValue>No</literalValue>
                        <quickReplyOptionIdentifier>d64d3a42-8185-4a3f-9346-b57c8e077213</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botVariableOperands>
                        <disableAutoFill>true</disableAutoFill>
                        <sourceName>_Text</sourceName>
                        <sourceType>StandardMlSlotClass</sourceType>
                        <targetName>User_Input</targetName>
                        <targetType>ConversationVariable</targetType>
                    </botVariableOperands>
                    <ignoreIntentRecognition>false</ignoreIntentRecognition>
                    <optionalCollect>false</optionalCollect>
                    <quickReplyType>Static</quickReplyType>
                    <quickReplyWidgetType>Menu</quickReplyWidgetType>
                    <type>Collect</type>
                    <variableOperationIdentifier>3cdfea5d-a2f0-420b-bc0a-fcca063023b9</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>0f853652-63f0-4dc7-a3cd-1fbf060d3f9c</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>Yes</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Can_I_access_other_solutions</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>d93308fd-af51-42c8-a014-f61f78b1b8d7</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>30ed96be-9ef4-4672-be6f-ab2dcd5d7bb2</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>No</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Confirmation_of_Solution</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>de629eec-5bdd-47ab-9977-2c9fc94bb328</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>719da59d-8a48-4afd-b831-c27557703ab1</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <stepIdentifier>975663d0-2c57-4533-8dfe-666ffd32f0de</stepIdentifier>
                <type>Wait</type>
            </botSteps>
            <developerName>How_do_I_sign_up</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>How do I sign up?</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botSteps>
                <botMessages>
                    <message>Once your line manager has invited you and you have been given access to OneHub, you can sign up for the solutions that interest you and then mark them as a favorite to set up your dashboard.</message>
                    <messageIdentifier>c59ac906-afca-401d-b25d-51b48433820b</messageIdentifier>
                </botMessages>
                <stepIdentifier>0f3c557a-de2d-4953-9e4f-ed1986cc7d15</stepIdentifier>
                <type>Message</type>
            </botSteps>
            <botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Confirmation_of_Solution</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>3c733e4f-f852-484d-a817-77b6558ee05e</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>952db25c-028d-4009-affe-99f473c5dfd2</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <stepIdentifier>96f61754-e4da-4612-8d61-8fa92c863fd8</stepIdentifier>
                <type>Wait</type>
            </botSteps>
            <developerName>Can_I_access_other_solutions</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Can I access other solutions?</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botSteps>
                <botMessages>
                    <message>Please visit our Contact Us section and provide us with as much relevant information as possible so we can assist you further.</message>
                    <messageIdentifier>4ad614d1-baa3-4d39-bed7-007069399e42</messageIdentifier>
                </botMessages>
                <stepIdentifier>fb887a62-1644-4fb4-b63b-30de3dde4a2a</stepIdentifier>
                <type>Message</type>
            </botSteps>
            <botSteps>
                <botMessages>
                    <message>Contact Us:
                        {!Community_URL}s/contact-us</message>
                    <messageIdentifier>75dbdab8-1479-4b01-9484-1368804271d5</messageIdentifier>
                </botMessages>
                <stepIdentifier>3ff6b375-7eac-479c-8a89-23aa09282c38</stepIdentifier>
                <type>Message</type>
            </botSteps>
            <botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Confirmation_of_Solution</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>dc9bf049-8a79-4b80-9c54-eb93d3294cce</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>92c1444a-5717-4bd8-bec6-b68eddca6169</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <stepIdentifier>bd353374-7ed4-45ad-97c4-bae3bd886ed9</stepIdentifier>
                <type>Wait</type>
            </botSteps>
            <developerName>Other</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Other questions?</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>User_Input</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>2c9ed671-c3ad-4dbb-8823-feddd5514223</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>be531079-7860-407f-86af-e604a61d39ee</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>5973397b-b371-4d39-914f-2f22ea8a171b</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <askCollectIfSet>false</askCollectIfSet>
                    <botMessages>
                        <message>Is there anything else I can do for you?</message>
                        <messageIdentifier>aa941e09-1510-4c86-b375-1a749fd0e476</messageIdentifier>
                    </botMessages>
                    <botQuickReplyOptions>
                        <literalValue>Yes</literalValue>
                        <quickReplyOptionIdentifier>5f268ec0-57aa-4cd7-9c26-01fda674bc94</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botQuickReplyOptions>
                        <literalValue>No</literalValue>
                        <quickReplyOptionIdentifier>cf025571-ed7b-4074-a18a-7c395d2fd5af</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botVariableOperands>
                        <disableAutoFill>true</disableAutoFill>
                        <sourceName>_Text</sourceName>
                        <sourceType>StandardMlSlotClass</sourceType>
                        <targetName>User_Input</targetName>
                        <targetType>ConversationVariable</targetType>
                    </botVariableOperands>
                    <ignoreIntentRecognition>false</ignoreIntentRecognition>
                    <optionalCollect>false</optionalCollect>
                    <quickReplyType>Static</quickReplyType>
                    <quickReplyWidgetType>Menu</quickReplyWidgetType>
                    <type>Collect</type>
                    <variableOperationIdentifier>1d34fa3a-436d-4d49-9ea7-d51d659fb268</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>b7486ac9-ac57-496d-8e44-b8a6f9d6674e</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>Yes</rightOperandValue>
                </botStepConditions>
                <botStepConditions>
                    <leftOperandName>Is_Enhanced_Bot_Enabled</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>true</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Main_Menu_Authenticated</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>a210f523-14cd-445f-81a8-7aef73e73b94</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>75c2be25-7203-4879-ae64-7b6311b09e98</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>Yes</rightOperandValue>
                </botStepConditions>
                <botStepConditions>
                    <leftOperandName>Is_Enhanced_Bot_Enabled</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>false</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Main_Menu</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>a1d41d47-865c-4706-827f-bd19bf9593bf</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>1e28fb88-99b0-4265-9e5a-e0e718701819</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>No</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>End_Chat</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>483e7ff6-9f6b-43c0-8818-0a7c66c59894</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>99e733ac-f87a-4c25-8272-cb77465a4328</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <stepIdentifier>c2638f3e-0be0-4a4c-a6d5-1fabf5be9ac3</stepIdentifier>
                <type>Wait</type>
            </botSteps>
            <developerName>Confirmation_of_Solution</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Confirmation of Solution</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botSteps>
                <botMessages>
                    <message>OneHub is currently only available to Standard Bank’s existing and new Corporate and Investment Banking clients in South Africa.</message>
                    <messageIdentifier>d402ba53-2fd2-4194-a9cd-5077a7c9eee0</messageIdentifier>
                </botMessages>
                <stepIdentifier>0d529fc0-2aec-4640-b28a-e937d6c76e8c</stepIdentifier>
                <type>Message</type>
            </botSteps>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>User_Input</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>39ed3e8d-4994-45e5-9b2e-97236d7174ee</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>db785539-9739-4ef0-90f6-a83560606cd6</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>3ad14957-a4fd-4065-b4eb-ddb63c2225d5</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <askCollectIfSet>false</askCollectIfSet>
                    <botMessages>
                        <message>Can I tell you more about OneHub availability?</message>
                        <messageIdentifier>d3628d62-2209-42b3-95b0-45754d4b5a54</messageIdentifier>
                    </botMessages>
                    <botQuickReplyOptions>
                        <literalValue>Yes</literalValue>
                        <quickReplyOptionIdentifier>6e093d98-66a7-49ff-85af-c7462100f28e</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botQuickReplyOptions>
                        <literalValue>No</literalValue>
                        <quickReplyOptionIdentifier>d6d0aed6-160d-4333-bddf-581dd427aeb2</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botVariableOperands>
                        <disableAutoFill>true</disableAutoFill>
                        <sourceName>_Text</sourceName>
                        <sourceType>StandardMlSlotClass</sourceType>
                        <targetName>User_Input</targetName>
                        <targetType>ConversationVariable</targetType>
                    </botVariableOperands>
                    <ignoreIntentRecognition>false</ignoreIntentRecognition>
                    <optionalCollect>false</optionalCollect>
                    <quickReplyType>Static</quickReplyType>
                    <quickReplyWidgetType>Menu</quickReplyWidgetType>
                    <type>Collect</type>
                    <variableOperationIdentifier>90929269-c0b9-4508-a0d2-61ebb4adc5ce</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>bf14c6f8-373e-4b9b-8cb4-8987f3672764</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>Yes</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Where_is_OneHub_available</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>9408b850-21c9-4b58-9b10-9b12bcf533f6</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>d7bf06a3-ec3f-4a40-95f6-7c9d1b241e5f</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>No</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Confirmation_of_Solution</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>f2946fc6-0b8b-4673-93fe-a8abb9c6761b</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>6f44f72b-c324-4aa2-82ea-4a13e8bdf5a7</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <stepIdentifier>50ae0876-1ef0-41b6-bbd4-bdc802ad2e7d</stepIdentifier>
                <type>Wait</type>
            </botSteps>
            <developerName>Who_can_sign_up_for_OneHub</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Who can sign up for OneHub?</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botSteps>
                <botMessages>
                    <message>Currently OneHub is only available in South Africa. We are working on a rollout plan to make it available in other countries that we operate in across Africa.  We will communicate with our clients as we make OneHub available in these countries.</message>
                    <messageIdentifier>c7d5fdb2-ad05-47e3-943e-c1faaf61108c</messageIdentifier>
                </botMessages>
                <stepIdentifier>e2d87d76-e229-45d2-bd14-7b3dbf3f9813</stepIdentifier>
                <type>Message</type>
            </botSteps>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>User_Input</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>ab8f6bb9-ba89-4eba-81f5-0471fb96ee8a</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>fadb9a51-99b6-43a5-a9e9-4e6e473efc0d</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>96aeda99-4327-46d1-8717-f95aa473d85a</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <askCollectIfSet>false</askCollectIfSet>
                    <botMessages>
                        <message>Can I tell you more about signing up?</message>
                        <messageIdentifier>e25fa2f1-e483-4878-9bb0-bff07b7dc8f4</messageIdentifier>
                    </botMessages>
                    <botQuickReplyOptions>
                        <literalValue>Yes</literalValue>
                        <quickReplyOptionIdentifier>c139952d-8c28-4c41-bcc0-4dc9393aea3a</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botQuickReplyOptions>
                        <literalValue>No</literalValue>
                        <quickReplyOptionIdentifier>73c11972-54e0-4c68-9400-52fda0b5ecc1</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botVariableOperands>
                        <disableAutoFill>true</disableAutoFill>
                        <sourceName>_Text</sourceName>
                        <sourceType>StandardMlSlotClass</sourceType>
                        <targetName>User_Input</targetName>
                        <targetType>ConversationVariable</targetType>
                    </botVariableOperands>
                    <ignoreIntentRecognition>false</ignoreIntentRecognition>
                    <optionalCollect>false</optionalCollect>
                    <quickReplyType>Static</quickReplyType>
                    <quickReplyWidgetType>Menu</quickReplyWidgetType>
                    <type>Collect</type>
                    <variableOperationIdentifier>0b875e11-bd1a-4b59-a438-51eaf0aa1ff6</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>4aa7bd50-bc6c-494a-84ef-578ca361aeae</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>Yes</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>How_do_I_sign_up</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>2bed9741-23ae-49bf-875e-670a92d2eb45</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>3ae46f20-ae2a-4842-9c49-4d74ea3231d5</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>No</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Confirmation_of_Solution</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>fdf9f966-2ae3-42d4-b676-0a3cc1c71b97</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>5b04c5fa-6ec7-4ed2-8311-210491477564</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <stepIdentifier>d8c30d91-5ab5-4a92-9a7a-ad980c14d6c6</stepIdentifier>
                <type>Wait</type>
            </botSteps>
            <developerName>Where_is_OneHub_available</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Where is OneHub available?</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botDialogGroup>Payment_Tracking</botDialogGroup>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>User_Input</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>85fe1043-36f9-4af3-a003-48291a12ce8f</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>fc3bd7e1-c403-4e12-8286-7e75cf2f0c1b</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Has_Message_Error</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>b46c54eb-8b86-4c41-b40c-1aa5bfa22d3b</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>b4d42555-3892-4134-96e4-320f123d7b61</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Error_Message</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>56a06275-5dc6-4d08-a148-d5ea4efdfada</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>6e1bd52b-bf7d-4a10-be13-12a54bf2d645</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Error_Type</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>29f21dbf-a602-4eff-a726-1eab51c1939f</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>f387c921-0e8d-49a1-b02f-f88f1c5d4f48</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Case_Id</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>0e425993-b4ea-4dd1-897b-84015abfcd35</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>9a685a2c-2804-4dc0-9327-ff0fd7bc89dd</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>2dffdf2f-4b45-43c7-af8a-32db351ec7a4</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Error_Log_Id</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>ffa4d80c-488b-4006-a3a1-c3e952ec2013</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>fa0d7e09-8e00-497b-8ac7-b1bdd75cb723</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Account_Number</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>3d00a9e2-dd5b-4647-98fb-296443e4ed13</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>8500cbe7-89e1-4483-95d1-bc0627b28c56</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Flow_Action_Type</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>b2fb3eb6-da00-4d05-91fa-05803478c152</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>ca5bd81b-8daf-492e-94b0-1a220a2a754d</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>UETR</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>728e39a1-5974-49b5-b742-bc88ab2460eb</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>8e939be4-923c-4600-97bd-3e09b14c3780</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>15fb4e7e-ba0e-4fc0-97cf-cf2d97a31590</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>Create_Case_From_Chat_Bot</invocationActionName>
                        <invocationActionType>flow</invocationActionType>
                        <invocationMappings>
                            <parameterName>ActionType</parameterName>
                            <type>Input</type>
                            <value>Create New Case</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>CaseId</parameterName>
                            <type>Input</type>
                            <variableName>Case_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ChatTranscriptId</parameterName>
                            <type>Input</type>
                            <variableName>RoutableId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ContactId</parameterName>
                            <type>Input</type>
                            <variableName>ContactId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>CountryName</parameterName>
                            <type>Input</type>
                            <variableName>Country_name</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ErrorLogId</parameterName>
                            <type>Input</type>
                            <variableName>Error_Log_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ServiceType</parameterName>
                            <type>Input</type>
                            <value>Payment Tracking</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>CaseId</parameterName>
                            <type>Output</type>
                            <variableName>Case_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>09c1e018-183e-4118-8386-b3b0cdb89b4f</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>87188d07-ba6d-4dc9-96a9-3a337f7f10b7</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <askCollectIfSet>false</askCollectIfSet>
                    <botMessages>
                        <message>Can I have the transaction reference number?</message>
                        <messageIdentifier>054fce42-d8f0-4860-bef6-0937bfdeb2b4</messageIdentifier>
                    </botMessages>
                    <botVariableOperands>
                        <disableAutoFill>true</disableAutoFill>
                        <sourceName>_Text</sourceName>
                        <sourceType>StandardMlSlotClass</sourceType>
                        <targetName>User_Input</targetName>
                        <targetType>ConversationVariable</targetType>
                    </botVariableOperands>
                    <optionalCollect>false</optionalCollect>
                    <quickReplyWidgetType>Buttons</quickReplyWidgetType>
                    <type>Collect</type>
                    <variableOperationIdentifier>d02d2330-8c92-4d8b-b67b-7caa5a979325</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>adf05fe9-8e71-465b-985c-6cf97149a344</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>OSB_VA_TransactionalSearch_CTRL</invocationActionName>
                        <invocationActionType>apex</invocationActionType>
                        <invocationMappings>
                            <parameterName>transactionNumber</parameterName>
                            <type>Input</type>
                            <variableName>User_Input</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>contactId</parameterName>
                            <type>Input</type>
                            <variableName>ContactId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>accountNumber</parameterName>
                            <type>Output</type>
                            <variableName>Account_Number</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorLogId</parameterName>
                            <type>Output</type>
                            <variableName>Error_Log_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>transactionReference</parameterName>
                            <type>Output</type>
                            <variableName>Transaction_Reference</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>actionType</parameterName>
                            <type>Output</type>
                            <variableName>Flow_Action_Type</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>hasError</parameterName>
                            <type>Output</type>
                            <variableName>Has_Message_Error</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorMsg</parameterName>
                            <type>Output</type>
                            <variableName>Error_Message</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>uetr</parameterName>
                            <type>Output</type>
                            <variableName>UETR</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorType</parameterName>
                            <type>Output</type>
                            <variableName>Error_Type</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>028633d0-28db-4c6c-93e0-b797aa9cbf7c</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>142e0a5d-2abd-49fb-8a83-f636a22e8613</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Error_Message</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>IsSet</operatorType>
                </botStepConditions>
                <botStepConditions>
                    <leftOperandName>Has_Message_Error</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>false</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botMessages>
                        <message>{!Error_Message}</message>
                        <messageIdentifier>d730d704-359f-44ae-9b63-ae46f69decdd</messageIdentifier>
                    </botMessages>
                    <stepIdentifier>36d7b19d-0cb0-4f23-8360-c6a4e290719c</stepIdentifier>
                    <type>Message</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Error_Message</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>4f28a477-0c5c-4e01-85c7-355823b5fa5c</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>99ca7917-7298-4ff6-8035-3f795a32a175</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Error_Log_Id</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>c241e678-6769-4400-96b8-bb349488b79b</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>d90787fe-0372-44f6-9b8c-691c26292c7b</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Case_Id</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>24960949-efe7-4976-ba57-385e6d7d1c3f</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>60af82e5-711b-4796-9dad-65c66f95ffc1</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Track_another_single_payment</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>2f4692b4-fdca-41d4-a09a-d25a7dff0600</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>23f4fa11-c333-440f-ae03-bdce1b7284b3</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>Create_Case_From_Chat_Bot</invocationActionName>
                        <invocationActionType>flow</invocationActionType>
                        <invocationMappings>
                            <parameterName>ActionType</parameterName>
                            <type>Input</type>
                            <variableName>Flow_Action_Type</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>CaseId</parameterName>
                            <type>Input</type>
                            <variableName>Case_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ChatTranscriptId</parameterName>
                            <type>Input</type>
                            <variableName>RoutableId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ContactId</parameterName>
                            <type>Input</type>
                            <variableName>ContactId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>CountryName</parameterName>
                            <type>Input</type>
                            <variableName>Country_name</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ErrorLogId</parameterName>
                            <type>Input</type>
                            <variableName>Error_Log_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ServiceType</parameterName>
                            <type>Input</type>
                            <value>Payment Tracking</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>CaseId</parameterName>
                            <type>Output</type>
                            <variableName>Case_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>e076e55a-58aa-42bb-ac76-94530dafa50a</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>fdcaab81-557d-428f-9a70-f66e0e61b327</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Has_Message_Error</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>true</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Error_Log_Id</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>85a0a990-b240-4dac-8aa7-e246494ce074</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>c30dd16b-f04a-4308-a193-d2c598f7cb92</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Flow_Action_Type</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>792cee62-7cd7-42f1-804b-2520fd270e6d</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>ee36ea23-3440-48e9-a7ff-a3a5b6262032</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Case_Id</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>0e4b2fbd-2946-451b-afc3-4e617456eecc</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>076112bf-a857-4aed-b859-24c222701b92</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>88cb05a3-1955-4597-82a6-f74dd8e2961f</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Has_Message_Error</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>true</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botMessages>
                        <message>{!Error_Message}</message>
                        <messageIdentifier>c7e928e8-996e-4183-a47a-15b44fae364c</messageIdentifier>
                    </botMessages>
                    <stepIdentifier>7ec1f5af-daf1-408b-9317-bc74761bcc6a</stepIdentifier>
                    <type>Message</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>4e4cb438-c2d8-4775-a24a-83fc9a91dee5</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Has_Message_Error</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>true</rightOperandValue>
                </botStepConditions>
                <botStepConditions>
                    <leftOperandName>Error_Type</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>NO_ACCOUNT_PERMISSION_ERROR</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Track_another_single_payment</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>61ffb3d1-2e57-4448-a40f-dcbf002cde92</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>8dbe90f6-4af5-40f3-a663-843755855d5c</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Has_Message_Error</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>true</rightOperandValue>
                </botStepConditions>
                <botStepConditions>
                    <leftOperandName>Error_Type</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>TRANSACTION_REFERENCE_INVALID</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Track_another_single_payment</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>854374a5-d85e-48c1-bf03-bf7c5df88fd2</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>e327b137-2874-4c13-a055-2c32dea5a1bb</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Has_Message_Error</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>true</rightOperandValue>
                </botStepConditions>
                <botStepConditions>
                    <leftOperandName>Error_Type</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>HTTP_REQUEST_SEND_ERROR</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Continue_Tracking</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>fd3ec7cc-ca89-4c34-8ca0-4f5a21104884</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>dee71d21-32c0-4d9a-8838-7d736b48f512</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Has_Message_Error</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>true</rightOperandValue>
                </botStepConditions>
                <botStepConditions>
                    <leftOperandName>Error_Type</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>HTTP_RESPONSE_ERROR</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Continue_Tracking</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>8e636f21-f1d6-4226-8e10-be799ad3b67f</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>042cfd76-2ae0-44e4-b5c7-d9cd905e7b0c</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Track_Selected_Payment</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>a55f559c-9a85-4889-98ca-81382f39a9e4</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>7bab0800-3d71-49ba-83a7-39b2430993cd</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botNavigation>
                    <botNavigationLinks>
                        <targetBotDialog>Track_Specific_Payment</targetBotDialog>
                    </botNavigationLinks>
                    <type>Redirect</type>
                </botNavigation>
                <stepIdentifier>635ec192-bf12-45d4-91db-31c515b2a23e</stepIdentifier>
                <type>Navigation</type>
            </botSteps>
            <developerName>Track_Payment</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Track  single payment</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botDialogGroup>Payment_Tracking</botDialogGroup>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>User_Input</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>c4f362fe-3dd1-449c-9716-47e70f957427</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>155bdbd1-76e5-45a1-a776-6fff1379e573</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>b7c5503f-21f9-42fb-929b-d98b0d54b346</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <askCollectIfSet>false</askCollectIfSet>
                    <botMessages>
                        <message>Would you like to track another payment?</message>
                        <messageIdentifier>5f9d72b8-abac-49aa-b675-b57d7179c94b</messageIdentifier>
                    </botMessages>
                    <botQuickReplyOptions>
                        <literalValue>Yes</literalValue>
                        <quickReplyOptionIdentifier>39bb702b-9503-41dc-a817-688f84a56090</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botQuickReplyOptions>
                        <literalValue>No</literalValue>
                        <quickReplyOptionIdentifier>0a2490ea-f299-4143-abe4-db38c4beb46f</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botVariableOperands>
                        <disableAutoFill>true</disableAutoFill>
                        <sourceName>_Text</sourceName>
                        <sourceType>StandardMlSlotClass</sourceType>
                        <targetName>User_Input</targetName>
                        <targetType>ConversationVariable</targetType>
                    </botVariableOperands>
                    <ignoreIntentRecognition>false</ignoreIntentRecognition>
                    <optionalCollect>false</optionalCollect>
                    <quickReplyType>Static</quickReplyType>
                    <quickReplyWidgetType>Menu</quickReplyWidgetType>
                    <type>Collect</type>
                    <variableOperationIdentifier>d49c6c05-caf4-4ca2-9205-9bc426d4e17b</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>3c63917e-6be3-4c55-83ea-fc98061a48c5</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>Yes</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Do_you_have_transaction_reference</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>3c817d5f-ba8f-43e2-9e12-50aa8e8ca292</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>dbb22b9d-d8ca-4e5f-96c6-8c673b9d866d</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>No</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Is_there_anything_else_I_can_help_you_with</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>0b121797-f23a-4804-87cf-7d0b7fad8e7f</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>011f392d-88fb-4128-a3c6-ee7785e847e9</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>Menu</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Main_Menu</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>bf60274d-48a4-408b-a7f4-91121c8bf555</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>f5d54d21-7fa3-4018-995c-819681d296c9</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botMessages>
                    <message>I did not understand that.</message>
                    <messageIdentifier>9cb28f1f-790c-4414-b77a-20168cb862e3</messageIdentifier>
                </botMessages>
                <stepIdentifier>59c2ed37-beeb-4e7a-af79-7c4eae515d8c</stepIdentifier>
                <type>Message</type>
            </botSteps>
            <botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Track_Specific_Payment</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>d1aedf3c-6a29-435d-a583-6b1b8d0196ef</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>3955f4c7-8b3d-4802-8129-1382ec7bb6af</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botNavigation>
                    <botNavigationLinks>
                        <targetBotDialog>End_Chat</targetBotDialog>
                    </botNavigationLinks>
                    <type>Redirect</type>
                </botNavigation>
                <stepIdentifier>34a6cf59-217b-44f2-a182-3e713f645c86</stepIdentifier>
                <type>Navigation</type>
            </botSteps>
            <developerName>Track_Specific_Payment</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Track Another Payment</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botDialogGroup>Payment_Tracking</botDialogGroup>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Has_Message_Error</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>c7db5552-50c6-4702-b049-90995f8237e1</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>874dd821-a3cd-41e7-b298-465d6d33ff5e</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Error_Message</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>4eb6395c-1ea9-42da-ade5-dc94b1ce69a0</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>1a378b03-4b2f-40c1-8bdf-39db9bc0e3ec</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Error_Type</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>8fc6d14d-c4aa-40c1-89ce-a916c437b321</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>88abde35-9eae-4a8c-9546-9730d62b81b4</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Selected_Transaction</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>e611efac-f08e-4c0b-a3d2-a68ad3bdd803</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>a11654f8-7667-4072-a569-6f5cc41a2c9a</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>UETR</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>c63795ed-3be9-4989-b582-edabbc89508c</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>a04b9103-b68a-4085-bf97-8718ef9a6b0f</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>bd62d212-ad27-4fc5-97ae-04bfc7231fc3</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Transaction_Date</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>23775c5f-eced-42f4-8570-a60fcc9188b0</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>7c3b787d-530c-488d-b6b5-1d8cdb662a5f</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Min_Amount</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>ac718af2-c80c-4a7a-b11b-53fa3e4687bb</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>638cd959-6c21-480f-a6e3-0bda85c69b1d</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Max_Amount</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>394bfd54-f469-4d8c-818e-b2d11a48eada</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>a877c2ba-7d2f-45d2-81d9-7f2d84cb6809</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Account_Number</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>612d7d6a-3bc8-4148-b994-3303bcf69441</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>71932cc5-5fc2-4cf5-9d93-3ff1e7a16b3e</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Payment_Currency</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>9ca3a1cc-bbf6-4b65-bf96-2decae2e9943</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>0bb3cbb1-f2be-4aeb-bc39-2dc117651597</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>8138379b-fc93-44f7-b06f-192204c429e0</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Has_Financial_Account</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>82fc7f52-c3c6-4f2b-99cd-c5566ff0ae0e</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>5380ba72-0ff5-434a-b33a-eb252b026d30</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Error_Log_Id</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>4c8cbe95-da0d-442f-bdf9-452bdae2c3e9</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>cfd6adb1-e9ca-4cd8-9283-5ddf880654e2</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Transaction_Reference</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>25572782-d981-4b1c-807c-9f316424cc26</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>7420cbd1-f734-471e-8258-9c35f95d62ef</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>4aa8587b-7672-41c1-b6a7-1a49ea940006</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>Create_Case_From_Chat_Bot</invocationActionName>
                        <invocationActionType>flow</invocationActionType>
                        <invocationMappings>
                            <parameterName>ChatTranscriptId</parameterName>
                            <type>Input</type>
                            <variableName>RoutableId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ErrorLogId</parameterName>
                            <type>Input</type>
                            <variableName>Error_Log_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ServiceType</parameterName>
                            <type>Input</type>
                            <value>Payment Tracking</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>CountryName</parameterName>
                            <type>Input</type>
                            <variableName>Country_name</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>CaseId</parameterName>
                            <type>Input</type>
                            <variableName>Case_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ContactId</parameterName>
                            <type>Input</type>
                            <variableName>ContactId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ActionType</parameterName>
                            <type>Input</type>
                            <value>Create New Case</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>CaseId</parameterName>
                            <type>Output</type>
                            <variableName>Case_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>f94ec758-c902-4903-b677-bcacbb4251b9</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>0e5162b8-7c8b-44ce-9efd-c7802098bc69</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <askCollectIfSet>false</askCollectIfSet>
                    <botMessages>
                        <message>Can I have the transaction date? (yyyy-mm-dd)</message>
                        <messageIdentifier>edc1d174-91eb-41c7-8783-8c97b5687d01</messageIdentifier>
                    </botMessages>
                    <botVariableOperands>
                        <disableAutoFill>true</disableAutoFill>
                        <sourceName>_Date</sourceName>
                        <sourceType>StandardMlSlotClass</sourceType>
                        <targetName>Transaction_Date</targetName>
                        <targetType>ConversationVariable</targetType>
                    </botVariableOperands>
                    <optionalCollect>false</optionalCollect>
                    <quickReplyWidgetType>Buttons</quickReplyWidgetType>
                    <retryMessages>
                        <message>I did not understand that. Can you type the date again?</message>
                        <messageIdentifier>f7cb8122-3e53-4152-8299-8e38f28dab34</messageIdentifier>
                    </retryMessages>
                    <type>Collect</type>
                    <variableOperationIdentifier>9a0d69bc-ec9a-4364-ae53-b8c8d4954492</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>458945d6-6e80-4465-b75b-ed00692f71fc</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <askCollectIfSet>false</askCollectIfSet>
                    <botMessages>
                        <message>Can I have the account number?</message>
                        <messageIdentifier>189a967e-9fc3-46f3-9364-58444c1a9beb</messageIdentifier>
                    </botMessages>
                    <botVariableOperands>
                        <disableAutoFill>true</disableAutoFill>
                        <sourceName>_Text</sourceName>
                        <sourceType>StandardMlSlotClass</sourceType>
                        <targetName>Account_Number</targetName>
                        <targetType>ConversationVariable</targetType>
                    </botVariableOperands>
                    <optionalCollect>false</optionalCollect>
                    <quickReplyWidgetType>Buttons</quickReplyWidgetType>
                    <type>Collect</type>
                    <variableOperationIdentifier>4f1ab58f-bcd1-4a7f-8c75-8cae609c5e6e</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>0150635c-753a-44d4-8650-8365109fd31d</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>OSB_SRV_FinancialAccSearchHandler</invocationActionName>
                        <invocationActionType>apex</invocationActionType>
                        <invocationMappings>
                            <parameterName>input</parameterName>
                            <type>Input</type>
                            <variableName>Account_Number</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>output</parameterName>
                            <type>Output</type>
                            <variableName>Has_Financial_Account</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>468c101f-a6d3-416f-8b32-5f16d4d518be</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>77bbe59c-0785-4e6d-8cb4-59bbb35e0ab2</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Has_Financial_Account</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>false</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>No_service_access</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>ae1024dc-f8f6-4c4d-9134-043240c7e559</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>9f3151b0-2f41-42b5-87ac-4790a48c9b55</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>OSB_SRV_FinancialAccPermissionHandler</invocationActionName>
                        <invocationActionType>apex</invocationActionType>
                        <invocationMappings>
                            <parameterName>accountNumber</parameterName>
                            <type>Input</type>
                            <variableName>Account_Number</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>contactId</parameterName>
                            <type>Input</type>
                            <variableName>ContactId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>permissionType</parameterName>
                            <type>Input</type>
                            <value>Payment tracking</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>hasError</parameterName>
                            <type>Output</type>
                            <variableName>Has_Payment_Tracking_Permission_on_Account</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorMessage</parameterName>
                            <type>Output</type>
                            <variableName>Error_Message</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>d8171d93-3a98-49cd-be2e-d4278c9458d9</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>16c464e2-2499-4250-b6d2-c3a28487fe63</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Has_Payment_Tracking_Permission_on_Account</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>false</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>No_service_access</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>a0f87291-3de6-4340-a81e-737d83b1640d</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>6c1a3a4b-965d-4615-9e12-b1ff77975949</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <askCollectIfSet>false</askCollectIfSet>
                    <botMessages>
                        <message>Can I have the minimum value for the range of transaction?</message>
                        <messageIdentifier>24d472b6-5b96-43d0-9da0-4698631e1c37</messageIdentifier>
                    </botMessages>
                    <botVariableOperands>
                        <disableAutoFill>true</disableAutoFill>
                        <sourceName>_Text</sourceName>
                        <sourceType>StandardMlSlotClass</sourceType>
                        <targetName>Min_Amount</targetName>
                        <targetType>ConversationVariable</targetType>
                    </botVariableOperands>
                    <optionalCollect>false</optionalCollect>
                    <quickReplyWidgetType>Buttons</quickReplyWidgetType>
                    <type>Collect</type>
                    <variableOperationIdentifier>4a9c148f-5d77-4aaa-8ba8-d76d4100bdf8</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>647207f1-dcdd-45dc-af59-499f3ee7d536</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <askCollectIfSet>false</askCollectIfSet>
                    <botMessages>
                        <message>Can I have the maximum value for the range of transaction?</message>
                        <messageIdentifier>92b1a3d5-b264-44f5-aae6-d46c515b1b94</messageIdentifier>
                    </botMessages>
                    <botVariableOperands>
                        <disableAutoFill>true</disableAutoFill>
                        <sourceName>_Text</sourceName>
                        <sourceType>StandardMlSlotClass</sourceType>
                        <targetName>Max_Amount</targetName>
                        <targetType>ConversationVariable</targetType>
                    </botVariableOperands>
                    <optionalCollect>false</optionalCollect>
                    <quickReplyWidgetType>Buttons</quickReplyWidgetType>
                    <type>Collect</type>
                    <variableOperationIdentifier>ef73bd12-7e03-4c82-85c9-f62c5f271278</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>56ff0a23-6393-4a4b-9b8f-c70c08313640</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>OSB_VA_TransactionalSearches_CTRL</invocationActionName>
                        <invocationActionType>apex</invocationActionType>
                        <invocationMappings>
                            <parameterName>maxAmount</parameterName>
                            <type>Input</type>
                            <variableName>Max_Amount</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>accountNumber</parameterName>
                            <type>Input</type>
                            <variableName>Account_Number</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>minAmount</parameterName>
                            <type>Input</type>
                            <variableName>Min_Amount</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>contactId</parameterName>
                            <type>Input</type>
                            <variableName>ContactId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>transactionDate</parameterName>
                            <type>Input</type>
                            <variableName>Transaction_Date</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>transactionNumber</parameterName>
                            <type>Input</type>
                            <variableName>Transaction_Reference</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorLogId</parameterName>
                            <type>Output</type>
                            <variableName>Error_Log_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorType</parameterName>
                            <type>Output</type>
                            <variableName>Error_Type</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>hasError</parameterName>
                            <type>Output</type>
                            <variableName>Has_Message_Error</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>transactions</parameterName>
                            <type>Output</type>
                            <variableName>Transaction_Search_Result</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorOutPutMessage</parameterName>
                            <type>Output</type>
                            <variableName>Error_Message</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>actionType</parameterName>
                            <type>Output</type>
                            <variableName>Flow_Action_Type</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>b626bbb1-5eae-499d-a77b-d5e7376d50e9</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>d3f825ea-403a-4ecf-9987-154032df45d1</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>Create_Case_From_Chat_Bot</invocationActionName>
                        <invocationActionType>flow</invocationActionType>
                        <invocationMappings>
                            <parameterName>ErrorLogId</parameterName>
                            <type>Input</type>
                            <variableName>Error_Log_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ServiceType</parameterName>
                            <type>Input</type>
                            <value>Payment Tracking</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>CountryName</parameterName>
                            <type>Input</type>
                            <variableName>Country_name</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>CaseId</parameterName>
                            <type>Input</type>
                            <variableName>Case_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ContactId</parameterName>
                            <type>Input</type>
                            <variableName>ContactId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ActionType</parameterName>
                            <type>Input</type>
                            <variableName>Flow_Action_Type</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>CaseId</parameterName>
                            <type>Output</type>
                            <variableName>Case_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>bfb523cb-5d5c-49b3-8e1d-31eb249c762f</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>790d7532-f9f8-4a3d-8051-659bb73c8c81</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Has_Message_Error</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>true</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Case_Id</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>21de87f5-a022-4bc7-a3c1-b3d7fbb72947</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>31180b6a-85f2-4d7d-abd3-003aeb15b939</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Error_Log_Id</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>c9278661-0492-43e2-b16c-e79f614d5477</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>00e6d08a-a529-4acc-b45e-aa916c9ed04e</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Flow_Action_Type</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>84363ebc-5c9c-4fea-9153-166a4e599c24</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>12c323ad-d7a5-4561-a789-54329f3f465e</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>59f01766-310d-42f2-941e-47b2316f73c0</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Has_Message_Error</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>true</rightOperandValue>
                </botStepConditions>
                <botStepConditions>
                    <leftOperandName>Error_Type</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>StatusNot200</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botMessages>
                        <message>{!Error_Message}</message>
                        <messageIdentifier>6b6081f5-a40c-46eb-ba80-157bc4fce907</messageIdentifier>
                    </botMessages>
                    <stepIdentifier>a06de598-3ed8-46d2-8e8b-ba7e5ae83847</stepIdentifier>
                    <type>Message</type>
                </botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Search_Transactions_Again</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>01a996c6-ec04-43a5-91e7-cbe9092015b6</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>5cd85731-3535-4c1c-9230-fea60a4a1cba</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Has_Message_Error</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>true</rightOperandValue>
                </botStepConditions>
                <botStepConditions>
                    <leftOperandName>Error_Type</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>NoContent</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botMessages>
                        <message>{!Error_Message}</message>
                        <messageIdentifier>a1de7896-d45b-4484-a426-cf1523695b8f</messageIdentifier>
                    </botMessages>
                    <stepIdentifier>3a97d3b1-a8e8-487d-ab16-2decc3ba228a</stepIdentifier>
                    <type>Message</type>
                </botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Search_Transactions_Again</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>25d2d8db-4a02-46d4-a133-9cb40fa5f4ef</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>5d55e389-a12a-45ff-bc62-caf4f9a181e1</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Has_Message_Error</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>true</rightOperandValue>
                </botStepConditions>
                <botStepConditions>
                    <leftOperandName>Error_Type</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>ServerError</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botMessages>
                        <message>{!Error_Message}</message>
                        <messageIdentifier>a52fb913-2a2d-4991-b3c9-3a1335afea9d</messageIdentifier>
                    </botMessages>
                    <stepIdentifier>22cce19c-e6a9-4e87-a964-3cdb120ffaf6</stepIdentifier>
                    <type>Message</type>
                </botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Search_Transactions_Again</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>916d78e4-999e-4b4a-9f49-2e7faf06fb3d</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>aabadc45-df44-4c03-a934-ae2743983b85</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Has_Message_Error</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>true</rightOperandValue>
                </botStepConditions>
                <botStepConditions>
                    <leftOperandName>Error_Type</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>NO_TRANSACTIONS_FOUND_ERROR</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Search_Transactions_Again</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>5256d4d2-9264-43ac-81af-6d9fe0587854</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>c6413fcb-9920-4273-ab05-2d2f11020d87</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Has_Message_Error</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>true</rightOperandValue>
                </botStepConditions>
                <botStepConditions>
                    <leftOperandName>Error_Type</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>HTTP_REQUEST_SEND_ERROR</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Continue_Tracking</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>2d50d065-dc7f-4c15-aa9c-876cc4d60a9d</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>26cdcd07-0c2d-45fd-a1bf-bbfd74b4e0db</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Has_Message_Error</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>true</rightOperandValue>
                </botStepConditions>
                <botStepConditions>
                    <leftOperandName>Error_Type</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>HTTP_RESPONSE_ERROR</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Continue_Tracking</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>c9eacffd-31b5-47e5-82fd-ff38981647a9</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>b6d65321-1fa9-45d5-9ff1-7bf5a698acda</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Has_Message_Error</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>true</rightOperandValue>
                </botStepConditions>
                <botStepConditions>
                    <leftOperandName>Error_Type</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>NO_ACCOUNT_PERMISSION_ERROR</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Track_Specific_Payment</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>75b6a161-8003-4a7a-a6c2-229b1920482e</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>8b5642ce-da4b-4e1f-8b48-1ead4c115561</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Has_Message_Error</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>true</rightOperandValue>
                </botStepConditions>
                <botStepConditions>
                    <leftOperandName>Error_Type</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>INVALID_AMOUNT_ERROR</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Search_Transactions_Again</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>2822dd78-a244-4914-b4f9-00242e3f0ec7</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>4c638c5c-e889-44cc-b634-339d50842a68</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Transaction_Search_Result</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>1</rightOperandValue>
                </botStepConditions>
                <botStepConditions>
                    <leftOperandName>Has_Message_Error</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>false</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botMessages>
                        <message>We could not find any transactions for your inputs.</message>
                        <messageIdentifier>27642e59-874b-4c9e-ba90-58728eadfdab</messageIdentifier>
                    </botMessages>
                    <stepIdentifier>d4eaabfa-d267-4270-a7ba-4de7410ff310</stepIdentifier>
                    <type>Message</type>
                </botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Search_Transactions_Again</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>79448a05-6d8d-4724-9fcd-e824484aa492</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>4b1b1c93-b8b8-49b3-9090-6d50486e16a6</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Transaction_Search_Result</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>0</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botMessages>
                        <message>We could not find any transactions for your inputs.</message>
                        <messageIdentifier>1151ce57-b269-4a56-b72b-8d3b497ca4a9</messageIdentifier>
                    </botMessages>
                    <stepIdentifier>81a6a23b-7297-4176-a1a2-13aeef38a7f6</stepIdentifier>
                    <type>Message</type>
                </botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Search_Transactions_Again</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>6db7586a-4869-4ec3-82e1-2e1c9010ced6</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>eb5e377d-92c2-44ae-9f1d-479bd4d6538e</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <askCollectIfSet>false</askCollectIfSet>
                    <autoSelectIfSingleChoice>false</autoSelectIfSingleChoice>
                    <botMessages>
                        <message>The transactions that I have found with your inputs are</message>
                        <messageIdentifier>921bbe62-8d32-4dd5-87dd-31aa12a30582</messageIdentifier>
                    </botMessages>
                    <botVariableOperands>
                        <disableAutoFill>true</disableAutoFill>
                        <sourceName>_Object</sourceName>
                        <sourceType>StandardMlSlotClass</sourceType>
                        <targetName>Selected_Transaction</targetName>
                        <targetType>ConversationVariable</targetType>
                    </botVariableOperands>
                    <ignoreIntentRecognition>false</ignoreIntentRecognition>
                    <invalidInputBotNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Track_Specific_Payment</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </invalidInputBotNavigation>
                    <optionalCollect>false</optionalCollect>
                    <quickReplyOptionTemplate>{!outputText}</quickReplyOptionTemplate>
                    <quickReplyType>Dynamic</quickReplyType>
                    <quickReplyWidgetType>Buttons</quickReplyWidgetType>
                    <sourceVariableName>Transaction_Search_Result</sourceVariableName>
                    <sourceVariableType>ConversationVariable</sourceVariableType>
                    <type>Collect</type>
                    <variableOperationIdentifier>3d2c5951-e00f-49a1-9734-2c74fdda59b5</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>8737f705-e6c6-4953-b79d-155b8a0e7825</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>OSB_VA_Extract_UETR</invocationActionName>
                        <invocationActionType>flow</invocationActionType>
                        <invocationMappings>
                            <parameterName>SelectedTransaction</parameterName>
                            <type>Input</type>
                            <variableName>Selected_Transaction</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>TransactionReference</parameterName>
                            <type>Output</type>
                            <variableName>Transaction_Reference</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>UETR</parameterName>
                            <type>Output</type>
                            <variableName>UETR</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>f27e756c-cf86-4f69-848d-9a32e866c84e</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>abbbf33e-ca44-4c0d-ae29-b271eaaebf8d</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Transaction_Reference</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>IsNotSet</operatorType>
                </botStepConditions>
                <botStepConditions>
                    <leftOperandName>UETR</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>IsNotSet</operatorType>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Transaction_Search</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>1838baf6-67a9-4a66-b433-c37d8a49022c</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>1c60d5dd-bab0-4733-bf42-1c8f23f7c38c</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>OSB_SRV_FinancialAccPermissionHandler</invocationActionName>
                        <invocationActionType>apex</invocationActionType>
                        <invocationMappings>
                            <parameterName>accountNumber</parameterName>
                            <type>Input</type>
                            <variableName>Account_Number</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>contactId</parameterName>
                            <type>Input</type>
                            <variableName>ContactId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>permissionType</parameterName>
                            <type>Input</type>
                            <value>Payment tracking</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>hasError</parameterName>
                            <type>Output</type>
                            <variableName>Has_Payment_Tracking_Permission_on_Account</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorMessage</parameterName>
                            <type>Output</type>
                            <variableName>Error_Message</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>43ccf8fd-521e-433c-ab70-c5c7b7dfcee2</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>b9204818-d560-4b02-8829-f82508e91262</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Has_Payment_Tracking_Permission_on_Account</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>false</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botMessages>
                        <message>You no longer have access to the account linked to the transaction that you have chosen.</message>
                        <messageIdentifier>a9febd1e-79ee-45c4-8d2d-499aed0ab6f2</messageIdentifier>
                    </botMessages>
                    <stepIdentifier>befa056d-9662-4945-b3cf-bd98b703e265</stepIdentifier>
                    <type>Message</type>
                </botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Choose_another_transaction</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>9b3b5f95-ae0c-4f91-8123-d56a2f1e4b7e</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>6de1b1b6-6d4d-41e5-b887-107341879de6</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botNavigation>
                    <botNavigationLinks>
                        <targetBotDialog>Track_Selected_Payment</targetBotDialog>
                    </botNavigationLinks>
                    <type>Redirect</type>
                </botNavigation>
                <stepIdentifier>55ec88b8-6dd4-47fd-9302-c505d6b8ebce</stepIdentifier>
                <type>Navigation</type>
            </botSteps>
            <developerName>Transaction_Search</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Transaction Search</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botDialogGroup>Payment_Tracking</botDialogGroup>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Has_Message_Error</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>a53f1b8a-ba90-41c4-8ce9-a8159b47226a</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>9b28222c-e053-4ed1-a1ad-714ecf27f156</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Error_Message</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>95691266-e8ca-4e98-983a-3a613d621456</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>1b36e0f1-acdb-40b3-a4ca-76293621d98e</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Error_Type</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>846a4e10-d0cd-47cd-adec-12e06fc219b6</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>d2dfb184-5170-4378-adf6-16d098aae5e1</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Error_Log_Id</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>8bb21858-f015-49e3-a978-2f754051e04b</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>ffecc883-3b16-48c1-9ce2-a881910be9f4</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Flow_Action_Type</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>8498d271-e0f7-4716-9e15-fc26b0781fcd</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>ac1f5671-9cc1-4d68-a0ac-1e6412e49043</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>f5aabec8-d959-45a9-ad9c-564a12a3df91</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Payment_Details</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>81bf0c19-cb52-49d6-827c-975fec7c151c</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>58c1d0d8-4862-4c29-ae00-a753a6e95511</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>3cca1733-5a75-4c9a-84ff-e85a941786f9</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>OSB_VA_PaymentTracking_CTRL</invocationActionName>
                        <invocationActionType>apex</invocationActionType>
                        <invocationMappings>
                            <parameterName>uetr</parameterName>
                            <type>Input</type>
                            <variableName>UETR</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>transactionReference</parameterName>
                            <type>Input</type>
                            <variableName>Transaction_Reference</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorOutputMessage</parameterName>
                            <type>Output</type>
                            <variableName>Error_Message</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorType</parameterName>
                            <type>Output</type>
                            <variableName>Error_Type</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>hasError</parameterName>
                            <type>Output</type>
                            <variableName>Has_Message_Error</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>actionType</parameterName>
                            <type>Output</type>
                            <variableName>Flow_Action_Type</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>outputText</parameterName>
                            <type>Output</type>
                            <variableName>Payment_Details</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorLogId</parameterName>
                            <type>Output</type>
                            <variableName>Error_Log_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>cae0f4ec-149f-453a-af33-9e8d14c719cd</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>6b2e2d17-01fb-4cde-b242-2c5ae99e541f</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>Create_Case_From_Chat_Bot</invocationActionName>
                        <invocationActionType>flow</invocationActionType>
                        <invocationMappings>
                            <parameterName>ErrorLogId</parameterName>
                            <type>Input</type>
                            <variableName>Error_Log_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ServiceType</parameterName>
                            <type>Input</type>
                            <value>Payment Tracking</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>CountryName</parameterName>
                            <type>Input</type>
                            <variableName>Country_name</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>CaseId</parameterName>
                            <type>Input</type>
                            <variableName>Case_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ContactId</parameterName>
                            <type>Input</type>
                            <variableName>ContactId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ActionType</parameterName>
                            <type>Input</type>
                            <variableName>Flow_Action_Type</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>CaseId</parameterName>
                            <type>Output</type>
                            <variableName>Case_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>6ada2c5d-7db2-4ca3-a03b-5ca54508fbfc</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>876f4e49-e5ca-42c4-a854-c98ca66b5cf4</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Case_Id</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>691e340e-ba80-41c4-b2ab-507d0b5877c0</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>a61eb75b-1d62-467b-942b-5e0284df94f3</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Error_Log_Id</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>f0403855-da22-4360-8e0a-657a3043c8f5</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>6e464e6f-62ca-49ab-97d5-36d9c2d60de3</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Flow_Action_Type</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>cce8a4d5-ebb1-4ffe-8800-e164026671db</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>8b106667-bf90-4f8d-b5dd-f406130633a3</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>5b5310f5-9920-48d3-9207-bdc9233775dc</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Has_Message_Error</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>true</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botMessages>
                        <message>{!Error_Message}</message>
                        <messageIdentifier>cf540837-82a5-4aef-b66b-8fe079d38923</messageIdentifier>
                    </botMessages>
                    <stepIdentifier>40b59cdf-045c-4029-8005-85c5eeacc174</stepIdentifier>
                    <type>Message</type>
                </botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Continue_Tracking</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>9e4c1f8f-395e-4df1-8efb-a05f45177391</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>faec827c-60e6-41ba-8953-88f39c2e9f89</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Payment_Details</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>IsNotSet</operatorType>
                </botStepConditions>
                <botSteps>
                    <botMessages>
                        <message>Swift transaction status could not be confirmed</message>
                        <messageIdentifier>4b19847a-55ba-4738-8da4-4924de3d55b6</messageIdentifier>
                    </botMessages>
                    <stepIdentifier>4cf55d9b-9009-4497-9e05-cb73b85f901d</stepIdentifier>
                    <type>Message</type>
                </botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Continue_Tracking</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>33e9c9fe-bdd4-4b15-8fdb-a8e57e06b310</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>eae0632a-b6e9-4a26-9bc5-198c8716dfd3</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Payment_Details</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>IsSet</operatorType>
                </botStepConditions>
                <botStepConditions>
                    <leftOperandName>Payment_Details</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>Payment Data has not been found.</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botMessages>
                        <message>Swift transaction status could not be confirmed</message>
                        <messageIdentifier>13fa7e41-8602-442b-b93c-8e8cbe36af30</messageIdentifier>
                    </botMessages>
                    <stepIdentifier>51ebbdd1-20c4-405b-9210-449e590bacef</stepIdentifier>
                    <type>Message</type>
                </botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Continue_Tracking</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>6e58bb50-0956-4354-aed9-fa3ea3bb2a19</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>dccccca2-f6d9-4f2e-85f4-a88fb335a40a</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botMessages>
                    <message>{!Payment_Details}</message>
                    <messageIdentifier>9790130e-3c71-467b-a67a-62a98c9a784a</messageIdentifier>
                </botMessages>
                <stepIdentifier>a993ef27-d866-45e5-9a34-77f5ab8c02bd</stepIdentifier>
                <type>Message</type>
            </botSteps>
            <botSteps>
                <botNavigation>
                    <botNavigationLinks>
                        <targetBotDialog>Track_Specific_Payment</targetBotDialog>
                    </botNavigationLinks>
                    <type>Redirect</type>
                </botNavigation>
                <stepIdentifier>3fa783c8-72b8-43d2-a5d1-0aa30b427efc</stepIdentifier>
                <type>Navigation</type>
            </botSteps>
            <developerName>Track_Selected_Payment</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Track Selected Payment</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botDialogGroup>Payment_Tracking</botDialogGroup>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>User_Input</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>77ebb2c9-c8d3-49d5-8eaf-36c5b6516f91</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>e26dba8c-0f8f-4f71-ac3c-4b8fe016410a</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Has_Payment_Tracking_Permission_on_Account</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>191b6c4f-b6a7-4435-b013-557c458ed3f8</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>b1f90e04-b93b-4cbd-ba2c-6a69645ca1e9</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Account_Number</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>5ea9ae37-e4c1-480d-a395-435e98a8a17a</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>3f74db40-c1e6-4610-9c13-a5fb5025bf42</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Has_AccessTo_Payment_Tracking</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>1d5506b5-4087-47ba-9638-2f62de631413</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>a41eec19-1b64-4562-ab55-baa19274584a</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Case_Id</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>c771a852-d735-40af-b5f3-8924beba407e</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>dfe656dc-6a53-4c54-b010-f163abfde8e9</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>60246585-d519-4251-8dd3-83bfa12c737c</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>Create_Case_From_Chat_Bot</invocationActionName>
                        <invocationActionType>flow</invocationActionType>
                        <invocationMappings>
                            <parameterName>ChatTranscriptId</parameterName>
                            <type>Input</type>
                            <variableName>RoutableId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ErrorLogId</parameterName>
                            <type>Input</type>
                            <variableName>Error_Log_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ServiceType</parameterName>
                            <type>Input</type>
                            <value>Payment Tracking</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>CountryName</parameterName>
                            <type>Input</type>
                            <variableName>Country_name</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>CaseId</parameterName>
                            <type>Input</type>
                            <variableName>Case_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ContactId</parameterName>
                            <type>Input</type>
                            <variableName>ContactId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ActionType</parameterName>
                            <type>Input</type>
                            <value>Create New Case</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>CaseId</parameterName>
                            <type>Output</type>
                            <variableName>Case_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>241ba1d8-5d11-4a11-a49f-cbce6b783922</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>58d84a3d-84da-4350-8014-0fc72439cd6f</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <askCollectIfSet>false</askCollectIfSet>
                    <botMessages>
                        <message>Do you know the transaction reference number?</message>
                        <messageIdentifier>74af5852-0dfb-4d70-907c-6f213f7fb197</messageIdentifier>
                    </botMessages>
                    <botQuickReplyOptions>
                        <literalValue>Yes</literalValue>
                        <quickReplyOptionIdentifier>91f5eb90-2c4d-4122-b70e-af5d5806dcaf</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botQuickReplyOptions>
                        <literalValue>No</literalValue>
                        <quickReplyOptionIdentifier>f27ae5d5-56d8-435f-99fb-5ef3e14d9489</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botVariableOperands>
                        <disableAutoFill>true</disableAutoFill>
                        <sourceName>_Text</sourceName>
                        <sourceType>StandardMlSlotClass</sourceType>
                        <targetName>User_Input</targetName>
                        <targetType>ConversationVariable</targetType>
                    </botVariableOperands>
                    <ignoreIntentRecognition>false</ignoreIntentRecognition>
                    <optionalCollect>false</optionalCollect>
                    <quickReplyType>Static</quickReplyType>
                    <quickReplyWidgetType>Menu</quickReplyWidgetType>
                    <type>Collect</type>
                    <variableOperationIdentifier>dd0f6edc-b9d9-49d2-b872-b70770c62cc4</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>89803462-a14e-456f-9f04-920389fc7478</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>Yes</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Track_Payment</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>b228b63f-83aa-4d85-9395-c8a05829aa28</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>1f8b6272-07ba-4159-b2d8-df3fbd70c6b2</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>No</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Transaction_Search</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>30b83a41-cc67-4d9a-a234-f00bf6efa44f</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>9bcb6c5d-1c3b-4533-b2dd-f87358186045</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>Menu</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Main_Menu</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>76e408b5-469b-4a71-9786-fa87dc8712bc</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>a2d01417-2de1-42a6-b3d3-91a64f7655a1</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botMessages>
                    <message>I did not understand that</message>
                    <messageIdentifier>03246346-9aaa-4637-8374-c4790834be90</messageIdentifier>
                </botMessages>
                <stepIdentifier>ba0382fe-90a9-4f9f-b1dc-3c0ecf32bf7e</stepIdentifier>
                <type>Message</type>
            </botSteps>
            <botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Do_you_have_transaction_reference</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>fe31db92-4c2a-4910-8465-da2b277f39e1</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>47ceabc6-f7cd-4563-9aba-ea9d8aeee265</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <stepIdentifier>d7db3d4c-5dba-489b-b402-44de2cab1faf</stepIdentifier>
                <type>Wait</type>
            </botSteps>
            <developerName>Do_you_have_transaction_reference</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Track my payment</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Has_Payment_Tracking_Permission_on_Account</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>IsSet</operatorType>
                </botStepConditions>
                <botStepConditions>
                    <leftOperandName>Has_Payment_Tracking_Permission_on_Account</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>false</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botMessages>
                        <message>You currently don&apos;t have access to the selected account. Please contact your team administrator to grant you access...</message>
                        <messageIdentifier>********-279c-40fd-811e-00375be903a9</messageIdentifier>
                    </botMessages>
                    <stepIdentifier>42b744de-950a-458f-a803-b5a14ec38627</stepIdentifier>
                    <type>Message</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Account_Number</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>479ec1d5-4770-4fc1-8ed2-1825b136e669</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>970e9e3e-9c9d-4f4f-ade7-8b2579c54f78</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Choose_another_transaction</targetBotDialog>
                        </botNavigationLinks>
                        <type>Call</type>
                    </botNavigation>
                    <stepIdentifier>d97220aa-ef5f-4b98-8b69-4805f9e1c374</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>dde61dd1-e797-429b-b971-ea47eadd34c2</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Is_Account_Number_Valid</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>IsSet</operatorType>
                </botStepConditions>
                <botStepConditions>
                    <leftOperandName>Is_Account_Number_Valid</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>false</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botMessages>
                        <message>The account number you provided is invalid.</message>
                        <messageIdentifier>cdf31e0b-bf76-4705-adfe-896a4d4bde08</messageIdentifier>
                    </botMessages>
                    <stepIdentifier>11eaa125-5363-4839-8007-3fd7463c00a8</stepIdentifier>
                    <type>Message</type>
                </botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Choose_another_transaction</targetBotDialog>
                        </botNavigationLinks>
                        <type>Call</type>
                    </botNavigation>
                    <stepIdentifier>737d9a93-42f3-4979-820c-7cb3432f2b87</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>500ba730-ca68-49d6-9d11-07d0f59b566b</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Has_Financial_Account</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>IsSet</operatorType>
                </botStepConditions>
                <botStepConditions>
                    <leftOperandName>Has_Financial_Account</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>false</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botMessages>
                        <message>The account number was not found.</message>
                        <messageIdentifier>38e7482b-650f-4a71-abb8-d43bbb96033b</messageIdentifier>
                    </botMessages>
                    <stepIdentifier>b73f86c0-46c4-4220-a5f2-daab5286b84a</stepIdentifier>
                    <type>Message</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Has_Financial_Account</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>085f9bb0-1f80-4d22-9b1e-7a65fbfa562e</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>58056b7b-21af-4a46-8d0d-de34d5c3186c</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Choose_another_transaction</targetBotDialog>
                        </botNavigationLinks>
                        <type>Call</type>
                    </botNavigation>
                    <stepIdentifier>b806c8c4-97fd-422c-b975-97235b4c8959</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>71eb1c0c-5e3d-4c05-b0ae-f53d49018765</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botMessages>
                    <message>You currently don&apos;t have access to this service</message>
                    <messageIdentifier>feb91ee5-3775-4bef-b6f4-cc3fd686f091</messageIdentifier>
                </botMessages>
                <stepIdentifier>6ae8f0d0-30ac-4957-ac16-1f81c242cccd</stepIdentifier>
                <type>Message</type>
            </botSteps>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Has_Access_To_Email_Statement</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>b5fc37a0-0c3e-4736-8068-6020dd5f81b3</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>6761b7b9-4331-46a8-811b-c2d4aa4b3245</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>58217883-7ed3-4561-8e39-2b94f76e48e7</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botNavigation>
                    <botNavigationLinks>
                        <targetBotDialog>Is_there_anything_else_I_can_help_you_with</targetBotDialog>
                    </botNavigationLinks>
                    <type>Redirect</type>
                </botNavigation>
                <stepIdentifier>dd55a216-a7ce-43e7-95ff-9ba2b10da2a3</stepIdentifier>
                <type>Navigation</type>
            </botSteps>
            <developerName>No_service_access</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>No service access</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>User_Input</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>897f96f7-7b08-41b1-9f68-b0ef9cb56027</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>3c4729c4-b0f2-4f30-8a25-169af28a7b54</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>7ea85bfb-4619-4a93-908d-a8c2bd7051d7</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <askCollectIfSet>false</askCollectIfSet>
                    <botMessages>
                        <message>Is there anything else I can help you with?</message>
                        <messageIdentifier>236edbe5-0b22-4c6e-9312-d7bb3e9657dd</messageIdentifier>
                    </botMessages>
                    <botQuickReplyOptions>
                        <literalValue>Yes</literalValue>
                        <quickReplyOptionIdentifier>76fdf85a-818d-4582-98f1-31ad3c03727e</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botQuickReplyOptions>
                        <literalValue>No</literalValue>
                        <quickReplyOptionIdentifier>a1fd566f-03db-4b80-838f-5493c997a348</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botVariableOperands>
                        <disableAutoFill>true</disableAutoFill>
                        <sourceName>_Text</sourceName>
                        <sourceType>StandardMlSlotClass</sourceType>
                        <targetName>User_Input</targetName>
                        <targetType>ConversationVariable</targetType>
                    </botVariableOperands>
                    <ignoreIntentRecognition>false</ignoreIntentRecognition>
                    <optionalCollect>false</optionalCollect>
                    <quickReplyType>Static</quickReplyType>
                    <quickReplyWidgetType>Menu</quickReplyWidgetType>
                    <type>Collect</type>
                    <variableOperationIdentifier>091c1f00-3dcc-4675-87b5-846a1a4b6064</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>c7b9d8f5-0223-429b-810d-4019b43d1aef</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>Yes</rightOperandValue>
                </botStepConditions>
                <botStepConditions>
                    <leftOperandName>Is_Enhanced_Bot_Enabled</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>true</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Main_Menu_Authenticated</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>6c5e2229-fbe1-464a-bacc-a910f3a105a8</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>6900323c-1c57-497f-a8ee-fdf71c08fbf6</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>No</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>End_Chat</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>97ce5795-b246-49ba-82cc-d47a1252cf82</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>e95ddb2b-8824-4c11-89c8-605473bf2511</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>Menu</rightOperandValue>
                </botStepConditions>
                <botStepConditions>
                    <leftOperandName>Is_Enhanced_Bot_Enabled</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>false</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Main_Menu</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>e68b1b28-adb5-4ce8-a5a6-6387f6ae1f8a</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>263bb5cc-5e8a-4f5a-8cda-060ad80a09e4</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botMessages>
                    <message>Sorry, I didn&apos;t understand that.</message>
                    <messageIdentifier>dd67a629-4f75-423b-986d-8f05a7f8e64f</messageIdentifier>
                </botMessages>
                <stepIdentifier>ae0d66fb-c06b-4dfc-ba67-a2df972fad58</stepIdentifier>
                <type>Message</type>
            </botSteps>
            <botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Is_there_anything_else_I_can_help_you_with</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>61895991-7e0a-454d-ae2c-fcfa98f7c9ce</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>ce95fd4e-c455-43ec-a4b9-3bcc7df7c8c1</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <stepIdentifier>765a04ac-e377-4a1c-844b-305462b71328</stepIdentifier>
                <type>Wait</type>
            </botSteps>
            <developerName>Is_there_anything_else_I_can_help_you_with</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Is there anything else I can help you with?</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botDialogGroup>Payment_Tracking</botDialogGroup>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>User_Input</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>875bcdbb-8924-4101-aae3-ecfdd3b8d74f</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>e325aa7d-2535-47b7-bfa5-01223e1ecdc2</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>12b84119-65b9-418f-b54c-55f687f71293</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <askCollectIfSet>false</askCollectIfSet>
                    <botMessages>
                        <message>Would you like to search for another transaction reference number?</message>
                        <messageIdentifier>8388f622-799b-4089-85e5-8f6cc41fae63</messageIdentifier>
                    </botMessages>
                    <botQuickReplyOptions>
                        <literalValue>Yes</literalValue>
                        <quickReplyOptionIdentifier>adcdbe6d-5f2f-4576-92a0-cd10411ed828</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botQuickReplyOptions>
                        <literalValue>No</literalValue>
                        <quickReplyOptionIdentifier>11ebca9c-1b26-4b73-a5da-a38ce587251c</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botVariableOperands>
                        <disableAutoFill>true</disableAutoFill>
                        <sourceName>_Text</sourceName>
                        <sourceType>StandardMlSlotClass</sourceType>
                        <targetName>User_Input</targetName>
                        <targetType>ConversationVariable</targetType>
                    </botVariableOperands>
                    <ignoreIntentRecognition>false</ignoreIntentRecognition>
                    <optionalCollect>false</optionalCollect>
                    <quickReplyType>Static</quickReplyType>
                    <quickReplyWidgetType>Menu</quickReplyWidgetType>
                    <type>Collect</type>
                    <variableOperationIdentifier>c4680bf8-41dc-40c2-8103-318e276947d8</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>5a50d0d2-6028-496b-9815-ce8bd6851893</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>Yes</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Track_Payment</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>326e35ca-22d4-4149-b058-0cd187f9b6e7</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>e4871e74-6808-494c-8085-ec9819140f66</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>No</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Is_there_anything_else_I_can_help_you_with</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>5cbd1219-d2d8-4d72-8688-2c7dbbb7f631</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>369640e2-e315-4e81-9a13-49720ffddb51</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>Menu</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Main_Menu</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>1d40eb1f-b856-4d40-be15-cc9a4d535b25</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>1be89ab1-c33b-4e25-83c9-1964d68bcaac</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botMessages>
                    <message>I did not understand that</message>
                    <messageIdentifier>87facb3a-0011-4d47-acec-8dba1113d6f3</messageIdentifier>
                </botMessages>
                <stepIdentifier>dea25342-41b5-44e8-b77d-0114bfde2072</stepIdentifier>
                <type>Message</type>
            </botSteps>
            <botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Track_another_single_payment</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>c1bd3349-d14f-4c4f-86b5-9732912f5577</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>57061f4d-61a3-4593-84a7-9c4b746b67df</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <stepIdentifier>3699606f-4908-4dd1-a3a2-69eb4f635635</stepIdentifier>
                <type>Wait</type>
            </botSteps>
            <developerName>Track_another_single_payment</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Track another single payment?</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botDialogGroup>Payment_Tracking</botDialogGroup>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>User_Input</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>1a5d813e-f926-4102-b7fb-9a7dbd279a70</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>f5a70211-f97a-4450-8138-c3e47bec3387</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>ec4c16ac-d024-40c9-9b39-002b91043292</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <askCollectIfSet>false</askCollectIfSet>
                    <botMessages>
                        <message>Would you like to search again?</message>
                        <messageIdentifier>04824e20-9085-4676-bb7f-909386ededa0</messageIdentifier>
                    </botMessages>
                    <botQuickReplyOptions>
                        <literalValue>Yes</literalValue>
                        <quickReplyOptionIdentifier>0976727d-de19-4a9f-a834-9b06e64b4738</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botQuickReplyOptions>
                        <literalValue>No</literalValue>
                        <quickReplyOptionIdentifier>aede4b67-4711-4754-94e0-6d1195b9343b</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botVariableOperands>
                        <disableAutoFill>true</disableAutoFill>
                        <sourceName>_Text</sourceName>
                        <sourceType>StandardMlSlotClass</sourceType>
                        <targetName>User_Input</targetName>
                        <targetType>ConversationVariable</targetType>
                    </botVariableOperands>
                    <ignoreIntentRecognition>false</ignoreIntentRecognition>
                    <optionalCollect>false</optionalCollect>
                    <quickReplyType>Static</quickReplyType>
                    <quickReplyWidgetType>Menu</quickReplyWidgetType>
                    <type>Collect</type>
                    <variableOperationIdentifier>82d3db6f-1c60-4456-ad2b-2a3cb64b6e6e</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>83db512f-ffac-4180-bfaf-d2e02623964c</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>Yes</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Transaction_Search</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>9cddbbaa-948f-4b19-926f-4dee3777bf2a</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>d204f6b6-16d8-434d-92c1-33db7d9c655f</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>No</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Is_there_anything_else_I_can_help_you_with</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>65535402-c15c-44fe-a2f7-30acfd030818</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>f946f4e9-4375-47ee-8044-5bb31f500590</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>Menu</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Main_Menu</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>104a6288-734a-4d73-86c2-8f06b0ff095a</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>4321bf86-4561-46f5-a0a8-ee6bbfa4d084</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botMessages>
                    <message>I did not understand that.</message>
                    <messageIdentifier>eb7cd810-7ece-4f2e-bc0d-87f29d8807ca</messageIdentifier>
                </botMessages>
                <stepIdentifier>dbd6b964-c836-49f3-99a5-8ee25d40a582</stepIdentifier>
                <type>Message</type>
            </botSteps>
            <botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Search_Transactions_Again</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>ee03cdea-d617-4edb-95fe-4a5c3efea175</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>33034ecf-e027-4977-b4a2-07a54fc1e8c7</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <stepIdentifier>68313b4d-916e-4098-b106-c3e9f9b983d1</stepIdentifier>
                <type>Wait</type>
            </botSteps>
            <developerName>Search_Transactions_Again</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Search Transactions Again</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botDialogGroup>Payment_Tracking</botDialogGroup>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>User_Input</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>b9b69a1e-562d-4473-92da-85efb02ec320</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>82bad9a1-7e68-4b3d-ab52-8cc5c11512a7</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>d969afc1-d3a9-45cc-ac01-26380aca3fc8</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <askCollectIfSet>false</askCollectIfSet>
                    <botMessages>
                        <message>Would you like to continue or try again later?</message>
                        <messageIdentifier>1aefb73e-995b-48d6-aa14-3eb43752e38d</messageIdentifier>
                    </botMessages>
                    <botQuickReplyOptions>
                        <literalValue>Continue</literalValue>
                        <quickReplyOptionIdentifier>cb23f4e9-1b92-4304-9c78-5edebd92fe66</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botQuickReplyOptions>
                        <literalValue>Try again later</literalValue>
                        <quickReplyOptionIdentifier>c3bfba56-cb31-4a0f-a948-e0ab2e35a798</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botVariableOperands>
                        <disableAutoFill>true</disableAutoFill>
                        <sourceName>_Text</sourceName>
                        <sourceType>StandardMlSlotClass</sourceType>
                        <targetName>User_Input</targetName>
                        <targetType>ConversationVariable</targetType>
                    </botVariableOperands>
                    <ignoreIntentRecognition>false</ignoreIntentRecognition>
                    <optionalCollect>false</optionalCollect>
                    <quickReplyType>Static</quickReplyType>
                    <quickReplyWidgetType>Menu</quickReplyWidgetType>
                    <type>Collect</type>
                    <variableOperationIdentifier>2318e00a-8cce-403d-a8dd-f0659875957d</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>61326fa2-2b10-415e-a369-9a7751c8d358</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>Continue</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Do_you_have_transaction_reference</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>92a34cc5-1840-43c4-b89c-adbfb5839e99</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>1fe2ecdd-b349-472c-9851-aa0aaea0af0a</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>Try again later</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Is_there_anything_else_I_can_help_you_with</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>8921f5cf-1dcf-45bc-92de-6d20a9abb1ba</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>594e06d1-e263-4aa5-8b3d-6880ccd17e08</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>Menu</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Main_Menu</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>a2de6015-9b43-454b-aab4-0926da3442f0</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>4467162a-b7d3-4ac5-9008-03cfa3161122</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botMessages>
                    <message>I did not understand that.</message>
                    <messageIdentifier>273eb722-5eeb-4f4b-8457-2b2cba2e1b0f</messageIdentifier>
                </botMessages>
                <stepIdentifier>c7e9c80a-618f-4199-adf3-fedaff0c04c3</stepIdentifier>
                <type>Message</type>
            </botSteps>
            <botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Continue_Tracking</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>3d1dc82b-60de-4c99-b43b-f7afa2125678</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>4e08ed28-b5d7-42a8-a505-c660c6d0b7fd</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <stepIdentifier>4dffed65-fd3e-4b0b-aa2f-30b853eb3d27</stepIdentifier>
                <type>Wait</type>
            </botSteps>
            <developerName>Continue_Tracking</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Continue Tracking?</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botDialogGroup>Payment_Tracking</botDialogGroup>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>User_Input</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>a3de6c0f-f78e-4d12-8d4d-67d1b5a4e079</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>b0725170-9528-421b-b4f9-05b8741822d4</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Has_Payment_Tracking_Permission_on_Account</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>fb62e10c-be42-43ac-bbe7-69a766f239ef</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>deaf03bd-549d-410a-beff-b3dd94f0dd22</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Account_Number</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>ba55fc89-3ff8-4510-9491-8715bb1ac1f7</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>b585ca8b-4405-4d9e-b102-d8f2172c7cde</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Is_Account_Number_Valid</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>a49b5b23-4219-447e-96e6-f28de964148b</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>3934b561-6ea9-4df3-9f9b-b55896df4ae8</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>ffcd6631-3cd1-488b-8d53-dc209a16bdba</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <askCollectIfSet>false</askCollectIfSet>
                    <botMessages>
                        <message>Would you like to choose another transaction?</message>
                        <messageIdentifier>17cd33c8-5c2e-4b6c-afbb-e6c3707abc32</messageIdentifier>
                    </botMessages>
                    <botQuickReplyOptions>
                        <literalValue>Yes</literalValue>
                        <quickReplyOptionIdentifier>a75d2c63-b099-4d65-9cda-f041b18ac998</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botQuickReplyOptions>
                        <literalValue>No</literalValue>
                        <quickReplyOptionIdentifier>bc384a4f-8906-414b-9af8-b8e5366b1411</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botVariableOperands>
                        <disableAutoFill>true</disableAutoFill>
                        <sourceName>_Text</sourceName>
                        <sourceType>StandardMlSlotClass</sourceType>
                        <targetName>User_Input</targetName>
                        <targetType>ConversationVariable</targetType>
                    </botVariableOperands>
                    <ignoreIntentRecognition>false</ignoreIntentRecognition>
                    <optionalCollect>false</optionalCollect>
                    <quickReplyType>Static</quickReplyType>
                    <quickReplyWidgetType>Menu</quickReplyWidgetType>
                    <type>Collect</type>
                    <variableOperationIdentifier>32a3ad43-35fd-4218-bf1f-01a9448eabcf</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>df5daf07-7457-457e-acae-236b94f1aaa7</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>Yes</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Do_you_have_transaction_reference</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>c6a02fc5-dae3-4df9-8a13-1653e3c750bd</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>2e93a558-9416-47af-9c7e-d434f44eb41a</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>No</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Is_there_anything_else_I_can_help_you_with</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>1fe7742a-9e53-417d-b45a-034be7dc8bf9</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>6c44813c-0252-42fa-9545-e0cbc38dfc15</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>Menu</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Main_Menu</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>54004773-126d-4049-b64f-292ef5b8f617</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>69515b08-dcf7-4bc0-a70e-492fe7122bdc</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botMessages>
                    <message>I did not understand that.</message>
                    <messageIdentifier>f4d4d824-eeb8-4107-b81d-6154355bbcaf</messageIdentifier>
                </botMessages>
                <stepIdentifier>66a4eadb-4419-4fb8-9128-25bd0bdfaf55</stepIdentifier>
                <type>Message</type>
            </botSteps>
            <botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Choose_another_transaction</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>d8f66e43-5baa-43a5-8f0b-76a5f16a3858</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>527407f7-1b71-4018-a5ce-c4922c478d84</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <stepIdentifier>ecf7c9f9-4dc7-40ff-96a5-8dd18dc8ca2d</stepIdentifier>
                <type>Wait</type>
            </botSteps>
            <developerName>Choose_another_transaction</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Choose another transaction</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botSteps>
                <botMessages>
                    <message>I am still learning but I can help you with the following:</message>
                    <messageIdentifier>781430b5-57b9-4ea1-a5ec-4e99b2040b6d</messageIdentifier>
                </botMessages>
                <stepIdentifier>c953a1bb-49b3-479d-9acb-339f43004a60</stepIdentifier>
                <type>Message</type>
            </botSteps>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Has_AccessTo_Payment_Tracking</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>c437ca05-2b92-4b15-9ac0-10909c097a97</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>51e25a2d-7183-4ae1-b651-8bca36b91d61</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>149403bb-42d0-4a45-96b5-bbe92e66fa3b</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botNavigation>
                    <botNavigationLinks>
                        <targetBotDialog>Learn_more_about_OneHub</targetBotDialog>
                    </botNavigationLinks>
                    <botNavigationLinks>
                        <targetBotDialog>Access_enhanced_virtual_assistant_services</targetBotDialog>
                    </botNavigationLinks>
                    <botNavigationLinks>
                        <targetBotDialog>Other</targetBotDialog>
                    </botNavigationLinks>
                    <type>Redirect</type>
                </botNavigation>
                <stepIdentifier>d827f06d-9eb2-47c3-8e70-2e01897bfe44</stepIdentifier>
                <type>Navigation</type>
            </botSteps>
            <description>Main bot menu displayed for authenticated users with access to DCS services (story CDCS 91)</description>
            <developerName>Main_Menu_Authenticated</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Main Menu Authenticated</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botSteps>
                <botNavigation>
                    <botNavigationLinks>
                        <targetBotDialog>What_is_One_Place_to_Land</targetBotDialog>
                    </botNavigationLinks>
                    <botNavigationLinks>
                        <targetBotDialog>Who_can_sign_up_for_OneHub</targetBotDialog>
                    </botNavigationLinks>
                    <botNavigationLinks>
                        <targetBotDialog>Where_is_OneHub_available</targetBotDialog>
                    </botNavigationLinks>
                    <botNavigationLinks>
                        <targetBotDialog>How_do_I_sign_up</targetBotDialog>
                    </botNavigationLinks>
                    <botNavigationLinks>
                        <targetBotDialog>Can_I_access_other_solutions</targetBotDialog>
                    </botNavigationLinks>
                    <botNavigationLinks>
                        <targetBotDialog>Back</targetBotDialog>
                    </botNavigationLinks>
                    <type>Redirect</type>
                </botNavigation>
                <stepIdentifier>25b70e60-250f-4d0c-9182-00142c5b67a1</stepIdentifier>
                <type>Navigation</type>
            </botSteps>
            <description>Menu with questions regarding OneHub (story CDCS-91)</description>
            <developerName>Learn_more_about_OneHub</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Learn more about OneHub</label>
            <mlIntent>Learn_more_about_OneHub</mlIntent>
            <mlIntentTrainingEnabled>false</mlIntentTrainingEnabled>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>One_Country_Active</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>IsNotSet</operatorType>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Choose_country</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>9143e35a-dd79-4861-a52b-c2e8cf2b7ecd</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>17690a82-0005-447a-9383-1db20c6c6ec7</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>One_Country_Active</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>24c6c5bd-2202-4a46-9f09-75ad314d7ae1</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>aa3eb9a3-6e63-4755-ada9-5568bde99063</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Account_Number</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>90da94be-a305-4bbc-941f-4f57b7cb7252</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>cb4c0a03-db4f-4f19-88ac-697dff7e15e4</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>2fe9b19e-a26d-4837-8aee-22312c555e90</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botNavigation>
                    <botNavigationLinks>
                        <targetBotDialog>Do_you_have_transaction_reference</targetBotDialog>
                    </botNavigationLinks>
                    <botNavigationLinks>
                        <targetBotDialog>View_my_balance</targetBotDialog>
                    </botNavigationLinks>
                    <botNavigationLinks>
                        <targetBotDialog>Email_my_statement</targetBotDialog>
                    </botNavigationLinks>
                    <botNavigationLinks>
                        <targetBotDialog>Back</targetBotDialog>
                    </botNavigationLinks>
                    <type>Redirect</type>
                </botNavigation>
                <stepIdentifier>18213cd8-1dff-43e0-927b-0009356539db</stepIdentifier>
                <type>Navigation</type>
            </botSteps>
            <description>Menu with DCS services (story CDCS-91)</description>
            <developerName>Access_enhanced_virtual_assistant_services</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Access enhanced virtual assistant services</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botSteps>
                <botNavigation>
                    <botNavigationLinks>
                        <targetBotDialog>Main_Menu_Authenticated</targetBotDialog>
                    </botNavigationLinks>
                    <type>Redirect</type>
                </botNavigation>
                <stepIdentifier>b77abb35-7002-4853-81a4-d7502c328cd2</stepIdentifier>
                <type>Navigation</type>
            </botSteps>
            <description>Dialog used to redirect to Main Menu  Authenticated (story CDCS-91)</description>
            <developerName>Back</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Back</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botDialogGroup>Balance_Viewing</botDialogGroup>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Error_Log_Id</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>203f436b-4732-4114-9170-4d6ae679464f</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>1a91e3e2-edd5-41f7-9b86-7a4288e3fe32</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>User_Input</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>c38672f9-972a-4a65-9052-244d82e8c322</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>735aa6f2-6912-473a-8838-e60d1682933f</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Account_Number</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>562e1036-7f7c-4f2f-8641-859ee591d473</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>ef6509f1-464e-4678-8ce7-b5e8f339137b</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Case_Id</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>eac6a3e9-daab-47da-8125-3f4b29cb035f</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>1726c4b4-c3c3-49cd-9c2d-89de8bc853e0</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>51aa22b5-86d3-49a3-8c22-20ebcede1047</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>OSB_SRV_FinancialAccPermissionHandler</invocationActionName>
                        <invocationActionType>apex</invocationActionType>
                        <invocationMappings>
                            <parameterName>accountNumber</parameterName>
                            <type>Input</type>
                            <variableName>Account_Number</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>contactId</parameterName>
                            <type>Input</type>
                            <variableName>ContactId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>permissionType</parameterName>
                            <type>Input</type>
                            <value>Balance inquiry</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorMessage</parameterName>
                            <type>Output</type>
                            <variableName>Error_Message</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>hasError</parameterName>
                            <type>Output</type>
                            <variableName>Has_Message_Error</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>56509dab-bffb-43bf-9a98-5b3abc19302e</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>3385aced-0b41-48c2-ae7c-7a053f32c862</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Has_Message_Error</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>true</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>No_service_access</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>f89d34d3-1c25-49d1-8575-5ccad0ca0133</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>4b8ed74a-4e94-407e-9702-1972d8dca54e</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>Create_Case_From_Chat_Bot</invocationActionName>
                        <invocationActionType>flow</invocationActionType>
                        <invocationMappings>
                            <parameterName>ChatTranscriptId</parameterName>
                            <type>Input</type>
                            <variableName>RoutableId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ErrorLogId</parameterName>
                            <type>Input</type>
                            <variableName>Error_Log_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ServiceType</parameterName>
                            <type>Input</type>
                            <value>Balance inquiry</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>CountryName</parameterName>
                            <type>Input</type>
                            <variableName>Country_name</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>CaseId</parameterName>
                            <type>Input</type>
                            <variableName>Case_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ContactId</parameterName>
                            <type>Input</type>
                            <variableName>ContactId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ActionType</parameterName>
                            <type>Input</type>
                            <value>Create New Case</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>CaseId</parameterName>
                            <type>Output</type>
                            <variableName>Case_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>********-ffb9-4a6d-b1e4-d98df003c5ec</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>2d7f883f-65c3-4d5d-8b05-6c19dc2815cb</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <askCollectIfSet>false</askCollectIfSet>
                    <botMessages>
                        <message>Do you know the account number?</message>
                        <messageIdentifier>68b31c62-d651-4d6d-95e8-4bb837caf073</messageIdentifier>
                    </botMessages>
                    <botQuickReplyOptions>
                        <literalValue>Yes</literalValue>
                        <quickReplyOptionIdentifier>934c1ce7-3938-40b3-af2b-f437bbf9c87e</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botQuickReplyOptions>
                        <literalValue>No</literalValue>
                        <quickReplyOptionIdentifier>7edc817b-483b-4488-aee8-9c5471524110</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botVariableOperands>
                        <disableAutoFill>true</disableAutoFill>
                        <sourceName>_Text</sourceName>
                        <sourceType>StandardMlSlotClass</sourceType>
                        <targetName>User_Input</targetName>
                        <targetType>ConversationVariable</targetType>
                    </botVariableOperands>
                    <ignoreIntentRecognition>false</ignoreIntentRecognition>
                    <optionalCollect>false</optionalCollect>
                    <quickReplyType>Static</quickReplyType>
                    <quickReplyWidgetType>Menu</quickReplyWidgetType>
                    <type>Collect</type>
                    <variableOperationIdentifier>f93a2adf-dd24-4853-8332-5cf322019400</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>d7dcc73f-aa4f-467c-9abc-e4460e836b48</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>Yes</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Can_I_have_the_account_number</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>c5b03076-1185-4515-a943-6974c5e119b9</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>62d732ce-0198-4522-bcf9-5650dd3d6a08</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>No</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Get_Clients</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>51741beb-aa83-47be-a515-b01fc3c3593b</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>c048b0a2-2834-4474-bc83-35c69ead1332</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>Menu</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Main_Menu_Authenticated</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>dbb73093-a8a8-4cb0-8544-374e295e6d84</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>52d6a17a-9413-4558-99d3-7c0967174e9a</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botSteps>
                    <botMessages>
                        <message>I did not understand that</message>
                        <messageIdentifier>59d2ac2b-e229-464a-aa20-48f65fc2de7b</messageIdentifier>
                    </botMessages>
                    <stepIdentifier>ffcbd889-8e6b-49d1-bc50-8e15258dec78</stepIdentifier>
                    <type>Message</type>
                </botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>View_my_balance</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>a28ee7fb-baff-43ad-8a82-d01fc2c0e27a</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>2eec97fd-42f7-4cd1-92f1-ec3dce4a6570</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <stepIdentifier>2b074e1e-182a-4c04-8a99-9bb87b96c791</stepIdentifier>
                <type>Wait</type>
            </botSteps>
            <developerName>View_my_balance</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>View my balance</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botDialogGroup>Balance_Viewing</botDialogGroup>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Has_Message_Error</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>bfa54435-48ec-4f13-8f6e-2313447bce34</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>b52fdb01-d957-419c-b71f-7ca9a38b189f</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Error_Type</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>62c37eb0-fd7f-467e-9200-0a15eabf8c98</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>82bc04c0-6ec1-4d72-866d-11c428ed0fad</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Error_Message</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>9ef7c8e8-2c5a-4c7c-aa2f-9552d87d5266</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>076823a9-d09f-444c-b37a-272adc4dbe48</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Balance_Details</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>6ef27b9c-c115-4ee7-9d8a-19b7d10c615d</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>4417f32a-875b-4e60-8674-0f9daa7863f6</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Flow_Action_Type</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>a641995d-237a-47c6-8966-c746644d0c29</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>11cdabdd-b284-4cf3-88c4-b038e3f86c8c</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>cf3c036b-8f03-49ec-bda1-6a7592421dec</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>OSB_VA_OCHAuthenticate_CTRL</invocationActionName>
                        <invocationActionType>apex</invocationActionType>
                        <invocationMappings>
                            <parameterName>serviceType</parameterName>
                            <type>Input</type>
                            <value>Balance inquiry</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>contactId</parameterName>
                            <type>Input</type>
                            <variableName>ContactId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>countryName</parameterName>
                            <type>Input</type>
                            <variableName>Country_name</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>accessToken</parameterName>
                            <type>Output</type>
                            <variableName>OCH_Access_Token</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorMessage</parameterName>
                            <type>Output</type>
                            <variableName>Error_Message</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>accessTokenCreatedTime</parameterName>
                            <type>Output</type>
                            <variableName>OCH_Access_Token_Created_Time</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>refreshToken</parameterName>
                            <type>Output</type>
                            <variableName>OCH_Refresh_Token</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorLogId</parameterName>
                            <type>Output</type>
                            <variableName>Error_Log_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>actionType</parameterName>
                            <type>Output</type>
                            <variableName>Flow_Action_Type</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>hasError</parameterName>
                            <type>Output</type>
                            <variableName>Has_Message_Error</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>f79c1342-1e4a-4e84-a47e-be38916c588f</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>3ab2369e-5745-4a79-b773-a7746255ba7d</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Has_Message_Error</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>true</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botMessages>
                        <message>{!Error_Message}</message>
                        <messageIdentifier>a2971b61-bf1f-4735-990c-3c1fb031a44d</messageIdentifier>
                    </botMessages>
                    <stepIdentifier>bca5770a-19f2-4acc-9a4c-b5a9fd16128c</stepIdentifier>
                    <type>Message</type>
                </botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Balance_case_update</targetBotDialog>
                        </botNavigationLinks>
                        <type>Call</type>
                    </botNavigation>
                    <stepIdentifier>2fc8b663-f210-4ec1-9d6a-7269abb233f9</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Continue_balance_inquiry</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>18860451-c9e2-46b7-87ec-d7ffecc7d71e</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>30111d57-e6e5-4aac-bebc-0e1e7f6d69b4</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>OSB_VA_ViewBalance_CTRL</invocationActionName>
                        <invocationActionType>apex</invocationActionType>
                        <invocationMappings>
                            <parameterName>accessToken</parameterName>
                            <type>Input</type>
                            <variableName>OCH_Access_Token</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>accountNumber</parameterName>
                            <type>Input</type>
                            <variableName>Account_Number</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>contactId</parameterName>
                            <type>Input</type>
                            <variableName>ContactId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>countryName</parameterName>
                            <type>Input</type>
                            <variableName>Country_name</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>actionType</parameterName>
                            <type>Output</type>
                            <variableName>Flow_Action_Type</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorLogId</parameterName>
                            <type>Output</type>
                            <variableName>Error_Log_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorOutputMessage</parameterName>
                            <type>Output</type>
                            <variableName>Error_Message</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorType</parameterName>
                            <type>Output</type>
                            <variableName>Error_Type</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>hasError</parameterName>
                            <type>Output</type>
                            <variableName>Has_Message_Error</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>outputMessage</parameterName>
                            <type>Output</type>
                            <variableName>Balance_Details</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>ad4a4145-c75b-475f-b9b6-6bde9891b22f</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>745b9d1b-fd65-478e-8177-97b70aef4bc4</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Balance_case_update</targetBotDialog>
                        </botNavigationLinks>
                        <type>Call</type>
                    </botNavigation>
                    <stepIdentifier>90857ac2-0952-4f83-b014-287c44f18f7f</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>65130dc1-6059-46de-a1cd-5526aaa39f36</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Error_Message</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>IsSet</operatorType>
                </botStepConditions>
                <botSteps>
                    <botMessages>
                        <message>{!Error_Message}</message>
                        <messageIdentifier>59755dcf-4f50-4772-a626-26c73dae1641</messageIdentifier>
                    </botMessages>
                    <stepIdentifier>a9647089-47b1-4d93-b216-ca66ea40ec78</stepIdentifier>
                    <type>Message</type>
                </botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>View_balance_for_another_account</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>3af10547-55c1-4a0f-bd19-c81319f3c13d</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>0c12bca1-2b54-45a9-9b42-cb614e6aa2f2</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Error_Type</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>HTTP_REQUEST_SEND_ERROR</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Continue_balance_inquiry</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>5042407b-e366-4e8a-bcd1-ad50cc271c6c</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>a3600a44-33e7-4194-9e63-7eadf6cfe275</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Error_Type</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>HTTP_RESPONSE_ERROR</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Continue_balance_inquiry</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>4c4fca35-a6b8-4a7b-b362-813dc6b664f8</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>fdbc6dcd-21c5-47db-abc9-219e40d217b5</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Has_Message_Error</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>true</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>View_balance_for_another_account</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>1624debc-c984-461c-a4d5-7972914a4d52</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>a99f7f94-8c8e-44db-8202-6397a40f0f35</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Error_Message</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>IsNotSet</operatorType>
                </botStepConditions>
                <botSteps>
                    <botMessages>
                        <message>{!Balance_Details}</message>
                        <messageIdentifier>85945f0d-655c-459f-a0db-75993f4bbdea</messageIdentifier>
                    </botMessages>
                    <stepIdentifier>1ad69b50-aebd-4630-8740-29ae2805290a</stepIdentifier>
                    <type>Message</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>2831b0f9-854a-429b-83bd-08215665a0f3</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botNavigation>
                    <botNavigationLinks>
                        <targetBotDialog>View_balance_for_another_account</targetBotDialog>
                    </botNavigationLinks>
                    <type>Redirect</type>
                </botNavigation>
                <stepIdentifier>385eecd3-90c0-4bb9-bfc3-860e7238f753</stepIdentifier>
                <type>Navigation</type>
            </botSteps>
            <developerName>View_balance_for_selected_account</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>View balance for selected account</label>
            <mlIntent>View_balance_for_selected_account</mlIntent>
            <mlIntentTrainingEnabled>false</mlIntentTrainingEnabled>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botDialogGroup>Balance_Viewing</botDialogGroup>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>User_Input</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>********-7c4e-4e50-86ac-b450f81c6f7b</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>33b669e2-eeed-458f-ae3a-3cc753c0346f</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>604a3401-c201-4ae0-81ef-767b8704fd99</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <askCollectIfSet>false</askCollectIfSet>
                    <botMessages>
                        <message>Would you like to view balance for another account?</message>
                        <messageIdentifier>942c3e9a-e8e8-485f-b793-5d5a2e528951</messageIdentifier>
                    </botMessages>
                    <botQuickReplyOptions>
                        <literalValue>Yes</literalValue>
                        <quickReplyOptionIdentifier>767e4361-67b9-40ac-a459-f804659e4dd5</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botQuickReplyOptions>
                        <literalValue>No</literalValue>
                        <quickReplyOptionIdentifier>********-96eb-4390-adc7-226052018cd1</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botVariableOperands>
                        <disableAutoFill>true</disableAutoFill>
                        <sourceName>_Text</sourceName>
                        <sourceType>StandardMlSlotClass</sourceType>
                        <targetName>User_Input</targetName>
                        <targetType>ConversationVariable</targetType>
                    </botVariableOperands>
                    <ignoreIntentRecognition>false</ignoreIntentRecognition>
                    <optionalCollect>false</optionalCollect>
                    <quickReplyType>Static</quickReplyType>
                    <quickReplyWidgetType>Menu</quickReplyWidgetType>
                    <type>Collect</type>
                    <variableOperationIdentifier>26d37f5c-999a-4363-bfd6-55cd165115b4</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>100ad341-51f9-4f72-836f-7b71bce2a9d5</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>Yes</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>View_my_balance</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>9a10d881-a63e-4406-a9d8-47bbd6b302ac</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>bd35007c-6958-4f07-8dd2-2902e63c6849</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>No</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Is_there_anything_else_I_can_help_you_with</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>fd1583cb-0082-41ec-a0bd-6e1584f63847</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>6c775c93-1788-47ea-87f0-67135bcd3202</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>Menu</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Main_Menu_Authenticated</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>e5de5f00-5e55-460f-9d38-81b28bd98374</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>40db961e-b61e-453d-92ac-f6ec34983dc5</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botSteps>
                    <botMessages>
                        <message>I did not understand that</message>
                        <messageIdentifier>56b417ca-02f6-434a-8acb-2fec3380ed99</messageIdentifier>
                    </botMessages>
                    <stepIdentifier>623d0709-1675-4230-9706-ec737c61da47</stepIdentifier>
                    <type>Message</type>
                </botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>View_balance_for_another_account</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>********-463a-42c7-9e05-7b5d82b35ffd</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>84cbf31a-3537-407a-a4eb-3f2c9457ec77</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <stepIdentifier>3dfc20c8-ab86-4681-a166-f549308ddab0</stepIdentifier>
                <type>Wait</type>
            </botSteps>
            <developerName>View_balance_for_another_account</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>View balance for another account</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botDialogGroup>Balance_Viewing</botDialogGroup>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>User_Input</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>90f617a0-c646-43f0-97e7-3a9048498987</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>32a787e8-7e94-4f5f-804a-0b9e03c268c8</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Account_Number</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>1b472ea7-0b61-4862-9741-3c6fa64d5a46</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>4c5a9103-878c-4ff8-bfe4-521e4b7454e4</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>b228871e-66bc-4a6f-8da7-4ee75d27fef2</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <askCollectIfSet>false</askCollectIfSet>
                    <botMessages>
                        <message>Can I have the account number?</message>
                        <messageIdentifier>f50e43a2-e117-492e-af4e-9d9db217481a</messageIdentifier>
                    </botMessages>
                    <botVariableOperands>
                        <disableAutoFill>true</disableAutoFill>
                        <sourceName>_Text</sourceName>
                        <sourceType>StandardMlSlotClass</sourceType>
                        <targetName>Account_Number</targetName>
                        <targetType>ConversationVariable</targetType>
                    </botVariableOperands>
                    <optionalCollect>false</optionalCollect>
                    <quickReplyWidgetType>Buttons</quickReplyWidgetType>
                    <type>Collect</type>
                    <variableOperationIdentifier>70e36daf-e479-4692-9ca7-f828918538f4</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>e9aeee79-f730-40b3-8a94-84a5082b2031</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>OSB_SRV_FinancialAccPermissionHandler</invocationActionName>
                        <invocationActionType>apex</invocationActionType>
                        <invocationMappings>
                            <parameterName>accountNumber</parameterName>
                            <type>Input</type>
                            <variableName>Account_Number</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>contactId</parameterName>
                            <type>Input</type>
                            <variableName>ContactId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>permissionType</parameterName>
                            <type>Input</type>
                            <value>Balance Inquiry</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorMessage</parameterName>
                            <type>Output</type>
                            <variableName>Error_Message</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>hasError</parameterName>
                            <type>Output</type>
                            <variableName>Has_Message_Error</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>02083b0a-8b8a-4fea-9cfb-81d2d0c49eda</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>aae6a32a-5bba-475d-8d0a-e4067d0f426b</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Has_Message_Error</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>false</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>View_balance_for_selected_account</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>9d7503de-e845-4b04-add6-4c39949c4b58</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>********-94c4-4236-b893-b27ada19fcf7</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botMessages>
                    <message>{!Error_message}</message>
                    <messageIdentifier>e6082281-bd62-4f70-8892-17d75fb4edb2</messageIdentifier>
                </botMessages>
                <stepIdentifier>401e2486-53c0-4b9a-8c3d-7ba2e9bc61c0</stepIdentifier>
                <type>Message</type>
            </botSteps>
            <botSteps>
                <botNavigation>
                    <botNavigationLinks>
                        <targetBotDialog>View_balance_for_another_account</targetBotDialog>
                    </botNavigationLinks>
                    <type>Redirect</type>
                </botNavigation>
                <stepIdentifier>df522e80-e1d5-45fe-afa1-0507571088a8</stepIdentifier>
                <type>Navigation</type>
            </botSteps>
            <developerName>Can_I_have_the_account_number</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Can I have the account number?</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botDialogGroup>Balance_Viewing</botDialogGroup>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>User_Input</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>0289522c-e77d-431c-a0ab-509e77db99b8</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>a1c0e0d1-8366-4c91-912a-beddc627ae3f</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>b711ad3a-1286-449e-8307-8b4bb64d0668</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <askCollectIfSet>false</askCollectIfSet>
                    <botMessages>
                        <message>Would you like to continue or try again later?</message>
                        <messageIdentifier>85e25277-3c91-452b-a17e-645f8355848b</messageIdentifier>
                    </botMessages>
                    <botQuickReplyOptions>
                        <literalValue>Continue</literalValue>
                        <quickReplyOptionIdentifier>3e6b8b3f-6215-4965-ae67-140853322870</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botQuickReplyOptions>
                        <literalValue>Try again later</literalValue>
                        <quickReplyOptionIdentifier>63c6084e-8dfb-4319-834d-43e3a9fbb341</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botVariableOperands>
                        <disableAutoFill>true</disableAutoFill>
                        <sourceName>_Text</sourceName>
                        <sourceType>StandardMlSlotClass</sourceType>
                        <targetName>User_Input</targetName>
                        <targetType>ConversationVariable</targetType>
                    </botVariableOperands>
                    <ignoreIntentRecognition>false</ignoreIntentRecognition>
                    <optionalCollect>false</optionalCollect>
                    <quickReplyType>Static</quickReplyType>
                    <quickReplyWidgetType>Menu</quickReplyWidgetType>
                    <type>Collect</type>
                    <variableOperationIdentifier>a0e9ab3e-b914-4f9f-bd74-fd07d7af9f17</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>abf532e6-722a-4b26-81d8-b03d07632cb3</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>Continue</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>View_my_balance</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>42bc9e47-0ea5-467a-8bd2-a054e359e698</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>6c9f737e-f5ff-434f-bd62-e792fb237328</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>Try again later</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Is_there_anything_else_I_can_help_you_with</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>1325c418-97d3-4cc2-a0f2-74b7d18dbfe4</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>aed5fe3e-2b8b-4e29-8b6b-77f3de96d705</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>Menu</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Main_Menu_Authenticated</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>bf0b440b-cd18-4c44-931c-00c973575aab</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>d409582f-20b5-4ba4-8da7-f29b8a5e50b9</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botSteps>
                    <botMessages>
                        <message>I did not understand that</message>
                        <messageIdentifier>62cf2ad4-452e-4adc-8db5-8f551b84a1ce</messageIdentifier>
                    </botMessages>
                    <stepIdentifier>18b1f920-ab0d-434e-b2fa-fc198f792e3c</stepIdentifier>
                    <type>Message</type>
                </botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Continue_balance_inquiry</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>9f72fa16-701f-43ee-92c6-5cbe6354c3e5</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>f9ef16ca-a7de-4abb-9234-91cd3c23fd1a</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <stepIdentifier>8db8b6df-6e0b-4bdc-9edb-5ba287e9d79e</stepIdentifier>
                <type>Wait</type>
            </botSteps>
            <developerName>Continue_balance_inquiry</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Continue balance inquiry?</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botDialogGroup>Balance_Viewing</botDialogGroup>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Selected_Account_Type</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>b4295acd-0c96-4059-8f2d-91e6589610cf</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>2c4c6196-ce65-4702-a089-3e3a0f77e43c</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Has_Message_Error</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>0bf588b2-aa7b-44a6-8131-0d6d8927b474</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>28fdf80a-6b62-427e-a87b-c21ba251457c</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Error_Type</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>c1392f14-9b05-4505-a3a9-8554a9fa0184</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>a7e3a5e3-d2be-4868-b06a-bfa791d01e3e</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Error_Message</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>596d0d18-2cd2-4c09-9cc3-58423b3ed3fc</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>58909132-7846-4415-a4cd-11ef4a3c6018</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Flow_Action_Type</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>fff14366-134a-480e-b15e-203abbece926</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>a7dd3d47-421f-4bbf-aa48-a8c172959b9a</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>acb5afc3-d506-4dd6-abb2-69eb6661906d</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Error_Log_Id</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>445f2ab9-f5cc-48e6-a37d-05f8f4de25cd</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>b7255588-745e-49a1-b4c4-f18ba6f0cda5</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Customer_Identifier</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>2e38ab5c-839c-4181-82f7-d04b0469cd67</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>ccecc9ad-2751-4c7e-b5cd-4d6578d5e353</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Selected_Account</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>0b50ddb2-eb95-450b-bd76-f84476ecbbf6</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>c505fe87-b7bd-4b4a-b4b7-ad63e0fab5f4</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Account_Number</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>********-f098-43be-9d1e-10d05b1acafb</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>f587c67e-4bc8-42a6-a065-b1a9d1431fef</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>a6267c58-1972-4a5a-9fd6-e69c13049ed7</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>OSB_VA_Account_Types</invocationActionName>
                        <invocationActionType>flow</invocationActionType>
                        <invocationMappings>
                            <parameterName>CountryName</parameterName>
                            <type>Input</type>
                            <variableName>Country_name</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ServiceType</parameterName>
                            <type>Input</type>
                            <value>Balance inquiry</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>AccountTypes</parameterName>
                            <type>Output</type>
                            <variableName>Account_Types</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>df42b573-094c-42d6-9e61-477a7a6652eb</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>e546a8c0-14b7-4577-9642-51100f6bad85</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <askCollectIfSet>false</askCollectIfSet>
                    <autoSelectIfSingleChoice>false</autoSelectIfSingleChoice>
                    <botMessages>
                        <message>Choose your account type</message>
                        <messageIdentifier>95375eba-212a-44e0-a148-6762fb54aab6</messageIdentifier>
                    </botMessages>
                    <botVariableOperands>
                        <disableAutoFill>true</disableAutoFill>
                        <sourceName>_Object</sourceName>
                        <sourceType>StandardMlSlotClass</sourceType>
                        <targetName>Selected_Account_Type</targetName>
                        <targetType>ConversationVariable</targetType>
                    </botVariableOperands>
                    <ignoreIntentRecognition>false</ignoreIntentRecognition>
                    <invalidInputBotNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Main_Menu_Authenticated</targetBotDialog>
                        </botNavigationLinks>
                        <type>Call</type>
                    </invalidInputBotNavigation>
                    <optionalCollect>false</optionalCollect>
                    <quickReplyOptionTemplate>{!Label}</quickReplyOptionTemplate>
                    <quickReplyType>Dynamic</quickReplyType>
                    <quickReplyWidgetType>Menu</quickReplyWidgetType>
                    <sourceVariableName>Account_Types</sourceVariableName>
                    <sourceVariableType>ConversationVariable</sourceVariableType>
                    <type>Collect</type>
                    <variableOperationIdentifier>892ee58e-a534-48dd-a3a8-2f7ad609ecaf</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>41f4b270-c354-404d-bf03-715f96df0737</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>OSB_VA_OCHAuthenticate_CTRL</invocationActionName>
                        <invocationActionType>apex</invocationActionType>
                        <invocationMappings>
                            <parameterName>serviceType</parameterName>
                            <type>Input</type>
                            <value>Balance inquiry</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>contactId</parameterName>
                            <type>Input</type>
                            <variableName>ContactId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>countryName</parameterName>
                            <type>Input</type>
                            <variableName>Country_name</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>accessToken</parameterName>
                            <type>Output</type>
                            <variableName>OCH_Access_Token</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorMessage</parameterName>
                            <type>Output</type>
                            <variableName>Error_Message</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>accessTokenCreatedTime</parameterName>
                            <type>Output</type>
                            <variableName>OCH_Access_Token_Created_Time</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>refreshToken</parameterName>
                            <type>Output</type>
                            <variableName>OCH_Refresh_Token</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorLogId</parameterName>
                            <type>Output</type>
                            <variableName>Error_Log_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>actionType</parameterName>
                            <type>Output</type>
                            <variableName>Flow_Action_Type</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>hasError</parameterName>
                            <type>Output</type>
                            <variableName>Has_Message_Error</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>c9ecaff6-501b-454a-b7d7-c8c305d52151</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>6908a944-55e9-400f-a1ce-6675dd64f787</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Has_Message_Error</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>true</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botMessages>
                        <message>{!Error_Message}</message>
                        <messageIdentifier>ce263cdc-b691-4638-947a-f927b4abdc33</messageIdentifier>
                    </botMessages>
                    <stepIdentifier>d78dffdd-d05c-4d85-afc4-e7e27d7e6f61</stepIdentifier>
                    <type>Message</type>
                </botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Balance_case_update</targetBotDialog>
                        </botNavigationLinks>
                        <type>Call</type>
                    </botNavigation>
                    <stepIdentifier>2b132f75-96d0-4e93-9698-1659552abe90</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Continue_balance_inquiry</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>e76cefa5-13ae-4cb5-97e5-5addadb9dab6</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>6c4b290d-5faa-4efe-a0ad-9ba1499e4ee5</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>OSB_VA_CustomerIdentifier_CTRL</invocationActionName>
                        <invocationActionType>apex</invocationActionType>
                        <invocationMappings>
                            <parameterName>countryName</parameterName>
                            <type>Input</type>
                            <variableName>Country_name</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>clientName</parameterName>
                            <type>Input</type>
                            <variableName>Client_Name</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>serviceType</parameterName>
                            <type>Input</type>
                            <value>Balance inquiry</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>accessToken</parameterName>
                            <type>Input</type>
                            <variableName>OCH_Access_Token</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>contactId</parameterName>
                            <type>Input</type>
                            <variableName>ContactId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>hasError</parameterName>
                            <type>Output</type>
                            <variableName>Has_Message_Error</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>actionType</parameterName>
                            <type>Output</type>
                            <variableName>Flow_Action_Type</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>customerIdentifier</parameterName>
                            <type>Output</type>
                            <variableName>Customer_Identifier</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorMessage</parameterName>
                            <type>Output</type>
                            <variableName>Error_Message</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorLogId</parameterName>
                            <type>Output</type>
                            <variableName>Error_Log_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorType</parameterName>
                            <type>Output</type>
                            <variableName>Error_Type</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>de035fde-4806-4c7d-af06-df58d15f8f4c</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>73f410a5-4fc7-40f3-a38f-89c8a9cb40a2</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Error_Message</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>IsSet</operatorType>
                </botStepConditions>
                <botSteps>
                    <botMessages>
                        <message>{!Error_Message}</message>
                        <messageIdentifier>73cc8ff4-c35f-4a3b-b33e-8e78829cb557</messageIdentifier>
                    </botMessages>
                    <stepIdentifier>67f6c301-cdff-42b4-8ce8-cc92615bb62e</stepIdentifier>
                    <type>Message</type>
                </botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Balance_case_update</targetBotDialog>
                        </botNavigationLinks>
                        <type>Call</type>
                    </botNavigation>
                    <stepIdentifier>cdd56d7a-5439-49d5-a995-07512b90d8b7</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>3343473c-cb48-44d1-ac4b-4f67161c0f4a</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Error_Type</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>HTTP_REQUEST_SEND_ERROR</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Continue_balance_inquiry</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>68815e96-6014-44bd-9f14-c85fd9adebca</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>8922d227-ff0f-4171-b69f-09b8872fe6b7</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Error_Type</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>HTTP_RESPONSE_ERROR</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Continue_balance_inquiry</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>a8796bcb-9e98-4869-ad92-35c98305b3b9</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>c114344f-62df-4122-b750-eef4c2ad88d1</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Error_Type</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>NO_ACCOUNT_FOUND_ERROR</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>View_balance_for_another_account</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>4eae9167-88f7-437f-96bf-8d88ffb869ea</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>9de63bbf-e192-4aff-bc39-58a36e9feb32</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>OSB_VA_AccountSearch_CTRL</invocationActionName>
                        <invocationActionType>apex</invocationActionType>
                        <invocationMappings>
                            <parameterName>accessToken</parameterName>
                            <type>Input</type>
                            <variableName>OCH_Access_Token</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>contactId</parameterName>
                            <type>Input</type>
                            <variableName>ContactId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>customerIdentifier</parameterName>
                            <type>Input</type>
                            <variableName>Customer_Identifier</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>accountType</parameterName>
                            <type>Input</type>
                            <variableName>Selected_Account_Type</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>serviceType</parameterName>
                            <type>Input</type>
                            <value>Balance inquiry</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>countryName</parameterName>
                            <type>Input</type>
                            <variableName>Country_name</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>actionType</parameterName>
                            <type>Output</type>
                            <variableName>Flow_Action_Type</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorType</parameterName>
                            <type>Output</type>
                            <variableName>Error_Type</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorOutPutMessage</parameterName>
                            <type>Output</type>
                            <variableName>Error_Message</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>accounts</parameterName>
                            <type>Output</type>
                            <variableName>Accounts_List</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>hasError</parameterName>
                            <type>Output</type>
                            <variableName>Has_Message_Error</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorLogId</parameterName>
                            <type>Output</type>
                            <variableName>Error_Log_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>53bc36e3-0884-46ba-b3bc-a73078a29010</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>bf87b281-c559-45b6-9c3f-b579fb20c0a7</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Error_Message</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>IsSet</operatorType>
                </botStepConditions>
                <botSteps>
                    <botMessages>
                        <message>{!Error_Message}</message>
                        <messageIdentifier>4ea7d175-f9fa-4ec3-b8ec-214d846cd151</messageIdentifier>
                    </botMessages>
                    <stepIdentifier>ba3a2255-84e0-474e-9a7e-944a9c44048e</stepIdentifier>
                    <type>Message</type>
                </botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Balance_case_update</targetBotDialog>
                        </botNavigationLinks>
                        <type>Call</type>
                    </botNavigation>
                    <stepIdentifier>c3456420-7efa-45fc-a2a7-ad2524925cf9</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>e9597d05-435c-4934-86a6-d4b86b48fa21</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Error_Type</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>HTTP_REQUEST_SEND_ERROR</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Continue_balance_inquiry</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>556f0e5e-864b-47ca-bed8-8bad4e036444</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>aa2cab05-192e-489d-a6f9-66b04357e0e4</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Error_Type</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>HTTP_RESPONSE_ERROR</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Continue_balance_inquiry</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>d6c74417-6525-4b15-9b62-c02ee14a95c3</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>90cdaaf0-95ef-4116-89a3-85c5be4dde94</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Error_Type</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>NO_ACCOUNT_FOUND_ERROR</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>View_balance_for_another_account</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>f8fcdf69-b6d6-452a-8fd8-caa5ab471c80</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>e7919b39-a936-4510-b60d-b32889bae315</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Error_Message</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>IsSet</operatorType>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Continue_balance_inquiry</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>1988dc8a-b9fb-4cdb-90bc-455d3827c80d</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>2c22ee29-46af-424d-a738-6e17be8d8941</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <askCollectIfSet>false</askCollectIfSet>
                    <autoSelectIfSingleChoice>false</autoSelectIfSingleChoice>
                    <botMessages>
                        <message>The account numbers that I have found with your inputs are</message>
                        <messageIdentifier>8f686e02-3108-46f2-86ee-731150e1fe7d</messageIdentifier>
                    </botMessages>
                    <botVariableOperands>
                        <disableAutoFill>true</disableAutoFill>
                        <sourceName>_Object</sourceName>
                        <sourceType>StandardMlSlotClass</sourceType>
                        <targetName>Selected_Account</targetName>
                        <targetType>ConversationVariable</targetType>
                    </botVariableOperands>
                    <ignoreIntentRecognition>false</ignoreIntentRecognition>
                    <invalidInputBotNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Main_Menu_Authenticated</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </invalidInputBotNavigation>
                    <optionalCollect>false</optionalCollect>
                    <quickReplyOptionTemplate>{!outputText}</quickReplyOptionTemplate>
                    <quickReplyType>Dynamic</quickReplyType>
                    <quickReplyWidgetType>Menu</quickReplyWidgetType>
                    <sourceVariableName>Accounts_List</sourceVariableName>
                    <sourceVariableType>ConversationVariable</sourceVariableType>
                    <type>Collect</type>
                    <variableOperationIdentifier>39037a47-d5c0-4d00-9b2b-46dec9be93b3</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>aad23bd7-50ba-44a7-a0b9-82c8de0e8059</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>OSB_VA_Get_Account_Number</invocationActionName>
                        <invocationActionType>flow</invocationActionType>
                        <invocationMappings>
                            <parameterName>AccountInfo</parameterName>
                            <type>Input</type>
                            <variableName>Selected_Account</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>AccountNumber</parameterName>
                            <type>Output</type>
                            <variableName>Account_Number</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>e3d57b49-d466-4979-891f-fe1ff97cdaef</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>ba4bbc21-64fa-447f-9935-91197aa0d9bc</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Account_Number</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>IsNotSet</operatorType>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Get_Clients</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>6fe8970a-906f-4073-a1ac-68422f41fc03</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>bde50bf6-f9d4-4bfd-8412-06fe06413538</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botNavigation>
                    <botNavigationLinks>
                        <targetBotDialog>View_balance_for_selected_account</targetBotDialog>
                    </botNavigationLinks>
                    <type>Redirect</type>
                </botNavigation>
                <stepIdentifier>39c66bd1-f7b8-4777-a610-ed41265698e2</stepIdentifier>
                <type>Navigation</type>
            </botSteps>
            <developerName>Search_for_the_account</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Search for the account</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botDialogGroup>Balance_Viewing</botDialogGroup>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Client_Name</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>17b29e48-82df-45ef-a05b-1c9818e57822</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>********-b97b-4d75-8190-84b91225ece7</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>376c8555-c280-47e6-bd36-6b5659b8d756</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>OSB_VA_AvailableClients</invocationActionName>
                        <invocationActionType>apex</invocationActionType>
                        <invocationMappings>
                            <parameterName>countryName</parameterName>
                            <type>Input</type>
                            <variableName>Country_name</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>contactId</parameterName>
                            <type>Input</type>
                            <variableName>ContactId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>serviceType</parameterName>
                            <type>Input</type>
                            <value>Balance inquiry</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>output</parameterName>
                            <type>Output</type>
                            <variableName>Client_Names</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>aefdfb3c-d9b9-43b2-958d-b0115db377a4</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>8afc0c1c-ed29-4921-94d0-4423210134b7</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <askCollectIfSet>false</askCollectIfSet>
                    <autoSelectIfSingleChoice>false</autoSelectIfSingleChoice>
                    <botMessages>
                        <message>Choose your company name</message>
                        <messageIdentifier>f5f95058-5cd5-471a-a17f-a80d49013d07</messageIdentifier>
                    </botMessages>
                    <botVariableOperands>
                        <disableAutoFill>true</disableAutoFill>
                        <sourceName>_Text</sourceName>
                        <sourceType>StandardMlSlotClass</sourceType>
                        <targetName>Client_Name</targetName>
                        <targetType>ConversationVariable</targetType>
                    </botVariableOperands>
                    <ignoreIntentRecognition>false</ignoreIntentRecognition>
                    <invalidInputBotNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Main_Menu_Authenticated</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </invalidInputBotNavigation>
                    <optionalCollect>false</optionalCollect>
                    <quickReplyType>Dynamic</quickReplyType>
                    <quickReplyWidgetType>Menu</quickReplyWidgetType>
                    <sourceVariableName>Client_Names</sourceVariableName>
                    <sourceVariableType>ConversationVariable</sourceVariableType>
                    <type>Collect</type>
                    <variableOperationIdentifier>1fe690ec-5351-4834-8421-2d330b935223</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>29ddfa89-3a8e-4766-aa6e-44436c1aa2f8</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botNavigation>
                    <botNavigationLinks>
                        <targetBotDialog>Search_for_the_account</targetBotDialog>
                    </botNavigationLinks>
                    <type>Redirect</type>
                </botNavigation>
                <stepIdentifier>5bf95f23-7fa2-4d14-8b38-763cd77cb3ee</stepIdentifier>
                <type>Navigation</type>
            </botSteps>
            <developerName>Get_Clients</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Get Clients</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botDialogGroup>Balance_Viewing</botDialogGroup>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>Create_Case_From_Chat_Bot</invocationActionName>
                        <invocationActionType>flow</invocationActionType>
                        <invocationMappings>
                            <parameterName>ChatTranscriptId</parameterName>
                            <type>Input</type>
                            <variableName>RoutableId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ErrorLogId</parameterName>
                            <type>Input</type>
                            <variableName>Error_Log_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ServiceType</parameterName>
                            <type>Input</type>
                            <value>Balance inquiry</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>CountryName</parameterName>
                            <type>Input</type>
                            <variableName>Country_name</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>CaseId</parameterName>
                            <type>Input</type>
                            <variableName>Case_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ContactId</parameterName>
                            <type>Input</type>
                            <variableName>ContactId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ActionType</parameterName>
                            <type>Input</type>
                            <variableName>Flow_Action_Type</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>CaseId</parameterName>
                            <type>Output</type>
                            <variableName>Case_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>2a8156e0-5dd9-40bf-9140-d663cbe79735</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>fc732b50-0d62-4088-a8ee-366667c76d8c</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Flow_Action_Type</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>f241e5c7-a62e-4a61-9377-cd734ebee57e</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>5602bede-1195-4784-be12-fda1d72f13ea</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Error_Log_Id</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>729b906f-b68f-459d-a7db-19e58ee0013f</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>e3d0223a-32a8-462b-a729-10b9b8acdeca</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Case_Id</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>39a96d4c-846b-4a70-92d6-12cf6cb3ca07</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>be969738-d455-4834-a817-40002ca96327</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>11974c3b-15ba-4438-81ec-4ba3f978fa53</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <stepIdentifier>c673e41f-131e-4fcd-bcb4-8e97a36fb1c3</stepIdentifier>
                <type>Wait</type>
            </botSteps>
            <developerName>Balance_case_update</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Balance case update</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botDialogGroup>Email_statement</botDialogGroup>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Account_Number</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>b668da83-8dcb-46f4-88df-aff764e1b17f</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>5e4e8614-52ad-4876-bdfb-85ca6cddb4e2</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Case_Id</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>8cfd9e2a-4a3d-497e-9f56-68770d632192</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>ae19cc33-96ce-4715-ba60-d69808aed9b6</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>17a52b0c-d2df-4695-a4a6-53a9d5d13226</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Error_Log_Id</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>32967f39-74d8-4c76-8b7c-5edabcb37399</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>72ab729d-65c2-4d30-9b16-dbd31a50219b</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>User_Input</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>6abdf747-6b0a-4985-bb57-b2eee384eeb6</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>ef7d10e9-09e7-4d49-a7e7-f53d03fe62de</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>a25d04f3-da73-455d-8a86-d6c91c5e4df4</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>OSB_SRV_FinancialAccPermissionHandler</invocationActionName>
                        <invocationActionType>apex</invocationActionType>
                        <invocationMappings>
                            <parameterName>accountNumber</parameterName>
                            <type>Input</type>
                            <variableName>Account_Number</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>contactId</parameterName>
                            <type>Input</type>
                            <variableName>ContactId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>permissionType</parameterName>
                            <type>Input</type>
                            <value>Email statement</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorMessage</parameterName>
                            <type>Output</type>
                            <variableName>Error_Message</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>hasError</parameterName>
                            <type>Output</type>
                            <variableName>Has_Message_Error</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>fe77df05-91dc-4253-9238-8bd4e11b901b</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>ad484b14-f7e5-4508-b83c-bfbec7723948</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Has_Message_Error</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>true</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>No_service_access</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>dba53a61-cef3-4205-8b34-870e3d999a44</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>b177a440-eb88-4acf-9315-84ddf8347fbf</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>Create_Case_From_Chat_Bot</invocationActionName>
                        <invocationActionType>flow</invocationActionType>
                        <invocationMappings>
                            <parameterName>ActionType</parameterName>
                            <type>Input</type>
                            <value>Create New Case</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>CaseId</parameterName>
                            <type>Input</type>
                            <variableName>Case_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ErrorLogId</parameterName>
                            <type>Input</type>
                            <variableName>Error_Log_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>CountryName</parameterName>
                            <type>Input</type>
                            <variableName>Country_name</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ServiceType</parameterName>
                            <type>Input</type>
                            <value>Email statement</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ChatTranscriptId</parameterName>
                            <type>Input</type>
                            <variableName>RoutableId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ContactId</parameterName>
                            <type>Input</type>
                            <variableName>ContactId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>CaseId</parameterName>
                            <type>Output</type>
                            <variableName>Case_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>a0a64b1a-7467-404c-8627-e98eefe9c33d</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>0a26abcc-c926-40b3-ab65-b641110f2188</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <askCollectIfSet>false</askCollectIfSet>
                    <botMessages>
                        <message>Do you know the account number?</message>
                        <messageIdentifier>9af97030-c727-43fd-9828-e78c15958c42</messageIdentifier>
                    </botMessages>
                    <botQuickReplyOptions>
                        <literalValue>Yes</literalValue>
                        <quickReplyOptionIdentifier>cdbcfa84-2a02-4034-9158-f0309a2b9bd5</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botQuickReplyOptions>
                        <literalValue>No</literalValue>
                        <quickReplyOptionIdentifier>f944e73f-689f-4065-b1b6-f57a92622d70</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botVariableOperands>
                        <disableAutoFill>true</disableAutoFill>
                        <sourceName>_Text</sourceName>
                        <sourceType>StandardMlSlotClass</sourceType>
                        <targetName>User_Input</targetName>
                        <targetType>ConversationVariable</targetType>
                    </botVariableOperands>
                    <ignoreIntentRecognition>false</ignoreIntentRecognition>
                    <optionalCollect>false</optionalCollect>
                    <quickReplyType>Static</quickReplyType>
                    <quickReplyWidgetType>Menu</quickReplyWidgetType>
                    <type>Collect</type>
                    <variableOperationIdentifier>f2f090fd-93ee-44bc-a313-4beb4618a817</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>e866c008-1334-4bff-b431-bf476284c9df</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>Yes</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Statement_Can_I_have_account_number</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>954af96c-9a0e-49d3-b17b-23589089314d</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>7fb92219-23c2-4d15-9a01-a77bb535e6bf</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>No</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Statement_get_clients</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>1a677831-7396-4063-aaed-6772f8e9ab46</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>9beb221d-2e4f-4fa1-9718-1d7e6d7e2474</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>Menu</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Main_Menu_Authenticated</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>ce3980ac-f53b-45a1-9fbb-d36a263cc974</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>2aa335b0-7226-4462-8ea0-b8ff953f689d</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botSteps>
                    <botMessages>
                        <message>I did not understand that</message>
                        <messageIdentifier>7727b2c0-4f85-416f-95a5-bcac0c87f826</messageIdentifier>
                    </botMessages>
                    <stepIdentifier>4b93bb5b-c91a-4b56-bae4-4b40719491c5</stepIdentifier>
                    <type>Message</type>
                </botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Email_my_statement</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>9fe07851-5218-42f1-a938-4b02539bab7c</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>5b1432fc-2873-4123-b037-fee24841d71a</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <stepIdentifier>53304781-f0a4-4d20-b6b8-d3b303b29d98</stepIdentifier>
                <type>Wait</type>
            </botSteps>
            <description>CDCS-184 Initial dialog for generating statement</description>
            <developerName>Email_my_statement</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Email my statement</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botDialogGroup>Email_statement</botDialogGroup>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>User_Input</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>0b215d0e-6e5a-4e0b-84da-3739a5ccee4b</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>695b08c2-57c2-43d6-98e7-bb1e3317fde6</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Account_Number</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>2a8010d0-b359-41b0-ac82-33d968d97f71</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>06f4c21a-5882-43ff-a31c-360fda375bb9</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>2badc2b6-eb16-4da0-b9af-e48593c838fa</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <askCollectIfSet>false</askCollectIfSet>
                    <botMessages>
                        <message>Can I have the account number?</message>
                        <messageIdentifier>07927a86-59b5-48c6-839a-ef5b8ce5d10c</messageIdentifier>
                    </botMessages>
                    <botVariableOperands>
                        <disableAutoFill>true</disableAutoFill>
                        <sourceName>_Text</sourceName>
                        <sourceType>StandardMlSlotClass</sourceType>
                        <targetName>Account_Number</targetName>
                        <targetType>ConversationVariable</targetType>
                    </botVariableOperands>
                    <optionalCollect>false</optionalCollect>
                    <quickReplyWidgetType>Buttons</quickReplyWidgetType>
                    <type>Collect</type>
                    <variableOperationIdentifier>d54e04b4-59b2-448d-ac81-af1916cd2af6</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>619b71dd-5cb8-4af4-924e-1263d2b275f4</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>OSB_SRV_FinancialAccPermissionHandler</invocationActionName>
                        <invocationActionType>apex</invocationActionType>
                        <invocationMappings>
                            <parameterName>accountNumber</parameterName>
                            <type>Input</type>
                            <variableName>Account_Number</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>contactId</parameterName>
                            <type>Input</type>
                            <variableName>ContactId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>permissionType</parameterName>
                            <type>Input</type>
                            <value>Email statement</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorMessage</parameterName>
                            <type>Output</type>
                            <variableName>Error_Message</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>hasError</parameterName>
                            <type>Output</type>
                            <variableName>Has_Message_Error</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>7c3ca1e5-eb28-4bc6-8898-f6e8261912d2</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>68350f2e-04d9-4d98-8d90-6b2d27814c5c</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Has_Message_Error</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>false</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Select_time_period</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>c2b3d427-d5fa-4ba2-84e0-e9a8553fe089</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>17aa9dac-c97d-4cd6-a80d-bd91c1520a93</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botMessages>
                    <message>{!Error_Message}</message>
                    <messageIdentifier>e12a6d31-d88e-4b88-8418-6b3f8bddf864</messageIdentifier>
                </botMessages>
                <stepIdentifier>a7d2f228-9e68-4211-82d2-c7052683e533</stepIdentifier>
                <type>Message</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>Create_Case_From_Chat_Bot</invocationActionName>
                        <invocationActionType>flow</invocationActionType>
                        <invocationMappings>
                            <parameterName>ActionType</parameterName>
                            <type>Input</type>
                            <value>Close No Permission</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>CaseId</parameterName>
                            <type>Input</type>
                            <variableName>Case_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ErrorLogId</parameterName>
                            <type>Input</type>
                            <variableName>Error_Log_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>CountryName</parameterName>
                            <type>Input</type>
                            <variableName>Country_name</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ServiceType</parameterName>
                            <type>Input</type>
                            <value>Email statement</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ContactId</parameterName>
                            <type>Input</type>
                            <variableName>ContactId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>CaseId</parameterName>
                            <type>Output</type>
                            <variableName>Case_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>c542392a-baa7-4d7c-b7eb-9dd956207211</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>5504a00f-11df-419d-b5c1-82b953246347</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Case_Id</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>20f06863-dece-4894-a3a6-941ae7ef7f8f</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>1bf622af-0d71-466c-a427-d633e7c7d4db</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Error_Log_Id</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>6163caa1-ea87-4bd8-b02f-430457b9fa46</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>7b50ccfc-5749-4ddc-acd0-b08631fa8520</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Account_Number</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>188d7385-5387-4bba-81ba-fa8744244efe</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>693620d5-cc14-493c-8455-b1915421e585</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>c8898598-c51b-4a50-a737-977ea089fc66</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botNavigation>
                    <botNavigationLinks>
                        <targetBotDialog>Email_another_statement</targetBotDialog>
                    </botNavigationLinks>
                    <type>Redirect</type>
                </botNavigation>
                <stepIdentifier>81defd53-1777-492e-829f-2a545992a847</stepIdentifier>
                <type>Navigation</type>
            </botSteps>
            <description>CDCS-184  Dialog for user to input the number of the account to generate statement for</description>
            <developerName>Statement_Can_I_have_account_number</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Statement - Can I have account number</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botDialogGroup>Email_statement</botDialogGroup>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Statement_Time_Period</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>634b7d3f-8980-437b-b182-1f6ab7946707</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>9227d7e4-c77c-46e2-b099-4e5d48a7a40a</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Statement_From_Date</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>510d47fe-84b0-42cd-8202-6ac717ce2f1b</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>dabd1528-af18-4d31-b8be-cc61e7ca5b3b</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Statement_To_Date</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>4506c828-d56d-41af-b1f2-f4b707ca5570</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>c6d7c10b-8fe3-4a82-855d-1dbc85e0f8ca</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>1472abef-3939-4bb1-a30a-b1a3c846eea9</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <askCollectIfSet>false</askCollectIfSet>
                    <botMessages>
                        <message>Can you tell me what date range would you like the statement for?</message>
                        <messageIdentifier>f796f0fe-6739-4d78-83b2-91276095b06b</messageIdentifier>
                    </botMessages>
                    <botQuickReplyOptions>
                        <literalValue>Last 7 days</literalValue>
                        <quickReplyOptionIdentifier>e7907cef-0523-46d0-82b9-5f0486201dc2</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botQuickReplyOptions>
                        <literalValue>Last 30 days</literalValue>
                        <quickReplyOptionIdentifier>98c9b146-5ce2-4ca1-8b03-107505e4e83c</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botQuickReplyOptions>
                        <literalValue>I would like to choose my own date range</literalValue>
                        <quickReplyOptionIdentifier>7de834d4-c669-4fca-abdf-1dc8b69a4354</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botVariableOperands>
                        <disableAutoFill>true</disableAutoFill>
                        <sourceName>_Text</sourceName>
                        <sourceType>StandardMlSlotClass</sourceType>
                        <targetName>Statement_Time_Period</targetName>
                        <targetType>ConversationVariable</targetType>
                    </botVariableOperands>
                    <ignoreIntentRecognition>false</ignoreIntentRecognition>
                    <optionalCollect>false</optionalCollect>
                    <quickReplyType>Static</quickReplyType>
                    <quickReplyWidgetType>Menu</quickReplyWidgetType>
                    <type>Collect</type>
                    <variableOperationIdentifier>3be68065-f526-4dbf-b3bf-8feceb61a19a</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>1bdffec8-62ad-4661-a376-4c3fcf94f19a</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Statement_Time_Period</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>I would like to choose my own date range</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Select_custom_time_period</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>ee354941-84c5-4897-828b-502fd423be49</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>012ae788-b52a-4ab6-a7b8-bd341149b61a</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botNavigation>
                    <botNavigationLinks>
                        <targetBotDialog>Inquiry_email_statement</targetBotDialog>
                    </botNavigationLinks>
                    <type>Redirect</type>
                </botNavigation>
                <stepIdentifier>748e6975-5ea4-4017-86ab-2b1e948745ec</stepIdentifier>
                <type>Navigation</type>
            </botSteps>
            <description>CDCS-184 Dialog for selecting time period of a statement</description>
            <developerName>Select_time_period</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Select time period</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botDialogGroup>Email_statement</botDialogGroup>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Statement_From_Date</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>6846defa-1498-4ad7-bab8-462491ecd34c</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>98e50ae4-dd28-4af7-8979-c3e16c298708</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Statement_To_Date</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>23174b53-df21-4c4d-b57f-29a4cfb6460c</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>8308be80-fd6d-46f6-8842-bb60a73b5240</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Statement_Time_Period</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>089c878d-58bc-4e78-8a30-9415519ed52f</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>e9ea155f-8cc6-4d9d-bb13-629a16364ed2</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>f1bfe7d7-b819-47d9-9b87-2bba850315c0</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <askCollectIfSet>false</askCollectIfSet>
                    <botMessages>
                        <message>Please provide me with a from date (yyyy-mm-dd)</message>
                        <messageIdentifier>8e9740d0-783f-4994-9ddb-1b3be342ce9e</messageIdentifier>
                    </botMessages>
                    <botVariableOperands>
                        <disableAutoFill>true</disableAutoFill>
                        <sourceName>_Date</sourceName>
                        <sourceType>StandardMlSlotClass</sourceType>
                        <targetName>Statement_From_Date</targetName>
                        <targetType>ConversationVariable</targetType>
                    </botVariableOperands>
                    <optionalCollect>false</optionalCollect>
                    <quickReplyWidgetType>Buttons</quickReplyWidgetType>
                    <retryMessages>
                        <message>Date is invalid, please provide date in format yyyy-mm-dd</message>
                        <messageIdentifier>dc7a4dc7-6d42-48c9-8427-725de6ffedb9</messageIdentifier>
                    </retryMessages>
                    <type>Collect</type>
                    <variableOperationIdentifier>1c733de8-f516-4f6b-86b1-a4cc7f2d1236</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>6d437d3f-1ac2-4cf9-97ec-56ce01008fd1</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <askCollectIfSet>false</askCollectIfSet>
                    <botMessages>
                        <message>Please provide me with a to date (yyyy-mm-dd)</message>
                        <messageIdentifier>dfdf5e25-87dd-4b01-ac3f-0e74d5bedd4b</messageIdentifier>
                    </botMessages>
                    <botVariableOperands>
                        <disableAutoFill>true</disableAutoFill>
                        <sourceName>_Date</sourceName>
                        <sourceType>StandardMlSlotClass</sourceType>
                        <targetName>Statement_To_Date</targetName>
                        <targetType>ConversationVariable</targetType>
                    </botVariableOperands>
                    <optionalCollect>false</optionalCollect>
                    <quickReplyWidgetType>Buttons</quickReplyWidgetType>
                    <retryMessages>
                        <message>Date is invalid, please provide date in format yyyy-mm-dd</message>
                        <messageIdentifier>dcb362fb-f098-45c6-a03f-c5a385512a88</messageIdentifier>
                    </retryMessages>
                    <type>Collect</type>
                    <variableOperationIdentifier>f401e1a5-5918-4d75-a03c-c13be3bc958e</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>9260457e-2faf-4d53-801e-ca2fc2ce6d02</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>OSB_VA_StatementPeriodValidation_CTRL</invocationActionName>
                        <invocationActionType>apex</invocationActionType>
                        <invocationMappings>
                            <parameterName>toDate</parameterName>
                            <type>Input</type>
                            <variableName>Statement_To_Date</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>fromDate</parameterName>
                            <type>Input</type>
                            <variableName>Statement_From_Date</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>isValid</parameterName>
                            <type>Output</type>
                            <variableName>Are_Dates_Valid</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>message</parameterName>
                            <type>Output</type>
                            <variableName>Dates_Validation_Message</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>79c3f95f-4df3-494f-8563-f1caa1abaf6e</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>27ffccd6-daa0-4c98-a3ed-53834ff82a74</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Are_Dates_Valid</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>false</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botMessages>
                        <message>{!Dates_Validation_Message}</message>
                        <messageIdentifier>dc93f76f-f080-4e63-be97-971b277bff55</messageIdentifier>
                    </botMessages>
                    <stepIdentifier>5ead3a40-787c-4bee-89a3-2437007cfc51</stepIdentifier>
                    <type>Message</type>
                </botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Select_custom_time_period</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>2f0dadb2-62e6-4d23-b1cf-3ea201441630</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>322e12f2-09c6-4e3e-8fc0-11440abb8b34</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botNavigation>
                    <botNavigationLinks>
                        <targetBotDialog>Inquiry_email_statement</targetBotDialog>
                    </botNavigationLinks>
                    <type>Redirect</type>
                </botNavigation>
                <stepIdentifier>10cb3a2d-6f2d-4d75-a377-25a3c1c13182</stepIdentifier>
                <type>Navigation</type>
            </botSteps>
            <description>CDCS-184 Dialog for selecting custom time period of a statement</description>
            <developerName>Select_custom_time_period</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Select custom time period</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botDialogGroup>Email_statement</botDialogGroup>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>User_Input</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>ea2dd41b-694e-4f50-a2dd-1704860f922f</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>0e0fc9c4-4ff1-4a5d-901f-b248b040b3e1</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>8ae439e7-860c-4a34-861a-8cbeb779ab9d</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <askCollectIfSet>false</askCollectIfSet>
                    <botMessages>
                        <message>Would you like to request another statement to be emailed?</message>
                        <messageIdentifier>b53ff05d-3f31-456e-95c8-80cf5ffb5d5a</messageIdentifier>
                    </botMessages>
                    <botQuickReplyOptions>
                        <literalValue>Yes</literalValue>
                        <quickReplyOptionIdentifier>24845199-c99d-4ddf-a74f-25708b2cde32</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botQuickReplyOptions>
                        <literalValue>No</literalValue>
                        <quickReplyOptionIdentifier>4dfadc6a-7646-4fc7-a918-4722f81dfee4</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botVariableOperands>
                        <disableAutoFill>true</disableAutoFill>
                        <sourceName>_Text</sourceName>
                        <sourceType>StandardMlSlotClass</sourceType>
                        <targetName>User_Input</targetName>
                        <targetType>ConversationVariable</targetType>
                    </botVariableOperands>
                    <ignoreIntentRecognition>false</ignoreIntentRecognition>
                    <optionalCollect>false</optionalCollect>
                    <quickReplyType>Static</quickReplyType>
                    <quickReplyWidgetType>Menu</quickReplyWidgetType>
                    <type>Collect</type>
                    <variableOperationIdentifier>8f4295ec-c775-4215-bd9c-7755f3912e40</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>7883cfe4-158f-4ab4-89c9-79513fcc9cf7</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>Yes</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Email_my_statement</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>5ba76750-4714-4790-9ae6-15be77c6236d</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>759a97e9-1bc6-46db-b13b-bfceecfbbbfc</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>No</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Is_there_anything_else_I_can_help_you_with</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>612ff17c-9876-41f0-9a59-77893c8d4f8f</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>c6543501-041c-4e6d-b601-834292a74180</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>Menu</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Main_Menu_Authenticated</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>1996b531-8dff-4d41-9007-30741d0a4dfc</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>f4222d4f-ede6-4685-b230-ccecfc576fac</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botSteps>
                    <botMessages>
                        <message>I did not understand that</message>
                        <messageIdentifier>843544cc-9e92-4365-89cf-e462bfb7b551</messageIdentifier>
                    </botMessages>
                    <stepIdentifier>59ad2bff-fbc0-4489-8e91-0729c954b941</stepIdentifier>
                    <type>Message</type>
                </botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Email_another_statement</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>f45ba93a-a997-4774-bb1a-7aee99012759</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>c2b297cd-0adb-43b4-8c4d-8ecbb9f76fc6</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <stepIdentifier>d4bfca20-0e67-4591-aa3d-f26ebfecfabb</stepIdentifier>
                <type>Wait</type>
            </botSteps>
            <description>CDCS-184 User has the opportunity to generate another statement</description>
            <developerName>Email_another_statement</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Email another statement</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botDialogGroup>Email_statement</botDialogGroup>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>User_Input</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>5b2754d8-038c-4a10-a3ef-a4c14db4c7f5</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>bb880ff7-14c0-4ce1-b590-0eacf3eb9cb3</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>8fac5d60-2851-4167-9ec6-0aa334433907</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <askCollectIfSet>false</askCollectIfSet>
                    <botMessages>
                        <message>Would you like to continue or try again later?</message>
                        <messageIdentifier>976cbb36-848e-4c85-a032-6636259242d8</messageIdentifier>
                    </botMessages>
                    <botQuickReplyOptions>
                        <literalValue>Continue</literalValue>
                        <quickReplyOptionIdentifier>6c4397f9-ecf1-4687-9c76-3542be87b69e</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botQuickReplyOptions>
                        <literalValue>Try again later</literalValue>
                        <quickReplyOptionIdentifier>0e0d88af-123a-4399-b12f-f7df17312343</quickReplyOptionIdentifier>
                    </botQuickReplyOptions>
                    <botVariableOperands>
                        <disableAutoFill>true</disableAutoFill>
                        <sourceName>_Text</sourceName>
                        <sourceType>StandardMlSlotClass</sourceType>
                        <targetName>User_Input</targetName>
                        <targetType>ConversationVariable</targetType>
                    </botVariableOperands>
                    <ignoreIntentRecognition>false</ignoreIntentRecognition>
                    <optionalCollect>false</optionalCollect>
                    <quickReplyType>Static</quickReplyType>
                    <quickReplyWidgetType>Menu</quickReplyWidgetType>
                    <type>Collect</type>
                    <variableOperationIdentifier>4e2c02d2-d0b2-4df7-8634-76b76907b674</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>0621f461-54c7-48a1-92d2-1bbe9ec45ede</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>Continue</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Email_my_statement</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>6473a72a-7802-481c-a385-d9187e3d9518</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>16710319-d71f-4415-8937-82fe1bf2dbe0</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>Try again later</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Is_there_anything_else_I_can_help_you_with</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>0c2b5ddb-a26b-4a2d-a7bd-c177762f53eb</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>9be375c6-75dc-4a8f-b0ae-01edb9ed847f</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>User_Input</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>Menu</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Main_Menu_Authenticated</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>8b882b55-a122-47e2-9738-4b27f283365b</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>1fea26e4-6fe9-4754-9ab3-5ec20cc4ec3a</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botMessages>
                    <message>Did understand that</message>
                    <messageIdentifier>51864b53-51cf-43f6-a956-e7ce3b2db87f</messageIdentifier>
                </botMessages>
                <stepIdentifier>382388b4-890a-4591-8526-c0825709a303</stepIdentifier>
                <type>Message</type>
            </botSteps>
            <botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Continue_emailing_statement</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>93ec8f79-6fdc-41ce-aa05-90391b6119b7</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>3cd3660b-cd84-485f-88f5-80f2cce75ccc</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <stepIdentifier>460598f7-f04c-4f0a-b00e-d6263422c1f3</stepIdentifier>
                <type>Wait</type>
            </botSteps>
            <description>CDCS-184 User has the opportunity to generate another statement if there was an error</description>
            <developerName>Continue_emailing_statement</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Continue emailing statement</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botDialogGroup>Email_statement</botDialogGroup>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>OSB_VA_EmailStatement_CTRL</invocationActionName>
                        <invocationActionType>apex</invocationActionType>
                        <invocationMappings>
                            <parameterName>statementStart</parameterName>
                            <type>Input</type>
                            <variableName>Statement_From_Date</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>accessToken</parameterName>
                            <type>Input</type>
                            <variableName>OCH_Access_Token</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>contactId</parameterName>
                            <type>Input</type>
                            <variableName>ContactId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>countryName</parameterName>
                            <type>Input</type>
                            <variableName>Country_name</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>timePeriod</parameterName>
                            <type>Input</type>
                            <variableName>Statement_Time_Period</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>caseId</parameterName>
                            <type>Input</type>
                            <variableName>Case_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>statementEnd</parameterName>
                            <type>Input</type>
                            <variableName>Statement_To_Date</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>serviceType</parameterName>
                            <type>Input</type>
                            <value>Email statement</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>accountNumber</parameterName>
                            <type>Input</type>
                            <variableName>Account_Number</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>userId</parameterName>
                            <type>Input</type>
                            <variableName>Visitor_User_Id</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>actionType</parameterName>
                            <type>Output</type>
                            <variableName>Flow_Action_Type</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>message</parameterName>
                            <type>Output</type>
                            <variableName>messageTest</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>hasError</parameterName>
                            <type>Output</type>
                            <variableName>Has_Message_Error</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorMessage</parameterName>
                            <type>Output</type>
                            <variableName>Error_Message</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorLogId</parameterName>
                            <type>Output</type>
                            <variableName>Error_Log_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>374fa1d6-603b-4976-a48e-ebdf59d07fea</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>6f0a4d3e-27b9-4ad3-bbb9-93bf71527a77</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Has_Message_Error</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>true</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botMessages>
                        <message>{!Error_Message}</message>
                        <messageIdentifier>96dafe4b-7b46-4867-89e1-b2075c488748</messageIdentifier>
                    </botMessages>
                    <stepIdentifier>29632619-37b8-407a-959d-19d646e61755</stepIdentifier>
                    <type>Message</type>
                </botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Statement_Case_update</targetBotDialog>
                        </botNavigationLinks>
                        <type>Call</type>
                    </botNavigation>
                    <stepIdentifier>b8bafc44-d205-477a-a8b4-b5c9c8e4aa61</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Continue_emailing_statement</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>bd587e1f-5d32-432e-afa7-5bcd549c98f8</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>57d966e9-c11d-4a48-8a79-c4b9f1dea681</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botMessages>
                    <message>{!messageTest}</message>
                    <messageIdentifier>5b4a53f8-ff36-4397-8599-5048937c5b67</messageIdentifier>
                </botMessages>
                <stepIdentifier>8f4386b3-61ad-4fd2-8c99-da9ffbe646df</stepIdentifier>
                <type>Message</type>
            </botSteps>
            <botSteps>
                <botNavigation>
                    <botNavigationLinks>
                        <targetBotDialog>Email_another_statement</targetBotDialog>
                    </botNavigationLinks>
                    <type>Redirect</type>
                </botNavigation>
                <stepIdentifier>d6dfeb3c-cd77-41d2-9b50-003fcaca65e7</stepIdentifier>
                <type>Navigation</type>
            </botSteps>
            <description>CDCS-184 call AWS statement generating API to generate statement</description>
            <developerName>Email_statement_for_selected_account</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Email statement for selected account</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botDialogGroup>Email_statement</botDialogGroup>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Selected_Account_Type</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>105c5694-ca16-4248-8ff5-4c0c6ba7bd03</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>2bd29247-3040-4634-93c3-23878d2255af</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Has_Message_Error</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>cf22692e-7987-4a6a-93fe-35a7e7abde27</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>638670d5-5fe6-408e-aeda-104dd57c7047</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Error_Type</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>140118d7-f6ed-46b8-8a5c-72442e1b7465</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>ff348813-48cb-4d97-9ce5-8f7734d96870</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Error_Message</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>53732903-7ec4-4c8c-bed3-2cac230d7479</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>c283060a-6299-4c16-9600-8607403c8742</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Flow_Action_Type</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>7c0ad0e8-8c09-4137-88c4-bbe1a98e8561</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>c76f5e93-a682-4963-8bdf-17caa6117aee</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>920792e1-9a39-43dd-9fe1-72a2831ce916</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Error_Log_Id</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>bafad27b-966e-44d4-9b83-2f354791c4f3</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>96206685-4dd9-4e61-ba03-7b0ed5c89939</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Customer_Identifier</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>c3781a69-8de3-466a-b1b7-901d2cc32767</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>b0f9e085-c165-4f97-a1e6-f8383f757352</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Selected_Account</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>f6a89745-ebc0-48a2-8fea-8a80002ec0b3</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>48e91ce0-ea8f-4b96-a297-630617fd0702</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Account_Number</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>fe6afaa4-f7c3-4bab-949f-aa7a69bd4e8e</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>74364b86-30dc-47fc-8fe8-425b0d3ca01e</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>da258471-4ff5-4765-aea3-c8d1f97da0a8</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>OSB_VA_Account_Types</invocationActionName>
                        <invocationActionType>flow</invocationActionType>
                        <invocationMappings>
                            <parameterName>CountryName</parameterName>
                            <type>Input</type>
                            <variableName>Country_name</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ServiceType</parameterName>
                            <type>Input</type>
                            <value>Email statement</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>AccountTypes</parameterName>
                            <type>Output</type>
                            <variableName>Account_Types</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>8e1e2238-1bf0-4288-a191-97da570c9d6b</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>a7c2e93e-6fe5-4fae-8184-a8766f176d57</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <askCollectIfSet>false</askCollectIfSet>
                    <autoSelectIfSingleChoice>false</autoSelectIfSingleChoice>
                    <botMessages>
                        <message>Choose your account type</message>
                        <messageIdentifier>057235c4-6d7e-484a-bd01-c1927ceac755</messageIdentifier>
                    </botMessages>
                    <botVariableOperands>
                        <disableAutoFill>true</disableAutoFill>
                        <sourceName>_Object</sourceName>
                        <sourceType>StandardMlSlotClass</sourceType>
                        <targetName>Selected_Account_Type</targetName>
                        <targetType>ConversationVariable</targetType>
                    </botVariableOperands>
                    <ignoreIntentRecognition>false</ignoreIntentRecognition>
                    <invalidInputBotNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Main_Menu_Authenticated</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </invalidInputBotNavigation>
                    <optionalCollect>false</optionalCollect>
                    <quickReplyOptionTemplate>{!Label}</quickReplyOptionTemplate>
                    <quickReplyType>Dynamic</quickReplyType>
                    <quickReplyWidgetType>Menu</quickReplyWidgetType>
                    <sourceVariableName>Account_Types</sourceVariableName>
                    <sourceVariableType>ConversationVariable</sourceVariableType>
                    <type>Collect</type>
                    <variableOperationIdentifier>8b88df5b-ba90-4a0c-a0ab-210631ba09e7</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>deaacf37-3589-41d0-a0c3-f9ab2952a503</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>OSB_VA_OCHAuthenticate_CTRL</invocationActionName>
                        <invocationActionType>apex</invocationActionType>
                        <invocationMappings>
                            <parameterName>contactId</parameterName>
                            <type>Input</type>
                            <variableName>ContactId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>serviceType</parameterName>
                            <type>Input</type>
                            <value>Email statement</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>countryName</parameterName>
                            <type>Input</type>
                            <variableName>Country_name</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>actionType</parameterName>
                            <type>Output</type>
                            <variableName>Flow_Action_Type</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>accessToken</parameterName>
                            <type>Output</type>
                            <variableName>OCH_Access_Token</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>refreshToken</parameterName>
                            <type>Output</type>
                            <variableName>OCH_Refresh_Token</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorLogId</parameterName>
                            <type>Output</type>
                            <variableName>Error_Log_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>accessTokenCreatedTime</parameterName>
                            <type>Output</type>
                            <variableName>OCH_Access_Token_Created_Time</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>hasError</parameterName>
                            <type>Output</type>
                            <variableName>Has_Message_Error</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorMessage</parameterName>
                            <type>Output</type>
                            <variableName>Error_Message</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>ddce0002-93af-4d97-aa31-a4957fe98781</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>425ed103-bff5-4cac-b870-1be052456458</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Has_Message_Error</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>true</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botMessages>
                        <message>{!Error_Message}</message>
                        <messageIdentifier>d7d54840-20c5-4b95-9108-c77dfc98df9b</messageIdentifier>
                    </botMessages>
                    <stepIdentifier>00d471e0-cfac-45bd-adad-e869e7730fd7</stepIdentifier>
                    <type>Message</type>
                </botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Statement_Case_update</targetBotDialog>
                        </botNavigationLinks>
                        <type>Call</type>
                    </botNavigation>
                    <stepIdentifier>65867aef-bd4b-44d9-96d3-bf23c9f6816d</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Continue_emailing_statement</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>2228051a-e9b1-4290-9819-4fb092ff6e2e</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>5bf34297-e657-470b-960a-48bdf206d841</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>OSB_VA_CustomerIdentifier_CTRL</invocationActionName>
                        <invocationActionType>apex</invocationActionType>
                        <invocationMappings>
                            <parameterName>accessToken</parameterName>
                            <type>Input</type>
                            <variableName>OCH_Access_Token</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>clientName</parameterName>
                            <type>Input</type>
                            <variableName>Client_Name</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>contactId</parameterName>
                            <type>Input</type>
                            <variableName>ContactId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>countryName</parameterName>
                            <type>Input</type>
                            <variableName>Country_name</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>serviceType</parameterName>
                            <type>Input</type>
                            <value>Email statement</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>actionType</parameterName>
                            <type>Output</type>
                            <variableName>Flow_Action_Type</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>customerIdentifier</parameterName>
                            <type>Output</type>
                            <variableName>Customer_Identifier</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorLogId</parameterName>
                            <type>Output</type>
                            <variableName>Error_Log_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorMessage</parameterName>
                            <type>Output</type>
                            <variableName>Error_Message</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorType</parameterName>
                            <type>Output</type>
                            <variableName>Error_Type</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>hasError</parameterName>
                            <type>Output</type>
                            <variableName>Has_Message_Error</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>e43697b7-4892-4ec9-88c7-17998270b840</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>8d0e8f77-541d-4a1d-b5f1-b8e72690a969</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Error_Message</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>IsSet</operatorType>
                </botStepConditions>
                <botSteps>
                    <botMessages>
                        <message>{!Error_Message}</message>
                        <messageIdentifier>8796520f-5e53-47d4-a392-821e0c51438c</messageIdentifier>
                    </botMessages>
                    <stepIdentifier>d2266b8c-1de0-42ba-9dec-067343db9687</stepIdentifier>
                    <type>Message</type>
                </botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Statement_Case_update</targetBotDialog>
                        </botNavigationLinks>
                        <type>Call</type>
                    </botNavigation>
                    <stepIdentifier>c35cee83-978f-4f5b-9725-516a7ede4887</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>c947bf4d-8fa7-4fef-81f1-6cb1dff68009</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Error_Type</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>HTTP_REQUEST_SEND_ERROR</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Continue_emailing_statement</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>b460c218-b7fb-44b3-9d50-715ac48851e4</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>53622f32-01e1-4db1-bebd-034ef416b7b8</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Error_Type</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>HTTP_RESPONSE_SEND_ERROR</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Continue_emailing_statement</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>16ca4cb9-074b-4717-8c2b-25421646a052</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>110460a5-baf7-4998-8088-fca925a9a4a5</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Has_Message_Error</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>true</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Continue_emailing_statement</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>efdbcbe1-b153-416d-a586-40b10ca06d40</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>92581fb0-66a0-4b28-8aea-bb6fa7d22511</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Error_Type</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>NO_ACCOUNT_FOUND_ERROR</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Email_another_statement</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>ec51cbaa-1a44-414a-9c04-657be035cd07</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>ae17007d-d2c7-4599-9c00-3906f1088a92</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>OSB_VA_AccountSearch_CTRL</invocationActionName>
                        <invocationActionType>apex</invocationActionType>
                        <invocationMappings>
                            <parameterName>serviceType</parameterName>
                            <type>Input</type>
                            <value>Email statement</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>countryName</parameterName>
                            <type>Input</type>
                            <variableName>Country_name</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>accountType</parameterName>
                            <type>Input</type>
                            <variableName>Selected_Account_Type</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>customerIdentifier</parameterName>
                            <type>Input</type>
                            <variableName>Customer_Identifier</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>accessToken</parameterName>
                            <type>Input</type>
                            <variableName>OCH_Access_Token</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>contactId</parameterName>
                            <type>Input</type>
                            <variableName>ContactId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>hasError</parameterName>
                            <type>Output</type>
                            <variableName>Has_Message_Error</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>actionType</parameterName>
                            <type>Output</type>
                            <variableName>Flow_Action_Type</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorLogId</parameterName>
                            <type>Output</type>
                            <variableName>Error_Log_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorType</parameterName>
                            <type>Output</type>
                            <variableName>Error_Type</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>accounts</parameterName>
                            <type>Output</type>
                            <variableName>Accounts_List</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorOutPutMessage</parameterName>
                            <type>Output</type>
                            <variableName>Error_Message</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>ed76726b-430c-42ad-8403-e8888b1ab1fc</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>7d1d1eb2-9756-4c91-90dd-48e592ba604f</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Error_Message</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>IsSet</operatorType>
                </botStepConditions>
                <botSteps>
                    <botMessages>
                        <message>{!Error_Message}</message>
                        <messageIdentifier>4d85edd0-61dc-46e4-9759-93f5ad4c031b</messageIdentifier>
                    </botMessages>
                    <stepIdentifier>e5ef9e04-5431-4a5a-b6d0-96df96bc6f40</stepIdentifier>
                    <type>Message</type>
                </botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Statement_Case_update</targetBotDialog>
                        </botNavigationLinks>
                        <type>Call</type>
                    </botNavigation>
                    <stepIdentifier>87e05416-2889-47c4-9153-3e4627af4568</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>9ca25f58-36b3-47d3-95c6-d364d1cf17a8</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Error_Type</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>HTTP_REQUEST_SEND_ERROR</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Continue_emailing_statement</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>2d382f24-3253-42f7-8e08-708307a1e191</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>da8a4c92-6743-479c-bc8b-92005796bee0</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Error_Type</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>NO_ACCOUNTS_FOUND_ERROR</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Email_another_statement</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>c802ece7-7c06-4ba4-b39b-2b26f30dcd27</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>8dc40a3f-153d-4dc6-8ffd-9930c6b168e0</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Has_Message_Error</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>true</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Email_another_statement</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>89a09724-3be9-46da-9ec6-29a88756918e</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>e62ed0df-0e65-4671-90d0-8b0f0c63373b</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <askCollectIfSet>false</askCollectIfSet>
                    <autoSelectIfSingleChoice>false</autoSelectIfSingleChoice>
                    <botMessages>
                        <message>The account numbers that I have found with your inputs are</message>
                        <messageIdentifier>97b79719-ec23-4606-99c7-282b12b55074</messageIdentifier>
                    </botMessages>
                    <botVariableOperands>
                        <disableAutoFill>true</disableAutoFill>
                        <sourceName>_Object</sourceName>
                        <sourceType>StandardMlSlotClass</sourceType>
                        <targetName>Selected_Account</targetName>
                        <targetType>ConversationVariable</targetType>
                    </botVariableOperands>
                    <ignoreIntentRecognition>false</ignoreIntentRecognition>
                    <invalidInputBotNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Main_Menu_Authenticated</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </invalidInputBotNavigation>
                    <optionalCollect>false</optionalCollect>
                    <quickReplyOptionTemplate>{!outputText}</quickReplyOptionTemplate>
                    <quickReplyType>Dynamic</quickReplyType>
                    <quickReplyWidgetType>Menu</quickReplyWidgetType>
                    <sourceVariableName>Accounts_List</sourceVariableName>
                    <sourceVariableType>ConversationVariable</sourceVariableType>
                    <type>Collect</type>
                    <variableOperationIdentifier>dd62cd09-cf69-4248-90a9-3d20af17cc23</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>ebcee546-582b-4335-a447-882041c5be3d</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>OSB_VA_Get_Account_Number</invocationActionName>
                        <invocationActionType>flow</invocationActionType>
                        <invocationMappings>
                            <parameterName>AccountInfo</parameterName>
                            <type>Input</type>
                            <variableName>Selected_Account</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>AccountNumber</parameterName>
                            <type>Output</type>
                            <variableName>Account_Number</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>2eb33ea0-f065-46d6-b787-5901270d0186</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>a73c15c3-c045-4422-be26-45c528a0a324</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Account_Number</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>IsNotSet</operatorType>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Statement_get_clients</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>ed0a02ce-5cf3-42c1-937a-aa68b1801881</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>d585b45e-e9f6-441e-b5f0-e1f72652f412</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botNavigation>
                    <botNavigationLinks>
                        <targetBotDialog>Select_time_period</targetBotDialog>
                    </botNavigationLinks>
                    <type>Redirect</type>
                </botNavigation>
                <stepIdentifier>ebe6d1cd-e630-4237-9eed-4d734dea0779</stepIdentifier>
                <type>Navigation</type>
            </botSteps>
            <description>CDCS-185 - dialog for searching for the account number</description>
            <developerName>Account_search_statement</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Account search - statement</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botDialogGroup>Email_statement</botDialogGroup>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>Create_Case_From_Chat_Bot</invocationActionName>
                        <invocationActionType>flow</invocationActionType>
                        <invocationMappings>
                            <parameterName>ActionType</parameterName>
                            <type>Input</type>
                            <variableName>Flow_Action_Type</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>CaseId</parameterName>
                            <type>Input</type>
                            <variableName>Case_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ErrorLogId</parameterName>
                            <type>Input</type>
                            <variableName>Error_Log_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>CountryName</parameterName>
                            <type>Input</type>
                            <variableName>Country_name</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ServiceType</parameterName>
                            <type>Input</type>
                            <value>Email statement</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ChatTranscriptId</parameterName>
                            <type>Input</type>
                            <variableName>RoutableId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>ContactId</parameterName>
                            <type>Input</type>
                            <variableName>ContactId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>CaseId</parameterName>
                            <type>Output</type>
                            <variableName>Case_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>9f4c11d3-6a62-4a77-b192-0ebbc5e05cbc</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>c61fe8f8-3781-4b86-9b49-e848f4738773</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Flow_Action_Type</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>6683d97f-bfe9-4f53-af0c-25c3f20037a7</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>93faf556-7da8-4829-ac1b-b436bf17e61f</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Error_Log_Id</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>1a35330b-2e69-46c9-b428-d41ddf284bbb</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>baab1163-6ac8-41c4-a1c1-abc05387a884</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Case_Id</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>dad1fad6-e90a-49bd-8e2d-2dd2f05dd164</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>80041223-b765-42f0-becc-a73524b05c02</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>8e5138db-8a3f-4833-8bb4-77a43dc6f977</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <stepIdentifier>daf9cb5b-b7d0-4125-b7d4-3098bdf27bdf</stepIdentifier>
                <type>Wait</type>
            </botSteps>
            <developerName>Statement_Case_update</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Statement Case update</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botDialogGroup>Email_statement</botDialogGroup>
            <botSteps>
                <botSteps>
                    <botVariableOperation>
                        <botVariableOperands>
                            <targetName>Client_Name</targetName>
                            <targetType>ConversationVariable</targetType>
                        </botVariableOperands>
                        <type>Unset</type>
                        <variableOperationIdentifier>bfb0bb28-3f88-4274-a0e9-982a0cd47aa9</variableOperationIdentifier>
                    </botVariableOperation>
                    <stepIdentifier>368b125b-a2e7-4daf-a40d-0de282871d72</stepIdentifier>
                    <type>VariableOperation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>021f231d-cf2b-447b-ad2a-e200dfa281d5</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>OSB_VA_AvailableClients</invocationActionName>
                        <invocationActionType>apex</invocationActionType>
                        <invocationMappings>
                            <parameterName>countryName</parameterName>
                            <type>Input</type>
                            <variableName>Country_name</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>contactId</parameterName>
                            <type>Input</type>
                            <variableName>ContactId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>serviceType</parameterName>
                            <type>Input</type>
                            <value>Email statement</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>output</parameterName>
                            <type>Output</type>
                            <variableName>Client_Names</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>cf8a64a0-e220-4adc-9abd-04c66b241b75</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>46637f38-2c93-49e0-bb3f-0b7c2db8fa23</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <askCollectIfSet>false</askCollectIfSet>
                    <autoSelectIfSingleChoice>false</autoSelectIfSingleChoice>
                    <botMessages>
                        <message>Choose your company name</message>
                        <messageIdentifier>179f7e58-d23f-4efa-b131-8d521e2a837a</messageIdentifier>
                    </botMessages>
                    <botVariableOperands>
                        <disableAutoFill>true</disableAutoFill>
                        <sourceName>_Text</sourceName>
                        <sourceType>StandardMlSlotClass</sourceType>
                        <targetName>Client_Name</targetName>
                        <targetType>ConversationVariable</targetType>
                    </botVariableOperands>
                    <ignoreIntentRecognition>false</ignoreIntentRecognition>
                    <invalidInputBotNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Main_Menu_Authenticated</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </invalidInputBotNavigation>
                    <optionalCollect>false</optionalCollect>
                    <quickReplyType>Dynamic</quickReplyType>
                    <quickReplyWidgetType>Menu</quickReplyWidgetType>
                    <sourceVariableName>Client_Names</sourceVariableName>
                    <sourceVariableType>ConversationVariable</sourceVariableType>
                    <type>Collect</type>
                    <variableOperationIdentifier>074ae5d2-5559-4885-aede-9018a14c3e64</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>d1d9ee36-35d1-4c08-8e97-1b4a6777f6fc</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botNavigation>
                    <botNavigationLinks>
                        <targetBotDialog>Account_search_statement</targetBotDialog>
                    </botNavigationLinks>
                    <type>Redirect</type>
                </botNavigation>
                <stepIdentifier>d9abeb57-e331-4a22-9182-dca1248cd89e</stepIdentifier>
                <type>Navigation</type>
            </botSteps>
            <developerName>Statement_get_clients</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Statement get clients</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botSteps>
                <botMessages>
                    <message>We are experiencing technical difficulties at the moment. Please try again later and if the problem still persists, please contact Client Services.</message>
                    <messageIdentifier>a224d4f2-9ae4-45d6-a5c7-d863d2878e6e</messageIdentifier>
                </botMessages>
                <stepIdentifier>cdb9dcb4-46bd-4399-a6c4-534351f3ffb3</stepIdentifier>
                <type>Message</type>
            </botSteps>
            <botSteps>
                <botNavigation>
                    <botNavigationLinks>
                        <targetBotDialog>Is_there_anything_else_I_can_help_you_with</targetBotDialog>
                    </botNavigationLinks>
                    <type>Redirect</type>
                </botNavigation>
                <stepIdentifier>2d5c5d8f-e777-4e4b-bf32-787d86c35814</stepIdentifier>
                <type>Navigation</type>
            </botSteps>
            <developerName>Error_Dialog</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Error Dialog</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botDialogGroup>Email_statement</botDialogGroup>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>OSB_VA_OCHAuthenticate_CTRL</invocationActionName>
                        <invocationActionType>apex</invocationActionType>
                        <invocationMappings>
                            <parameterName>contactId</parameterName>
                            <type>Input</type>
                            <variableName>ContactId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>serviceType</parameterName>
                            <type>Input</type>
                            <value>Email statement</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>countryName</parameterName>
                            <type>Input</type>
                            <variableName>Country_name</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>actionType</parameterName>
                            <type>Output</type>
                            <variableName>Flow_Action_Type</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>accessToken</parameterName>
                            <type>Output</type>
                            <variableName>OCH_Access_Token</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>refreshToken</parameterName>
                            <type>Output</type>
                            <variableName>OCH_Refresh_Token</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorLogId</parameterName>
                            <type>Output</type>
                            <variableName>Error_Log_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>accessTokenCreatedTime</parameterName>
                            <type>Output</type>
                            <variableName>OCH_Access_Token_Created_Time</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>hasError</parameterName>
                            <type>Output</type>
                            <variableName>OCH_Has_Error</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorMessage</parameterName>
                            <type>Output</type>
                            <variableName>Error_Message</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>0ae82ee6-041c-453f-a508-c5af923c3213</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>22cd5f3b-d480-4a09-9f69-9d4a6a2b8fa4</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>OCH_Has_Error</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>true</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botMessages>
                        <message>{!Error_Message}</message>
                        <messageIdentifier>a379ef2a-95b7-4640-a56e-e8057a68e70a</messageIdentifier>
                    </botMessages>
                    <stepIdentifier>d41ae93e-c4a2-423d-83b2-c17afac0d868</stepIdentifier>
                    <type>Message</type>
                </botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Statement_Case_update</targetBotDialog>
                        </botNavigationLinks>
                        <type>Call</type>
                    </botNavigation>
                    <stepIdentifier>d067c274-89ab-4314-90f9-ba68ef353a3d</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Continue_emailing_statement</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>f32fcb70-1da8-4061-bb74-c5c1beb19069</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>292a6df9-60a3-4237-95db-78276996b7a1</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>OSB_VA_OCHInquiryStatement_CTRL</invocationActionName>
                        <invocationActionType>apex</invocationActionType>
                        <invocationMappings>
                            <parameterName>statementStart</parameterName>
                            <type>Input</type>
                            <variableName>Statement_From_Date</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>timePeriod</parameterName>
                            <type>Input</type>
                            <variableName>Statement_Time_Period</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>accessToken</parameterName>
                            <type>Input</type>
                            <variableName>OCH_Access_Token</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>countryName</parameterName>
                            <type>Input</type>
                            <variableName>Country_name</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>caseId</parameterName>
                            <type>Input</type>
                            <variableName>Case_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>accountNumber</parameterName>
                            <type>Input</type>
                            <variableName>Account_Number</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>statementEnd</parameterName>
                            <type>Input</type>
                            <variableName>Statement_To_Date</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>contactId</parameterName>
                            <type>Input</type>
                            <variableName>ContactId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>userId</parameterName>
                            <type>Input</type>
                            <variableName>EndUserId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>serviceType</parameterName>
                            <type>Input</type>
                            <value>Email statement</value>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>actionType</parameterName>
                            <type>Output</type>
                            <variableName>Flow_Action_Type</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorMessage</parameterName>
                            <type>Output</type>
                            <variableName>Error_Message</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>hasError</parameterName>
                            <type>Output</type>
                            <variableName>OCH_Has_Error</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>noOfTransactions</parameterName>
                            <type>Output</type>
                            <variableName>Number_of_transactions</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>message</parameterName>
                            <type>Output</type>
                            <variableName>messageTest</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>errorLogId</parameterName>
                            <type>Output</type>
                            <variableName>Error_Log_Id</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>4a4e5278-a61f-452e-8c82-c946c1c8be3c</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>0e107ebf-767c-49e4-8508-f765be074560</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Number_of_transactions</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>0</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botMessages>
                        <message>{!Error_Message}</message>
                        <messageIdentifier>475429e8-1154-4526-b208-d267badf7485</messageIdentifier>
                    </botMessages>
                    <stepIdentifier>4519b808-e3f9-4e9a-8d80-acaaf850e1c7</stepIdentifier>
                    <type>Message</type>
                </botSteps>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Statement_Case_update</targetBotDialog>
                        </botNavigationLinks>
                        <type>Call</type>
                    </botNavigation>
                    <stepIdentifier>b1f68939-2040-490b-9145-be84c4e3c4d7</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>f3507118-8852-4205-88f5-f2d55372f9a8</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Error_Type</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>NO_ACCOUNTS_FOUND_ERROR</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Email_another_statement</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>9950e2bf-1e83-4d9e-aba4-b7ff6975eeb6</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>095319e4-12a5-444b-9fd6-24c3335cbf4e</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>OCH_Has_Error</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>true</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Continue_emailing_statement</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>6af4d9ab-6f3d-45be-bbfa-973281b31428</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>98a52eb2-f741-4b01-97e0-e810c63e9acc</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>Number_of_transactions</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>GreaterThanOrEqualTo</operatorType>
                    <rightOperandValue>1</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Email_statement_for_selected_account</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>07b85e07-d5d2-4437-af09-576016b6bcb2</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>2273bad6-db05-4d6c-ae8a-554407b36d81</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botNavigation>
                    <botNavigationLinks>
                        <targetBotDialog>Email_another_statement</targetBotDialog>
                    </botNavigationLinks>
                    <type>Redirect</type>
                </botNavigation>
                <stepIdentifier>cfb7ae22-addc-4616-bc30-4cbabbf007af</stepIdentifier>
                <type>Navigation</type>
            </botSteps>
            <developerName>Inquiry_email_statement</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Inquiry email statement</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <botDialogs>
            <botSteps>
                <botVariableOperation>
                    <botInvocation>
                        <invocationActionName>OSB_VA_CountryName_CTRL</invocationActionName>
                        <invocationActionType>apex</invocationActionType>
                        <invocationMappings>
                            <parameterName>contactId</parameterName>
                            <type>Input</type>
                            <variableName>ContactId</variableName>
                            <variableType>ContextVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>oneCountryActive</parameterName>
                            <type>Output</type>
                            <variableName>One_Country_Active</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>countryName</parameterName>
                            <type>Output</type>
                            <variableName>Country_name</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                        <invocationMappings>
                            <parameterName>availableCountryList</parameterName>
                            <type>Output</type>
                            <variableName>Available_Country_List</variableName>
                            <variableType>ConversationVariable</variableType>
                        </invocationMappings>
                    </botInvocation>
                    <type>Set</type>
                    <variableOperationIdentifier>59fec7e2-f322-4787-9a87-d075e11eee14</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>801aab88-d222-45ba-ae6e-91bd6aca20d1</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botStepConditions>
                    <leftOperandName>One_Country_Active</leftOperandName>
                    <leftOperandType>ConversationVariable</leftOperandType>
                    <operatorType>Equals</operatorType>
                    <rightOperandValue>true</rightOperandValue>
                </botStepConditions>
                <botSteps>
                    <botNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Access_enhanced_virtual_assistant_services</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </botNavigation>
                    <stepIdentifier>76dbde83-1240-4c37-8760-30c4f530220e</stepIdentifier>
                    <type>Navigation</type>
                </botSteps>
                <conditionLogicType>And</conditionLogicType>
                <stepIdentifier>bbcd6803-ea83-4299-bed8-9648de6476cf</stepIdentifier>
                <type>Group</type>
            </botSteps>
            <botSteps>
                <botVariableOperation>
                    <askCollectIfSet>true</askCollectIfSet>
                    <autoSelectIfSingleChoice>false</autoSelectIfSingleChoice>
                    <botMessages>
                        <message>Choose your country</message>
                        <messageIdentifier>04363622-8fba-45af-87e2-57913f0fb85e</messageIdentifier>
                    </botMessages>
                    <botVariableOperands>
                        <disableAutoFill>true</disableAutoFill>
                        <sourceName>_Text</sourceName>
                        <sourceType>StandardMlSlotClass</sourceType>
                        <targetName>Country_name</targetName>
                        <targetType>ConversationVariable</targetType>
                    </botVariableOperands>
                    <ignoreIntentRecognition>false</ignoreIntentRecognition>
                    <invalidInputBotNavigation>
                        <botNavigationLinks>
                            <targetBotDialog>Main_Menu_Authenticated</targetBotDialog>
                        </botNavigationLinks>
                        <type>Redirect</type>
                    </invalidInputBotNavigation>
                    <optionalCollect>false</optionalCollect>
                    <quickReplyType>Dynamic</quickReplyType>
                    <quickReplyWidgetType>Menu</quickReplyWidgetType>
                    <sourceVariableName>Available_Country_List</sourceVariableName>
                    <sourceVariableType>ConversationVariable</sourceVariableType>
                    <type>Collect</type>
                    <variableOperationIdentifier>df55ec8a-21c3-48db-9f6d-bc4eef96b074</variableOperationIdentifier>
                </botVariableOperation>
                <stepIdentifier>4de3dc53-d3f1-4039-9e74-7c511dab35b2</stepIdentifier>
                <type>VariableOperation</type>
            </botSteps>
            <botSteps>
                <botNavigation>
                    <botNavigationLinks>
                        <targetBotDialog>Access_enhanced_virtual_assistant_services</targetBotDialog>
                    </botNavigationLinks>
                    <type>Redirect</type>
                </botNavigation>
                <stepIdentifier>98c55951-1433-48cb-a555-663ca6402a86</stepIdentifier>
                <type>Navigation</type>
            </botSteps>
            <developerName>Choose_country</developerName>
            <isPlaceholderDialog>false</isPlaceholderDialog>
            <label>Choose country</label>
            <showInFooterMenu>false</showInFooterMenu>
        </botDialogs>
        <citationsEnabled>false</citationsEnabled>
        <conversationSystemDialogs>
            <dialog>Error_Dialog</dialog>
            <type>ErrorHandling</type>
        </conversationSystemDialogs>
        <conversationVariables>
            <collectionType>List</collectionType>
            <dataType>Object</dataType>
            <developerName>Accounts_List</developerName>
            <label>Accounts List</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Text</dataType>
            <developerName>Account_Name</developerName>
            <label>Account Name</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Text</dataType>
            <developerName>Account_Number</developerName>
            <label>Account Number</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Text</dataType>
            <developerName>Account_Short_Name</developerName>
            <label>Account Short Name</label>
        </conversationVariables>
        <conversationVariables>
            <collectionType>List</collectionType>
            <dataType>Object</dataType>
            <developerName>Account_Types</developerName>
            <label>Account Types</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Boolean</dataType>
            <developerName>Are_Dates_Valid</developerName>
            <label>Are Dates Valid</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Text</dataType>
            <developerName>Authenticated_Contact</developerName>
            <label>Authenticated Contact</label>
        </conversationVariables>
        <conversationVariables>
            <collectionType>List</collectionType>
            <dataType>Text</dataType>
            <developerName>Available_Country_List</developerName>
            <label>Available Country List</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Text</dataType>
            <developerName>Balance_Details</developerName>
            <label>Balance Details</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Text</dataType>
            <developerName>Case_Id</developerName>
            <label>Case Id</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Text</dataType>
            <developerName>Client_Name</developerName>
            <label>Client Name</label>
        </conversationVariables>
        <conversationVariables>
            <collectionType>List</collectionType>
            <dataType>Text</dataType>
            <developerName>Client_Names</developerName>
            <label>Client Names</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Text</dataType>
            <developerName>Community_URL</developerName>
            <label>Community URL</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Text</dataType>
            <developerName>Country_name</developerName>
            <label>Country name</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Text</dataType>
            <developerName>Customer_Identifier</developerName>
            <label>Customer Identifier</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Text</dataType>
            <developerName>Dates_Validation_Message</developerName>
            <label>Dates Validation Message</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Text</dataType>
            <developerName>DCS_Service_Type</developerName>
            <label>DCS Service Type</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Text</dataType>
            <developerName>Error_Log_Id</developerName>
            <label>Error Log Id</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Text</dataType>
            <developerName>Error_Message</developerName>
            <label>Error Message</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Boolean</dataType>
            <developerName>Error_on_Request</developerName>
            <label>Error on Request</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Text</dataType>
            <developerName>Error_Type</developerName>
            <label>Error Type</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Text</dataType>
            <developerName>Flow_Action_Type</developerName>
            <label>Flow Action Type</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Boolean</dataType>
            <developerName>Has_AccessTo_Payment_Tracking</developerName>
            <label>Has AccessTo Payment Tracking</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Boolean</dataType>
            <developerName>Has_Access_To_Balance_Inquiry</developerName>
            <label>Has Access To Balance Inquiry</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Boolean</dataType>
            <developerName>Has_Access_To_Email_Statement</developerName>
            <label>Has Access To Email Statement</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Boolean</dataType>
            <developerName>Has_Email_Statement_Permission_On_Account</developerName>
            <label>Has Email Statement Permission On Account</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Boolean</dataType>
            <developerName>Has_Financial_Account</developerName>
            <label>Has Financial Account</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Boolean</dataType>
            <developerName>Has_Message_Error</developerName>
            <label>Has Message Error</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Boolean</dataType>
            <developerName>Has_Payment_Tracking_Permission_on_Account</developerName>
            <label>Has Payment Tracking Permission on Account</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Boolean</dataType>
            <developerName>Has_Permission_for_Transaction_Reference</developerName>
            <label>Has Permission for Transaction Reference</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Boolean</dataType>
            <developerName>Is_Account_Number_Valid</developerName>
            <label>Is Account Number Valid</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Boolean</dataType>
            <developerName>Is_Enhanced_Bot_Enabled</developerName>
            <label>Is Enhanced Bot Enabled</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Boolean</dataType>
            <developerName>Is_Token_Correct</developerName>
            <label>Is Token Correct</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Boolean</dataType>
            <developerName>Is_Transaction_Found</developerName>
            <label>Is Transaction Found</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Boolean</dataType>
            <developerName>Is_Transaction_Ref_Valid</developerName>
            <label>Is Transaction Ref Valid</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Boolean</dataType>
            <developerName>Is_Transaction_Selected</developerName>
            <label>Is Transaction Selected</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Text</dataType>
            <developerName>Job_Title</developerName>
            <label>Job Title</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Text</dataType>
            <developerName>Max_Amount</developerName>
            <label>Max Amount</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Text</dataType>
            <developerName>messageTest</developerName>
            <label>messageTest</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Text</dataType>
            <developerName>Min_Amount</developerName>
            <label>Min Amount</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Number</dataType>
            <developerName>Number_of_transactions</developerName>
            <label>Number of transactions</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Text</dataType>
            <developerName>OCH_Access_Token</developerName>
            <label>OCH Access Token</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Text</dataType>
            <developerName>OCH_Access_Token_Created_Time</developerName>
            <label>OCH Access Token Created Time</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Text</dataType>
            <developerName>OCH_Error_Message</developerName>
            <label>OCH Error Message</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Text</dataType>
            <developerName>OCH_Error_Type</developerName>
            <label>OCH Error Type</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Boolean</dataType>
            <developerName>OCH_Has_Error</developerName>
            <label>OCH Has Error</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Text</dataType>
            <developerName>OCH_Refresh_Token</developerName>
            <label>OCH Refresh Token</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Boolean</dataType>
            <developerName>One_Country_Active</developerName>
            <label>One Country Active</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Number</dataType>
            <developerName>Payment_Amount</developerName>
            <label>Payment Amount</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Text</dataType>
            <developerName>Payment_Currency</developerName>
            <label>Payment Currency</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Text</dataType>
            <developerName>Payment_Details</developerName>
            <label>Payment Details</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Text</dataType>
            <developerName>Payment_State</developerName>
            <label>Payment State</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Object</dataType>
            <developerName>Selected_Account</developerName>
            <label>Selected Account</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Object</dataType>
            <developerName>Selected_Account_Type</developerName>
            <label>Selected Account Type</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Text</dataType>
            <developerName>Selected_Accpunt_Number</developerName>
            <label>Selected Account Number</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Object</dataType>
            <developerName>Selected_Transaction</developerName>
            <label>Selected Transaction</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Text</dataType>
            <developerName>Statement_Details</developerName>
            <label>Statement Details</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Date</dataType>
            <developerName>Statement_From_Date</developerName>
            <label>Statement From Date</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Text</dataType>
            <developerName>Statement_Time_Period</developerName>
            <label>Statement Time Period</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Date</dataType>
            <developerName>Statement_To_Date</developerName>
            <label>Statement To Date</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Text</dataType>
            <developerName>Sub_State</developerName>
            <label>Sub State</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Number</dataType>
            <developerName>Test_Name</developerName>
            <label>Test Name</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Date</dataType>
            <developerName>Transaction_Date</developerName>
            <label>Transaction Date</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Text</dataType>
            <developerName>Transaction_Reference</developerName>
            <label>Transaction Reference</label>
        </conversationVariables>
        <conversationVariables>
            <collectionType>List</collectionType>
            <dataType>Object</dataType>
            <developerName>Transaction_Search_Result</developerName>
            <label>Transaction Search Result</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Text</dataType>
            <developerName>UETR</developerName>
            <label>UETR</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Text</dataType>
            <developerName>User_Input</developerName>
            <label>User Input</label>
        </conversationVariables>
        <conversationVariables>
            <dataType>Text</dataType>
            <developerName>VisitorEmail</developerName>
            <label>Visitor Email</label>
        </conversationVariables>
        <entryDialog>Welcome</entryDialog>
        <intentDisambiguationEnabled>false</intentDisambiguationEnabled>
        <intentV3Enabled>false</intentV3Enabled>
        <knowledgeActionEnabled>false</knowledgeActionEnabled>
        <knowledgeFallbackEnabled>false</knowledgeFallbackEnabled>
        <mainMenuDialog>Main_Menu</mainMenuDialog>
        <nlpProviders>
            <language>en_US</language>
            <nlpProviderType>EinsteinAi</nlpProviderType>
        </nlpProviders>
        <smallTalkEnabled>false</smallTalkEnabled>
    </botVersions>
</Bot>
